Source: qlcplus
Section: misc
Priority: optional
Maintainer: <PERSON><PERSON> <<EMAIL>>
Build-Depends:
 debhelper (>= 7.0.50),
 g++,
 libasound2-dev (>= 1.0.16),
 libfftw3-dev,
 libftdi1-dev (>= 0.17),
 libsndfile1-dev,
 qtbase5-dev, qtscript5-dev,
 qtmultimedia5-dev, libqt5serialport5-dev,
 libqt5websockets5-dev,
 libstdc++-dev,
 libudev-dev,
 libusb-1.0-0-dev (>= 2:0.1.12),
 make,
 pkg-config,
 libxml2-utils,
 shared-mime-info (>= 0.71),
Standards-Version: 3.7.3
Homepage: http://www.qlcplus.org/

Package: qlcplus
Section: misc
Architecture: any
Depends:
 ${shlibs:Depends},
 ${misc:Depends},
Recommends:
 libsndfile1,
 libqt5multimedia5-plugins,
Description: Q Light Controller Plus - The open DMX lighting desk
 Software lighting desk for controlling professional lighting fixtures.

Package: qlcplus-dbg
Architecture: any
Section: debug
Priority: extra
Depends:
 qlcplus (= ${binary:Version}),
 ${misc:Depends},
Description: Debugging symbols for Q Light Controller Plus
 Q Light Controller Plus debugging symbols.
