qlcplus (4.11.2) stable; urgency=low

  * engine: fix crash caused by an invalid IO mapping
  * engine: fix intensity override not considered during fade outs
  * UI/Function Manager: fixed keyboard shortcut conflicts and document them
  * UI/Function Manager: allow to import multiple of audio/video files at once
  * UI/Channel Groups: added expand/collapse all button helpers
  * UI/Chaser Editor: add a button to shuffle the selected Chaser steps (thanks to <PERSON>)
  * UI/RGBMatrix Editor: fix preview not updating on pattern change when play button is on
  * Show Manager: fix crash when adding a Sequence after deleting one
  * Show Manager: fix crash when editing a Sequence bound to a deleted Scene
  * Show Manager: fix items start time indication when dragging
  * Virtual Console/Slider: fix submaster initial value not applied and inverted mode
  * Virtual Console/Slider: added 'value catching' option for external controller faders (<PERSON><PERSON> proposal)
  * Virtual Console/Slider: fix values range when switching between Slider and Knob appearance
  * Virtual Console/Slider: react on Scene flashing when in playback mode
  * Virtual Console/Knob: fix DMX values not updated when interacting with the mouse wheel
  * Virtual Console/Cue List: allow to select a step with next/previous buttons during pause
  * Virtual Console/Cue List: go to the right chaser step after a pause (thanks to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
  * Virtual Console/Frame: fix regression preventing to send the disable feedback
  * Web Access: added support for VC Buttons in Flash mode (Sylvain Laugié)
  * Web Access: added support for VC Frame circular page scrolling (Sylvain Laugié)
  * Web Access: update AudioTriggers state when changed from QLC+ (Sylvain Laugié)
  * plugins/udmx: added 'channels' configuration parameter (see documentation)
  * plugins/E1.31: fix crash on wrong packet length (David Garyga)
  * New fixture: DTS XR7 Spot (thanks to Nicolò Zanon)
  * New fixture: Ledj Slimline 12Q5 Batten (thanks to Dean Clough)
  * New fixture: Ayra Compar Kit 1 (thanks to eigenaardiger)
  * New fixtures: GLP Impression X4 S, Eurolite LED KLS-2500 (thanks to Mitsch)
  * New fixture: Chauvet Intimidator Spot 255 IRC (thanks to Ham Sadler)
  * New fixtures: Chauvet Geyser RGB, Geyser P6 (thanks to Andrew)
  * New fixture: Chauvet Rotosphere Q3 (thanks to Eric Sherlock)
  * New fixture: Showtec Compact Par 7 Q4 (thanks to Alexander)
  * New fixture: Contest Delirium (thanks to Vincent)
  * New fixture: Solena Mini Par 12, Max Bar 28 RGB (thanks to Nathan Durnan)
  * New fixtures: Showtec Phantom 65, Laserworld PRO-800RGB (thanks to Piotr Nowik)
  * New fixture: Chauvet MiN Spot RGBW (thanks to Jungle Jim)
  * New fixtures: Showtec Shark Wash One, American DJ Vizi Hex Wash7 (thanks to Georg Müller)
  * New fixture: Showtec Shark Beam FX One (thanks to Mats Lourenco)
  * New fixture: Stairville novaWash Quad LED (thanks to Luke Bonett)
  * New fixtures: Eurolite Party TCL Spot RGB, Expolite TourSpot 60, Expolite TourStick 72 RGBWA (thanks to Dirk J)
  * New fixture: Chauvet Hemisphere 5.1, Trident, Scorpion Storm RGX (thanks to Francois Blanchette)
  * New fixture: Briteq COB Slim 100-RGB (thanks to Thierry)
  * New fixture: American DJ UB 12H (thanks to Jason R Johnston)
  * New fixture: American DJ Mega Hex Par (thanks to Ben C)
  * New fixture: Cameo Q SPOT 15 RGBW (thanks to Antoine Houbron)
  * New fixture: lightmaXX Platinum Line Flat Par COB (thanks to Leonardo)
  * New fixture: Stairville LED Blinder 2 COB 2x65W (thanks to chritoep)
  * New fixture: lightmaXX LED PAR 64 (thanks to Johannes Felber)
  * New fixture: Cameo Thunder Wash Series (thanks to JP)
  * New fixtures: Briteq BT 575S, Stairville MH-x30 LED Beam (thanks to Andres Robles)
  * New fixture: beamZ LED FlatPAR-154 (thanks to Jászberényi Szabolcs)
  * New fixtures: Eurolite THA-100F COB, Cameo Tribar 200 IR (thanks to David Morgenschweis)
  * New fixture: beamZ BT310 LED FlatPAR 12x8W 4-1 DMX IR (thanks to Mark)
  * New fixture: Fun Generation PicoWash 40 Pixel Quad LED (thanks to Harm Aldick)
  * New fixtures: American DJ Entourage, Elumen8 MS-700PE, Ibiza PAR LED 712IR (thanks to Tim Cullingworth)
  * New fixtures: Martin MAC 401 Dual RGB Zoom, MAC 401 Dual CT Zoom, Stairville MH-z720 (thanks to Tim Cullingworth)
  * New fixture: Fun Generation SePar Quad UV (thanks to Helmet)

 -- Massimo Callegari <<EMAIL>>  Thu, 19 Apr 2018 20:21:22 +0200

qlcplus (4.11.1) stable; urgency=low

  * engine: fixed audio files detection by prioritizing sndfile over mad
  * engine: fixed HTP/LTP forced channels not set correctly
  * engine: keep track of input/output device lines even if they are disconnected
  * engine/Script: add blackout:on and blackout:off commands (Jano Svitok)
  * engine/Script: do not keep empty trailing lines when saving a workspace
  * UI: it is now possible to detach a QLC+ context tab on a separate window by double clicking on it
  * UI/RGB Panel: added RBG pixel type (thanks to Peter Marks)
  * UI/Remap: fixed RGB Panels remapping
  * UI/Input Output Manager: added a button to enable/disable USB hotplugging (disabled by default)
  * UI/Function Live Edit: restore basic live editing of Sequences
  * UI/RGB Matrix Editor: fixed save to Sequence feature
  * UI/Function Manager: when cloning a Sequence, clone the bound Scene too
  * Virtual Console/Button: highlight border with orange color when in "monitoring" state
  * Virtual Console/Slider: fix DMX values not updated when interacting with the mouse wheel, keyboard or Click And Go button
  * Virtual Console/Slider: fix level mode values range scaling
  * Virtual Console/XYPad: the speed of a running EFX preset can now be controlled by a Speed Dial widget
  * RGB Scripts: added "Noise", "3D Starfield", "Random pixel per row" and "Random pixel per row multicolor" (thanks to Doug Puckett)
  * Web access: added basic authentication support (thanks to Bartosz Grabias)
  * Web access: fixed solo frames collapse state
  * Web access: update feedbacks when a slider is moved
  * New fixtures: IMG Stageline BEAM-40 WS/RGBW, Fun-Generation LED Diamond Dome (thanks to Tolmino Muccitelli)
  * New fixture: Elation Cuepix Batten (thanks to Saul Vielmetti)
  * New fixture: Clay Paky Tiger Scan HMI 575/1200 (thanks to Daris Tomasoni)
  * New fixture: Litecraft WashX.21 (thanks to Hannes Braun)
  * New fixtures: Briteq Stagepainter 12, Nicols IP Wash 120, Showtec LED Powerline 16 Bar (thanks to Fredje Gallon)
  * New fixtures: Nicols Movelight, Nicols Birdy Wash 122, Briteq Giga Flash RGB (thanks to Fredje Gallon)
  * New fixtures: Litecraft PowerBar AT10.sx, Stairville MH-z1915 (thanks to Thorben / Fredje)
  * New fixtures: Martin MAC 700 Wash, ADB Warp M (thanks to Thorben)
  * New fixture: Laserworld CS-1000RGB Mk II (thanks to Piotr Nowik)
  * New fixture: Chauvet COLORrail IRC (thanks to Lane Parsons)
  * New fixtures: American DJ COB Cannon Wash DW, lightmaXX Vega Zoom Wash Beam (thanks to Florian Gerstenlauer)
  * New fixture: Martin Rush MH5 Profile (thanks to Falko)
  * New fixture: Cameo Flash Bar 150 (thanks to Kevin Wimmer)
  * New fixtures: Chauvet FXpar 9, IMG Stageline Wash-40 LED (thanks to PeterK)
  * New fixtures: JB Systems iRock 5C, JB Systems LED Devil (thanks to Andres Robles)
  * New fixtures: beamZ BAC406, Geni Mojo Color Moc (thanks to Mark Sy)
  * New fixtures: Stairville MH-250 S, Chauvet GigBAR 2, Pro-Lights Onyx (thanks to Freasy)
  * New fixtures: Coemar ProSpot 250 LX, Showtec Kanjo Spot 60 (thanks to Flo Edelmann)

 -- Massimo Callegari <<EMAIL>>  Sat, 28 Oct 2017 12:13:14 +0200

qlcplus (4.11.0) stable; urgency=low

  * engine: fixed setting start/end color while a RGB Matrix is running
  * engine: fixed crash when pausing a Show with an unavailable audio file
  * engine: major rework of Sequences. Projects using them need to be migrated
  * UI: enabled Alt key combinations on macOS to behave like other platforms (thanks to Matt Mayfield)
  * UI/RGB Panel: added panel direction (thanks to Raivis Rengelis)
  * UI/Fixture Manager: added weight and power consumption information on fixtures/universe selection (Chris de Rock idea)
  * UI/Scene Editor: preserve fixture tab order when fixtures with no channels set are present
  * UI/RGB Matrix Editor: allow the preview to run even in operate mode
  * UI/Audio Editor: added the possibility to loop an audio file (thanks to Raivis Rengelis)
  * UI/Simple Desk: fixed crash when changing values from a channel group in "fixtures view" mode
  * Virtual Console: prevent unwanted feedbacks from widgets in inactive Frame pages (thanks to Lukas Jähn)
  * Virtual Console: fixed manual selection of input channels not considering Frame pages (thanks to Lukas Jähn)
  * Virtual Console: fixed input profiles channels not honored on frame pages other than the first (thanks to Lukas Jähn)
  * Virtual Console/Slider: improved level monitoring with the possibility to act like a Simple Desk slider (see documentation)
  * Virtual Console/Frame: fixed 4.10.5b regression disabling widgets when switching page in design mode
  * Virtual Console/Frame: fixed key controls not copied when cloning a frame (thanks to Lukas Jähn)
  * Virtual Console/Frame: added the possibility to jump directly to a page and assign page names (thanks to Lukas Jähn)
  * Virtual Console/Cue List: improved linked crossfade to perform an additive blending between steps (see documentation)
  * Virtual Console/Speed Dial: improved tap button blinking and feedbacks (thanks to Lukas Jähn)
  * Virtual Console/Speed Dial: it is now possible to copy/paste factors (thanks to Jan Dahms)
  * Virtual Console/Clock: added external input support for countdown and stopwatch modes (thanks to Lukas Jähn)
  * Plugins/OSC: added channel number calculator in configuration page to help integrating new controllers
  * Plugins/Loopback: fixed spurious values emitted when a lot of channels are looped
  * Web access: fixed VC Slider in percentage mode and inverted appearance (thanks to Bartosz Grabias)
  * Web access: support VC Slider reduced range when in level mode
  * Web access: improved getChannelsValues and added Simple Desk reset per-channel (sdResetChannel API)
  * Web access: implemented keypad increase/decrease buttons (thanks to Santiago Benejam Torres)
  * New input profile: Zoom R16 (thanks to Benedict Stein)
  * New MIDI template: Akai APC40 MK2 Ableton mode (thanks to Branson Matheson)
  * New fixture: American DJ FREQ 5 Strobe (thanks to Martin Bochenek)
  * New fixture: Martin Rush MH3 (thanks to Ed Middlebrooks)
  * New fixture: Eurolite LED ACS BAR-12 (thanks to Michael Horber)
  * New fixtures: Martin Rush MH6 Wash, Cameo Studio PAR 64 RGBWA UV 12W (thanks to Piotr Nowik)
  * New fixtures: ETEC Moving Spot 60E, Cameo CLM PAR COB 1, Showtec Compact Power Lightset COB (thanks to Freasy)
  * New fixture: Stairville Tri Flat PAR Profile 5x3W RGB (thanks to Freasy)
  * New fixture: American DJ Punch LED Pro (thanks to Benedict Stein)
  * New fixtures: Contest Mini-Head 10W, Contest Evora B2R (thanks to Fredje Gallon)
  * New fixture: Robe DJ Scan 150 XT (thanks to Allan Madsen)
  * New fixtures: Futurelight PRO Slim PAR-12 HCL, PRO Slim PAR-12 MK2 HCL, Showtec Power Spot 9 Q5 (thanks to Lukas Jähn)
  * New fixture: Showtec XS-1W Mini Moving Beam (thanks to Habefaro)
  * New fixtures: Stage Right Stage Wash 18Wx18 LED PAR, Stage Right 7x20W COB LED Theater PAR (thanks to Collin Ong)
  * New fixture: Cameo CLPIXBAR450PRO, CLPIXBAR650PRO (thanks to Jean-Daniel Garcia & Jeremie Odermatt)
  * New fixture: Clay Paky Alpha Beam 1500 (thanks to Louis Gutenschwager)
  * New fixture: Stairville AFH-600 (thanks to Hannes Braun)
  * New fixture: Involight LED MH77S (thanks to Jászberényi Szabolcs)
  * New fixtures: ETEC LED PAR 64 18x10W RGBWA, LED PAR 64 18x15W RGBWA Zoom (thanks to Simon Orlob)
  * New fixture: Chauvet Swarm Wash FX (thanks to Stephen Olah)
  * New fixture: Clay Paky Alpha Spot HPE 575 (thanks to Rohmer)
  * New fixture: Robe LED Blinder 196LT (thanks to Tim Cullingworth)
  * New fixture: Chauvet COLORband T3 USB (thanks to Ian Nault)
  * New fixtures: American DJ Dotz Matrix, Martin Jem Compact Hazer Pro,Geni Mojo Spin Master Series (thanks to Sam Brooks)
  * New fixture: American DJ XS 400 (thanks to Jared)
  * New fixture: Velleman VDP1500SM (thanks to Freddy Hoogstoel)
  * New fixture: Chauvet Intimidator Spot 355Z IRC (thanks to Michael Clements)
  * New fixture: CLF Tricolor Mini Par (thanks to Jaron Blazer)
  * New fixture: Varytec LED Easy Move Mini Beam & Wash RGBW (thanks to Erik)
  * New fixtures: Smoke Factory Tour-Hazer II, JB Systems Panther, Robe Spot 160 XT (thanks to Thierry Rodolfo)
  * New fixture: American DJ LED Trispot (thanks to Patrick)
  * New fixtures: Contest STB-520 1500W Strobe, Elumen8 COB Tri 4 Pixel Batten, Briteq Tornado 7 (thanks to Robert Box)
  * New fixtures: American DJ 5P Hex, Pro-Lights Moonstone, Chauvet Intimidator Hybrid 140SR (thanks to Robert Box)
  * New fixtures: Robe Robin DLX Spot (thanks to Robert Box)
  * New fixture: ETC ColorSource PAR (thanks to Jon Rosen)
  * New fixture: lightmaXX 5ive STAR LED (thanks to Thomas Weber)
  * New fixture: Talent BL252A (thanks to Massimiliano Palmieri)
  * New fixtures: Showtec: Infinity iW-1915, Infinity XPLO-15 LED Strobe (thanks to Daniele Fogale)
  * New fixtures: Showtec: Infinity iB-5R, Compact Par 18 MKII, Phantom 20 LED Beam (thanks to Nicolò Zanon)
  * New fixture: Griven Gobostorm Plus MK2 (thanks to Attilio Bongiorni)
  * New fixture: Chauvet Freedom Stick (thanks to Jay Szewczyk)
  * New fixture: Eurolite TMH-14, Chauvet Intimidator Trio (thanks to Chris de Rock)
  * New fixture: Chauvet Scorpion Dual (thanks to Alan Chavis)
  * New fixture: American DJ Ultra Hex Bar 12 (thanks to Rhavin)
  * New fixture: Equinox Photon
  * New fixture: QTX MHS-60 (thanks to Nerijus Mongirdas)
  * New fixture: Eurolite LED TMH FE-600, MARQ Colormax Par64, Stairville CLB2.4 Compact LED PAR System (thanks to Klaus Muth)
  * New fixture: Chauvet SlimPar Hex 6 (thanks to Yinon Sahar)
  * New fixture: IMG Stageline PARL 20 DMX (thanks to Felix Pickenäcker)
  * New fixtures: Pro-Lights SmartBatHEX, Fury FY250W, Fury FY250S (thanks to Lorenzo Andreani)
  * New fixture: American DJ Ikon Profile (thanks to Ham Sadler)
  * New fixture: HQ Power Aeron Wash 575, JB Systems Space Color Laser (thanks to Ricardo Mendes)
  * New fixture: American DJ VPar (thanks to Eric Eskam)
  * New fixtures: MARQ Gesture Beam/Wash 102, Colormax Bat, Gesture Spot 100 (thanks to John Yiannikakis)
  * New fixtures: Chauvet COLORado 3P, Legend 330SR Spot, SlimPar HEX 3 (thanks to Kevin Zepp)
  * New fixture: American DJ Mini Dekker (thanks to Chris Davis)
  * New fixtures: American DJ Vizi BSW 300, Blizzard Lighting Flurry 5 (thanks to George Qualley)
  * New fixtures: Pro-Lights PIXIEWASH, Accent1Q, CromoSpot300 (thanks to Tolmino Muccitelli)
  * New fixtures: Involight LED MH50S, LED PAR 180, SBL 2000 (thanks to Facek)
  * New fixture: Pro-Lights Miniruby (thanks to Dario Gonzalez)
  * New fixture: Sagitter Smart DL Wash (thanks to Simu)
  * New fixtures: Eurolite LED THA-250F, Pro-Lights StudioCOBFC (thanks to Andrea Ugolini)
  * New fixture: American DJ Stinger Spot (thanks to Jason R. Johnston)
  * New fixture: Stairville Blade Sting 8 RGBW Beam Mover

 -- Massimo Callegari <<EMAIL>>  Sat, 24 Jun 2017 12:13:14 +0200

qlcplus (4.10.5b) stable; urgency=high

  * engine: fixed 4.10.5 regression on RGB Matrix preset step color calculation
  * Virtual Console/Frame: fixed widgets disable state when switching pages
  * Virtual Console: fixed SpeedDial and Animation widget presets feedbacks, and allow to use custom feedbacks
  * Plugins/DMX USB: fixed 4.10.5 regression preventing to receive data from PRO devices
  * Plugins/DMX USB: [Windows] fixed a long standing bug causing random crashes when receiving DMX data
  * Plugins/MIDI: [macOS] further changes to support virtual ports
  * New fixtures: Stairville M-Fog 1000 DMX, Cameo Superfly XS (thanks to Konni)
  * New fixture: ColorKey WaferPar Quad-W 12 (thanks to Taylor)
  * New fixture: Eurolite LED PARty RGBW (thanks to Heiko Fanieng)
  * New fixture: lightmaXX Platinum CLS-1 (thanks to Marc Geonet)

 -- Massimo Callegari <<EMAIL>>  Mon, 26 Dec 2016 12:13:14 +0200

qlcplus (4.10.5a) stable; urgency=high

  * engine: fixed playback of a chaser within a chaser

 -- Massimo Callegari <<EMAIL>>  Mon, 12 Dec 2016 12:13:14 +0200

qlcplus (4.10.5) stable; urgency=low

  * Engine: added indigo to fixture channel colors (thanks to Axel Metzke)
  * Engine: properly handle RGB Matrices with generic dimmers (Jano Svitok)
  * UI/Function Manager: fix crash when trying to clone a folder (David Garyga)
  * UI/RGB Matrix Editor: editor preview doesn't stop when testing the Function
  * UI/Collection Editor: allow multiple selection and added Function reordering buttons
  * UI/Remap: fixed universes list in target mapping
  * UI/Remap: fixed wrong Scene remapping when mixing cloned and new fixtures
  * UI/Remap: added remapping also of Fixture Groups
  * Virtual Console/Frame: Show page number when collapsed (thanks to Matthias Gubisch)
  * Virtual Console/Cue List: allow to choose playback buttons layout (Play/Pause + Stop or Play/Stop + Pause)
  * Plugins/DMX USB: fixed crash happening on PRO devices when receiving a full universe
  * Plugins/DMX USB: [MacOS] fixed regression caused by the Qt libraries on PRO devices
  * Plugins/MIDI: [MacOS] added support for virtual ports, show only active devices and properly handle hotplug
  * Fixture Editor: fixed minimum value of a new capability not updating correctly
  * RGB Scripts: added One by one (Jano Svitok)
  * New fixtures: Pro-Lights LumiPIX 12Q, Proel PLLEDMLBG (thanks to Andrea Ugolini)
  * New fixtures: Stairville DCL Flat Par 18x4W CW/WW, Cameo LED MultiPAR CLM-PAR-COB1 (thanks to Freasy)
  * New fixtures: High End Systems Studio Beam, lightmaXX EASY Wash 5IVE LED (thanks to Freasy)
  * New fixture: iSolution iColor 4 (thanks to withlime)
  * New fixtures: ETC ColorSource Spot, Blizzard Lighting LB-Par Hex (thanks to Robert Box)
  * New fixtures: American DJ: Chameleon QBar Pro,DJ Vizi Beam RXONE, XS 600, Focus Spot Three Z (thanks to Robert Box)
  * New fixture: JB-Lighting Varyscan P6, Cameo Wookie series, Cameo Hydrabeam series (thanks to Andres Robles)
  * New fixture: Chauvet RotoSphere LED (thanks to Carl Eisenbeis)
  * New fixture: Briteq Spectra 3D Laser (thanks to Robert Box + Freasy)
  * New fixture: Martin MH2 Wash (thanks to John Yiannikakis + Freasy)
  * New fixture: American DJ Flat Par Tri7X (thanks to Brian)
  * New fixtures: Ledj Stage Color 24, 59 7Q5 RGBW, 59 7Q5 RGBA (thanks to Paul Wilton)
  * New fixtures: American DJ: Inno Spot Elite, Stinger, Tri Phase (thanks to Piotr Nowik)
  * New fixtures: Showtec Phantom 95 LED Spot, Futurelight PHS-260 (thanks to Piotr Nowik)
  * New fixture: Blizzard Lighting Rocklite RGBAW (thanks to Larry Wall)
  * New fixture: American DJ Comscan LED (thanks to Chris)
  * New fixtures: PR Lighting XL 250/XL 700 Wash/XL 700 Spot, American DJ Accu Fog 1000 (thanks to István Király)
  * New fixtures: Equinox Ultra Scan LED, Kam Powercan84W, QTX HZ-3 (thanks to Chris Moses)
  * New fixture: Eurolite TMH-10 (thank to exmatrikulator)
  * New fixture: Eurolite LED SLS 5 BCL, Robe Fog 1500 FT (thanks to Christian Hollbjär)
  * New fixture: SGM Giotto Spot 400 (thanks to Mihai Andrei)
  * New fixture: Pulse LEDBAR 320 (thanks to Allan Rhynas)
  * New fixture: Equinox Swing Batten (thanks to Dean Clough)
  * New fixture: Cameo Pixbar 600 PRO, Chauvet COLORado 1 Quad Zoom Tour (thanks to Andrew Hallmark)
  * New fixture: Involight FM900 DMX (thanks to Jászberény Szabolcs)
  * New fixture: Showtec Stage Blinder Series (thanks to Antoni J. Canós)
  * New fixture: MARQ Gamut PAR H7 (thanks to Lance Lyda)
  * New fixtures: Chauvet: SlimPAR QUV12 USB, SlimPAR PRO H USB, Scorpion Bar RG (thanks to Pete Mueller)
  * New fixture: Stairville CLB8 Compact LED PAR System (thanks to Detlef Fossan)
  * New fixture: Chauvet Cubix 2.0 (thanks to Jungle Jim)
  * New fixture: Showtec Giant XL LED (thanks to Samuel Hofmann)
  * New fixtures: SGM: Idea Beam 300, Idea Led Bar 100, Idea Spot 700, Newton 1200 (thanks to Oscar Cervesato)
  * New fixtures: Pro Lights LumiPAR18QTour, Elation SIXPAR 200IP (thanks to Oscar Cervesato)
  * New fixture: Stairville Beam Moving Head B5R, American DJ Flat Par TW12, Varytec Easy Scan XT Mini (thanks to Thierry Rodolfo)

 -- Massimo Callegari <<EMAIL>>  Sat, 3 Dec 2016 12:13:14 +0200

qlcplus (4.10.4) stable; urgency=low

  * Scripts: Fix 4.10.3a regression that breaks values parsing (David Garyga)
  * Engine: fix relative paths when opening a project from the command line
  * Engine: improved the start/stop mechanism of Functions within a Show
  * Chaser Editor: a newly created step is now selected automatically
  * Scene Editor: fixed the tab order of the fixtures
  * Show Manager: added the possibility to pause a Show leaving the lights on
  * Show Manager/Audio: allow to display the waveform preview while playing a file
  * UI/Function Selection: fix crash on workspaces where a scene ID is bigger than its sequence ID (David Garyga)
  * UI/Video: fixed the fullscreen positioning on Windows
  * Virtual Console/Animation: fix behavior issue when changing the associated function (David Garyga)
  * Virtual Console/Frames: send feedbacks for the enable button
  * Virtual Console/Frames: fix 4.10.3 regression causing frames to resize after configuration
  * Virtual Console/Cue List: playback can now be paused and resumed (see documentation)
  * Virtual Console/Cue List: added a dedicated stop button, with external controls
  * Virtual Console/XYPad: fixed computation of reversed fixture position (Luca Ugolini)
  * Plugins/OSC: fixed regression of receiving data from the wrong interface (David Garyga)
  * Plugins/OSC: fixed regression causing not receiving data anymore when changing the input profile (David Garyga)
  * Plugins/MIDI: distinguish MIDI beat clock start and stop (see documentation)
  * Input Profiles Editor: it is now possible to define button custom feedbacks in a profile (see documentation)
  * New input profile: Novation Launchpad Pro (thanks to David Giardi)
  * New RGB script: Balls (color) (thanks to Rob Nieuwenhuizen)
  * Fixture updated: Starway MaxKolor-18 (thanks to Thierry Rodolfo and Robert Box)
  * Fixture updated: Cameo LED RGBW PAR64 18x8W (thanks to Lukas)
  * New fixture: American DJ Mega QA Par38 (thanks to Nathan Durnan)
  * New fixture: Martin MAC 250 Wash (thanks to Robert Box)
  * New fixture: Luxibel LX161 (thanks to Freddy Hoogstoel)
  * New fixture: Stairville MH-X60th LED Spot (thanks to Jasper Zevering)
  * New fixture: Cameo CLHB400RGBW (thanks to Mihai Andrei)
  * New fixture: Showlite Flood Light Panel 144x10mm LED RGBW (thanks to Ex)
  * New fixture: Color Imagination LedSpot 90 (SI-052), Robe Spot 575 XT (thanks to DJ Ladonin)
  * New fixture: Chauvet Mini Kinta (thanks to Jonathan Wilson)
  * New fixture: Eurolite LED ML-56 QCL RGBW-RGBA 18x8W (thanks to Matthijs ten Berge)
  * New fixture: High End Systems TechnoSpot (thanks to Tom Moeller)
  * New fixtures: American DJ Inno Pocket Spot Twins, Fog Fury 3000 WiFly, Event Bar Pro (thanks to MaBonzo)
  * New fixtures: American DJ Galaxian Gem IR, Vizi Roller Beam 2R (thanks to MaBonzo)
  * New fixture: Ayra ERO 506 (thanks to Bert Heikamp)
  * New fixture: Ayrton Arcaline 100 RGB, Martin Magnum Hazer (thanks to Thierry Rodolfo)
  * New fixtures: American DJ Asteroid 1200, Eurolite GKF-60, Eurolite LED FE-700 (thanks to Flox Garden)
  * New fixtures: Antari X-310 Pro Fazer, lightmaXX CLS-2 (thanks to Flox Garden)
  * New fixture: Beamz MHL90 Wash 5x18W RGBAW-UV (thanks to Hans Erik Tjelum)
  * New fixture: PR Lighting Pilot 150 (thanks to David Read)
  * New fixture: lightmaXX Platinum CLS-1 (thanks to Marc Geonet)

 -- Massimo Callegari <<EMAIL>>  Sun, 29 May 2016 12:13:14 +0200

qlcplus (4.10.3a) stable; urgency=low

  * Scripts: Fix 4.10.3 regression that breaks time values parsing (David Garyga)
  * RGBMatrix Editor: Fix 4.10.3 regression where QLC+ hangs on duration < 20ms (David Garyga)

 -- Massimo Callegari <<EMAIL>>  Wed, 9 Mar 2016 22:03:14 +0200

qlcplus (4.10.3) stable; urgency=low

  * Engine: Fix intensity channels forced back to HTP after LTP not working correctly (David Garyga)
  * Engine: Fix functions with low intensity killing current fade outs (David Garyga)
  * Audio Capture: Fix crash when selecting another audio input while a capture is running (David Garyga)
  * Audio Capture: Fix crash when trying to use a wrongly configured audio input (David Garyga)
  * Scene Editor: Remember Channels Groups values when saving and loading a workspace (David Garyga)
  * Scene Editor: Remember fixtures even with no activated channel (David Garyga)
  * RGBMatrix Editor: Fix preview now working when fade in > 0 (David Garyga)
  * RGBMatrix Editor: Fix length of fadeout on the preview (David Garyga)
  * Show Manager: Fix crash when editing the total time of an empty chaser (David Garyga)
  * Show Manager/Function Selection: Fix sequences always displayed even with Chasers and Scenes both filtered out (David Garyga)
  * Speed Dials: Fix display and input of the milliseconds field, update precision from 10ms to 1ms (David Garyga)
  * Input/Output Manager: Forbid deleting universes in the middle of the list, this prevents a lot of bugs and crashes (David Garyga)
  * Simple Desk: the number of faders is now dynamic depending on the window size (unless forced via config file)
  * Plugins/ArtNet: Fix input and output initialization conflict that results in no input (David Garyga)
  * Plugins/ArtNet: Allow sending and receiving ArtNet on several different interfaces (David Garyga)
  * Plugins/ArtNet: Allow selecting a different ArtNet input universe (David Garyga)
  * Plugins/ArtNet: Fix configuration issue that prevents setting a parameter back to its default value (David Garyga)
  * Plugins/OSC: Fix configuration issue that prevents setting a parameter back to its default value (David Garyga)
  * Plugins/OSC: OSC Output values range from 0.0 to 1.0
  * Plugins/OSC: Properly handle OSC bundles (restores Lemur compatibility)
  * Virtual Console: Fix copy of a frame containing a submaster slider resulting in a broken submaster (David Garyga)
  * Virtual Console/Slider: Enable function filters in playback function selection (David Garyga)
  * Virtual Console/Slider: Allow to force to LTP color channels controlled by a Click & Go button
  * Virtual Console/Solo Frame: Fix sliders in playback mode not actually stopping the attached function when the slider reaches 0 (David Garyga, thanks to Tubby)
  * Virtual Console/Animation: Can now be used in solo frames (David Garyga)
  * Virtual Console/Frames: fix page cloning of a nested multipage frame
  * Virtual Console/Frames: fix disabling frame pages. Now widgets get actually deleted
  * Web access: fixed custom fixtures loading
  * Web access: added a DMX keypad that can be accessed from the Simple Desk (thanks to Santiago Benejam Torres)
  * Input profiles: added Behringer BCR2000 (thanks to Michael Trojacher)
  * Input profiles: added Lemur iPad Studio Combo
  * RGB Scripts: added Strobe script (thanks to Rob Nieuwenhuizen)
  * New fixtures: Stellar Labs ECO LED PAR56, Chauvet Colorpalette II (thanks to Jimmy Traylor)
  * New fixtures: Chauvet: COLORado 1 Solo, Ovation FD-165WW, Rogue RH1 Hybrid, COLORdash Par Hex 12, COLORdash Accent Quad (thanks to Robert Box)
  * New fixtures: Chauvet: Vue 1.1, Intimidator Spot 100 IRC, Abyss USB, COREpar 40 USB, COREpar UV USB (thanks to Robert Box)
  * New fixtures: Chauvet: Intimidator Scan 305 IRC, Intimidator Barrel 305 IRC, SlimPAR T6 USB, SlimBANK TRI-18 (thanks to Robert Box)
  * New fixtures: Eurolite LED CLS-9 QCL RGBW 9x8W 12, JB-Lighting A12 Tunable White, SGM G-Profile (thanks to Robert Box)
  * New fixtures: Coemar Par Lite LED RGB, OXO LED Funstrip DMX, American DJ Stinger II (thanks to Robert Box)
  * New fixture: Chauvet LED PAR 64 Tri-C (thanks to Jungle Jim and Robert Box)
  * New fixtures: American DJ VBar, American DJ Jellydome, Briteq LDP Powerbar 6TC/12TC (thanks to Thierry Rodolfo)
  * New fixture: Sagitter Slimpar 18 RGB (thanks to Daniele Fogale)
  * New fixture: Microh LED Tri Bar (thanks to Michael Tughan)
  * New fixture: American DJ 12P Hex Pearl (thanks to Ethan Moses)
  * New fixture: JB-Lighting JBLED A7 (thanks to BLACKsun)
  * New fixture: Chauvet COREpar 80 USB (thanks to Chris Gill)
  * New fixture: Stairville DJ Lase 25+25-G MK-II (thanks to galaris)
  * New fixtures: PR Lighting XR 230 Spot, PR Lighting XLED 1037 (thanks to Ovidijus Cepukas)
  * New fixtures: Futurelight DJ-Scan 600, Eurolite LED PAR-64 RGBW+UV (thanks to Ovidijus Cepukas)
  * New fixtures: Varytec LED Pad 7 BA-D, American DJ X-Scan LED Plus, Showtec Blade Runner (thanks to DjProWings)
  * New fixture: Involight LED CC60S (thanks to Stephane Hofman)
  * New fixture: Stairville MH-x200 Pro Spot (thanks to Mirek Škop)
  * New fixtures: Varytec LED Giga Bar 4 MKII, Eurolite LED KLS Laser Bar FX Light Set (thanks to Daniel Schauder)
  * New fixture: Chauvet Mayhem (thanks to Jonathan Wilson)
  * New fixture: Ayra TDC Agaricus (thanks to Rob Nieuwenhuizen)
  * New fixture: American DJ Pinspot LED Quad DMX (thanks to Christian Polzer)
  * New fixture: Stairville AF-180 LED Fogger Co2 FX (thanks to Johannes Uhl)

 -- Massimo Callegari <<EMAIL>>  Sun, 6 Mar 2016 20:21:22 +0200

qlcplus (4.10.2) stable; urgency=low

  * Engine: added support for devices hotplug (DMX USB, MIDI, HID, Peperoni)
  * Engine: Universe passthrough data is now merged with QLC+ output, it is not affected by QLC+ processing
    (except for blackout) and it appears in the DMX monitor (Jano Svitok)
  * Audio: fixed playback of 24/32 bit wave files and Show Manager waveform preview
  * DMX Dump: it is now possible to dump DMX values on an existing Scene
  * ClickAndGo Widgets: Preset widgets now display the channel name on top of the capability list (David Garyga)
  * Function Manager: fix startup Function not cleared when deleting it (David Garyga)
  * Function Manager: highlight current startup Function when opening the Function selection dialog (David Garyga)
  * Function Selection: Don't lose the current selection when changing the function type filter (David Garyga)
  * Show Manager: fixed looped functions never stopping with certain durations (Jano Svitok)
  * Show Manager: fixed copy/paste of an existing Chaser
  * Show Manager: fix crashes when copying a sequence on an empty track (David Garyga)
  * Show Manager: repair conflicting sequences when loading a broken workspace (David Garyga)
  * EFX Editor: removed the intensity control. Please use separate Scenes for that
  * Virtual Console/Slider: fixed copy of the channel monitor mode (David Garyga)
  * Virtual Console/Slider: in level mode, activate only in operate mode and don't hold forced LTP channels (David Garyga)
  * Virtual Console/Slider: in playback mode, ignore the fade in/fade out of the attached Function (David Garyga)
  * Virtual Console/XYPad: added Fixture Group preset, to control a subgroup of Fixtures (see documentation)
  * Virtual Console/XYPad Properties: fixture ranges can now be set in degrees, percentage or DMX values
  * Virtual Console/XYPad Properties: fix manual input selection for presets (David Garyga)
  * Virtual Console/Cue List: improved mixed usage of crossfader and next/previous buttons (David Garyga)
  * Virtual Console/Cue List: fix effect of a submaster slider on a Cue List in crossfader mode (David Garyga)
  * Virtual Console/Cue List: fix crash when adding steps to the chaser being run by a Cue List (David Garyga)
  * Virtual Console/Audio Triggers: fix virtual console buttons triggering (David Garyga)
  * Virtual Console/Input Selection: allow custom feedbacks only on an assigned source and don't crash (David Garyga)
  * DMX Monitor: Fix strobing in 2D view (Jano Svitok)
  * Fixture Editor: Fix crash in the Channel Editor (David Garyga)
  * Plugins/uDMX: added support for AVLdiy.cn clone (thanks to Vitalii Husach)
  * Plugins/DMXUSB: (Linux) fixed data transmission of DMX4ALL NanoDMX
  * Input Profiles: added an option for Buttons to always generate a press/release event
  * New input profile: Touch OSC Automat5
  * New input profile: Novation Launch Control (thanks to Giacomo Gorini)
  * Updated fixture: Stairville xBrick Full-Colour 16X3W (thanks to Rico Hansen)
  * Updated fixture: Stairville MH-100 Beam 36x3 LED (thanks to Antoni J. Canos)
  * Updated fixture: American DJ Revo 3 (thanks to David Pilato)
  * New fixture: American DJ Dotz Flood
  * New fixture: Chauvet COLORdash Par Quad-7
  * New fixtures: Robe ColorWash 1200E AT, American DJ Starburst, Chauvet LED PAR 64 Tri-B (thanks to Robert Box)
  * New fixtures: Cameo Multi Par 3, HQ Power VDPL110CC LED Tri Spot, Showtec LED Pixel Track Pro (thanks to Robert Box)
  * New fixtures: American DJ: Inno Pocket Z4, On-X, WiFly EXR Dotz Par, WiFly EXR HEX5 IP, COB Cannon Wash Pearl (thanks to Robert Box)
  * New fixtures: BoomTone DJ Sky bar 288 LED, BoomToneDJ Strob LED 18, BoomToneDJ Froggy LED RGBW (thanks to Didou)
  * New fixture: iSolution iMove 250W (thanks to Thierry Rodolfo)
  * New fixture: Talent BL63 10" LED Bar (thanks to FooSchnickens)
  * New fixtures: Contest Oz-37x15QC, Evora DUO B2R, Evora Beam 5R, Evora Beam 15R (thanks to Jan Lachman)
  * New fixtures: Blizzard Lighting Lil G, Pixellicious, Lo-Pro CSI (thanks to Alton Olson)
  * New fixtures: Stairville LED Matrix Blinder 5x5, Showtec Power Spot 9 Q6 Tour V1 (thanks to Samuel)
  * New fixture: Blizzard Lighting StormChaser (thanks to Brent)
  * New fixtures: Showtec Explorer 250 Pro MKII, Showtec Pixel Bar 12 (thanks to Henk de Gunst)
  * New fixture: Philips Selecon PLProfile1 MkII (thanks to Freasy)
  * New fixture: PSL Strip Led RGB code K2014 (thanks to Lorenzo Andreani)
  * New fixture: Chauvet SlimPar Pro Tri (thank to Bulle)
  * New fixture: Chauvet GigBar IRC (thanks to JD-HP-DV7 and Jungle Jim)
  * New fixtures: Ghost Green 30, KOOLlight 3D RGB Laser, Mac Mah Mac FOG DMX (thanks to David Pilato)

 -- Massimo Callegari <<EMAIL>>  Sun, 13 Dec 2015 20:21:22 +0200

qlcplus (4.10.1) stable; urgency=high

  * Virtual Console/Cue List: improved step fader behaviour (David Garyga)
  * Plugins/DMXUSB: Fixed regression affecting Linux users and OSX users using the libFTDI interface
  * Plugins/ArtNet/E1.31/OSC: Improved network interfaces detection
  * New fixture: Enterius EC-133DMX (thanks to Krzysztof Ratynski)
  * New fixture: Showtec Dragon F-350 (thanks to Jasper Zevering)
  * New fixtures: JB Systems Lounge Laser DMX, JB Systems Super Solar RGBW (thanks to Robert Box)

 -- Massimo Callegari <<EMAIL>>  Wed, 21 Oct 2015 20:21:22 +0200

qlcplus (4.10.0) stable; urgency=low

  * Channel Groups: Fix crashes related to invalid channels (David Garyga)
  * Chaser: Fix flickering issue when chaser order is Random (David Garyga)
  * Engine: some more fixes on forced HTP/LTP channels
  * Engine: fixed 4.9.x regression causing QLC+ to hang at the end of audio playback
  * RGB Matrix Audio Spectrum: Fix crash when audio input volume is set to zero (David Garyga)
  * RGB Matrix: Fix 4.9.1 regression which makes fading of green and blue colors not smooth (David Garyga)
  * RGB Matrix: Introduced blending mode between matrices (see documentation)
  * Audio Input: Fix crashes when selecting another audio input device while an audio input driven function/widget is running (David Garyga)
  * Audio Input: It is now possible to select the audio input format (sample rate and channels)
  * Video: fixed playback from a time offset (where possible)
  * Video: (Windows) Videos now have a black background, like all the other platforms
  * Add Fixture dialog: Generic fixtures don't take the number of channels of the previously selected fixture (David Garyga)
  * Add Fixture dialog: Fix address 512 not usable by adding several fixtures at a time (David Garyga)
  * Scene Editor: correctly select the fixture tab when switching from tabbed/all channels view
  * EFX Editor: it is now possible to use an EFX on RGB channels (thanks to Giorgio Rebecchi)
  * Collection Editor: added preview button (thanks to Giorgio Rebecchi)
  * Fixture Remap: fixed remapping of EFX functions
  * Function Wizard: improved creation of color scenes for RGB panels
  * Function Wizard: automatically set a gobo picture (if available) on buttons attached to gobo Scenes
  * Show Manager: Fix some cursor teleportation issues (David Garyga)
  * Simple Desk: Fix cue playback on universes 2+ (David Garyga)
  * Simple Desk: Fix crash when selecting recently added universe (David Garyga)
  * Simple Desk: Added reset buttons to reset a single channel
  * Simple Desk: Fix page count when channels per page does not divide 512 (Jano Svitok, reported by Florian)
  * Virtual Console: fixed Grand Master not sending feedbacks
  * Virtual Console/Input Controls: implemented custom feebacks. For now used only by VC buttons
  * Virtual Console/Solo Frame: Option to allow mixing of sliders in playback mode (David Garyga)
  * Virtual Console/Speed Dial: Introduced multiplier/divisor, apply and presets buttons (see documentation)
  * Virtual Console/Button: allow to set a background picture
  * Virtual Console/Cue List: Options for the Next/Previous buttons behavior (David Garyga)
  * Virtual Console/Cue List: Fixed playback of a Chaser in reverse order (David Garyga)
  * Virtual Console/Cue List: Added a new "Steps" mode for the side faders (see documentation)
  * Virtual Console/Cue List: Allow to resize columns to 0 pixels, to completely hide them
  * Virtual Console/XYPad: Introduced presets, including the usage of existing EFX and Scenes (see documentation)
  * Virtual Console/XYPad: Fix DMX output not working when going to Operate mode while the XYPad is disabled (David Garyga, thanks to bestdani)
  * Input Profiles: added an option for MIDI profiles to feedback a Note Off or a Note On with 0 velocity. APCMini now works out of the box. (Jano Svitok)
  * Input Profiles: Improved BCF2000 Input profile (thanks to Lorenzo Andreani)
  * Plugins/MIDI: (Linux) Fixed data transmission to multiple devices (thanks to Adrian Kapka)
  * Plugins/MIDI: fixed Program Change handling on OSX and Windows
  * Plugins/MIDI: (Windows) do not close the device when sending SysEx data
  * Plugins/ArtNet: it is now possible to enter an arbitrary output IP
  * Plugins/OSC: it is now possible to enter an arbitrary output IP
  * Plugins/E1.31: added stream priority to configuration (thanks to Nathan Durnan)
  * Plugins/E1.31: added unicast support (David Garyga)
  * Plugins/DMXUSB: fixed close/open sequence on a Enttec Pro input line
  * Web Access: implemented frames collapse functionality
  * RGB Scripts: added Plasma Colors script (thanks to Nathan Durnan)
  * Fixture Editor: Channel capability editing is now done in a single window
  * Updated fixture: Showtec Indigo 6500 (thanks to Jochen Becker)
  * New fixture: Ayra ComPar 20 (thanks to Rob Nieuwenhuizen)
  * New fixture: XStatic X-240Bar RGB (thanks to Nathan Durnan)
  * New fixture: Venue ThinPAR 38 (thanks to Thierry Rodolfo)
  * New fixtures: Contest MiniCube-6TCb, Eurolite LED FE-1500, Lightronics FXLD618C2I, JB Systems COB-4BAR (thanks to Robert Box)
  * New fixtures: Eurolite LED KLS-401, Chauvet Intimidator Wash Zoom 350 IRC, Equinox Party Par LED PAR 56 (thanks to Robert Box)
  * New fixtures: Robe Robin MiniMe, PR Lighting Pilot 575, Stairville DJ Lase 150-RGY MkII, JB Systems Dynaspot (thanks to Robert Box)
  * New fixtures: American DJ Hyper Gem LED, Robe ColorSpot 575 AT, Kam iLink All Colour Models (thanks to Robert Box)
  * New fixtures: Chauvet Intimidator Wave 360 IRC, Varytec LED PAR56 (thanks to Habefaro)
  * New fixture: American DJ Fog Fury Jett (thanks to Dean Clough)
  * New fixtures: Eurolite TB-250, Futurelight DJ-HEAD 575 SPOT, GLX Lighting Power LED Beam 38 Narrow (thanks to Ovidijus Cepukas)
  * New fixture: Pro-Lights UVStrip18 (thanks to Alessandro Grechi)
  * New fixtures: American DJ Inno Pocket Beam Q4, Martin ZR24/7 Hazer, Blizzard Lighting Stimul-Eye (thanks to George Qualley)
  * New fixture: American DJ Mega TriPar Profile Plus (thanks to George Qualley)
  * New fixtures: Pro-Lights SmartBat, Robe ClubWash 600 CT (thanks to Lorenzo Andreani)
  * New fixture: Stairville Show Bar Tri 18x3W RGB (thanks to Udo Besenreuther)
  * New fixtures: Blizzard Lighting Rokbox Infiniwhite, Chauvet COREpar 80 (thanks to Chris Gill)
  * New fixture: Cameo CL Superfly HP (thanks to Stuart Brown)
  * New fixture: American DJ Event Bar Q4 (thanks to Maxime Bissonnette-Théorêt)
  * New fixture: Cameo LED Moving Head 60W CLMHR60W (thanks to Jasper Zevering)
  * New fixtures: Proel PLLED64RGB, Litecraft LED PAR 64 AT3, Robe Robin 300E Beam (thanks to Mihai Andrei)
  * New fixture: Electroconcept SPC029 (thanks to Bulle)
  * New fixture: Microh Plasmawave 1 RGB (thanks to Rommel)

 -- Massimo Callegari <<EMAIL>>  Sun, 18 Oct 2015 20:21:22 +0200

qlcplus (4.9.1) stable; urgency=high

  * RGBMatrix: SingleShot RGBMatrix make use of FadeOut time (David Garyga)
  * RGBMatrix Editor: Update duration when changing fade in time (David Garyga)
  * Monitor: fixed values update of EFX in relative mode
  * VC Audio Triggers: fixed triggers enable from the web interface
  * VC Audio Triggers: restored audio level fader
  * VC Speed Dial: Allow ms precision for absolute range (David Garyga)
  * Simple Desk: restored the icon to delete a Cue (Jano Svitok)
  * UI: fixed channel group sliders not showing up
  * Plugins/DMXUSB: fixed for the (hopefully) last time Stageprofi data transmission
  * Plugins/OSC: fixed data receive on loopback interface
  * Plugins/OSC: fixed messages with a name length multiple of 4 not received
  * RGB Scripts: add Waves script (thanks to Nathan Durnan)
  * New fixtures: Showtec Compact Par 18 Tri MKII, Showtec Indigo 150 LED, Elation Rayzor Q7 (thanks to Thierry Rodolfo)
  * New fixtures: Eurolite Led Theatre 36x3W CW/WW, Martin Viper AirFX, Clay Paky Glow Up, Martin CX-10 Extreme (thanks to Thierry Rodolfo)
  * New fixture: Sagitter Slimpar 7DL (thanks to Damiano Mologni)
  * New fixture: Varytec LED Hellball 3 (thanks to Daniel)
  * New fixtures: Botex-DPX-620-III, Stairville AF-40 DMX (thanks to Yateri)
  * New fixture: Cameo Gobo Scanner 60 (thanks to Tobi Vossen)
  * New fixtures: Stairville LED Flood Panel 150, Eurolite PST 15W QCL RGB (thanks to Ralf Herold)
  * New fixture: Eurolite LED D-400 RGBAW 3W DMX (thanks to Alexander Grözinger)

 -- Massimo Callegari <<EMAIL>>  Tue, 7 Jul 2015 20:21:22 +0200

qlcplus (4.9.0) stable; urgency=low

  * Function Manager: Fix sequences sometimes loaded in the chaser folder (David Garyga)
  * RGBMatrix: Automatic "dimmer set to 100%" feature can be disabled (David Garyga)
  * Input Selection: Unpatched universes not considered as invalid anymore (David Garyga)
  * Input/Output tab: Fix Add Universe button adding wrong universe ID after loading a workspace (David Garyga)
  * Speed Dial & RGB Matrix & Chaser: improve tap precision (David Garyga)
  * Monitor: Fix various issues & crashes when loading a project while the Monitor is open (David Garyga)
  * Simple Desk: Fix deadlock when clicking the "Reset Universes" button (David Garyga)
  * Fixture Selection: Disable heads non pan/tilt capable in EFX and XYPad editors (David Garyga)
  * Function Live Edit: Fix crash when changing tab mode in Scene Editor (David Garyga)
  * Function Live Edit: Fix deadlock when toggling blind mode in Scene Editor (David Garyga)
  * VC Button: Allow to edit the fade out time of a stop-all-functions button with the text input (David Garyga)
  * VC Submaster: fix submaster level not taken into account right from project loading (David Garyga)
  * VC Submaster: fix chaser not taking submaster level into account (David Garyga)
  * VC Submaster: Cue Lists are now affected by Submaster faders (David Garyga)
  * VC SpeedDial: Fix copy (David Garyga)
  * VC SpeedDial: Allow linking the infinite checkbox to an external input (David Garyga)
  * VC CueList: Activate Start/Stop button feedback (David Garyga)
  * Chaser Editor: Fix string-to-speed conversion (David Garyga)
  * Chaser Editor: Fix erratic behavior when changing speed values (David Garyga)
  * Fixture Manager: RGB panels can now have RGB, BGR, BRG, GBR, GRB, or RGBW components
  * Engine: Overall improvement to reduce the CPU usage to detect and monitor universe changes
  * Engine: fixed DMX monitor misbehaviours
  * Engine: Fix RGBMatrix randomly starting with wrong direction (David Garyga)
  * Engine: Fix RGBMatrix never shutting off lights when fadeout > duration (David Garyga)
  * Engine: Fix functions contained in chaser or audiotrigger not stopped properly (David Garyga)
  * Engine: Fix collection not updating its running children intensity (David Garyga)
  * Engine: Fix random erratic channel behavior when flashing huge scenes (David Garyga)
  * Engine: Fix EFX "jumps" when changing the speed of a running EFX (David Garyga)
  * Engine: fixed wrong behaviour of intensity channels forced back to HTP (Jano Svitok)
  * Engine: Channel modifiers moved after the Grand Master to be the last part of the DMX values processing (Jano Svitok)
  * Plugins: plugins configuration is now saved into the project XML to give more more portability (applies to MIDI, OSC, ArtNet and E1.31)
  * Plugins/OSC: rewritten from scratch to support infinite inputs/outputs, the loopback device and the new parameters system
  * Plugins/ArtNet: it is now possible to set the output IP, the ArtNet universe and the trasnmission mode (see documentation)
  * Plugins/E1.31: it is now possible to set the output IP, the E1.31 universe and the transmission mode (see documentation)
  * Plugins/E1.31: fixed input from a multicast address
  * Plugins/HID: added support for DMXControl Projects e.V. Nodle U1 (thanks to Stefan Krupop)
  * Plugins/HID: added support for Joysticks/Joypads on OSX
  * Plugins/DMXUSB: fixed all the Windows freezes encountered before (Stageprofi, Enttec Pro input, etc..)
  * New fixture: Eurolite LED SLS-QCL (thanks to Kripton)
  * New fixtures: Briteq COB PAR56 RGB, Briteq Pro Beamer Zoom, Briteq BT Theatre 100EC (thanks to Giacomo Gorini)
  * New fixture: Stairville AF-150 (thanks to Bastien Vide)
  * New fixture: Stairville LED PAR 64 Alu (thanks to Allan Rhynas)
  * New fixtures: Chauvet ColorBand Pix-M, Martin MAC 2000 Performance II, Chauvet LED Shadow, Venue THINPAR 64 (thank to Louis Gutenschwager)
  * New fixtures: Involight LED MH60S, ADJ Mega PAR Profile Plus, Robe Robin 100 LEDBeam, Eurolite TSL-100 (thanks to Thierry Rodolfo)
  * New fixtures: Elation Platinum Beam 5R, ADJ Starball LED DMX, Martin MAC Quantum Profile (thanks to Thierry Rodolfo)
  * New fixtures: Elation Sniper 2R, ADJ Inno Scan LED, ADJ Inno Pocket Spot, ADJ Inno Pocket Wash, Chauvet Derby X (thanks to Thierry Rodolfo)
  * New fixtures: Chauvet Q-Spot 250, Chauvet Double Derby X, Starway UrbanKolor, Starway Mode B (thanks to Thierry Rodolfo)
  * New fixtures: Stairville MH-110 Wash, Eurolite LED TMH-6, Showtec Phantom 75 LED Spot V2 (thanks to Thierry Rodolfo)
  * New fixtures: Chauvet Circus, Eliminator Electro 86, ADJ Nucleus PRO (thanks to Peter Chave)
  * New fixtures: Chauvet Swarm 5 FX, Chauvet Techno Strobe 168 (thanks to Jack Logue)
  * New fixture: Laserworld ES 600B (thanks to Dingezz)
  * New fixture: American DJ H2O DMX PRO (thanks to Marty Badger)
  * New fixrure: SGM Idea Spot 250 (thanks to Adrian Kapka)
  * New fixture: Triton Blue Wally MH-20 (thanks to Jose Paris)
  * New fixture: Stairville JunoScan MKII (thanks to Alexander Grözinger)
  * New fixtures: LDDE SpectraConnecT5, Martin MAC600E (thanks to Edgar Aichinger)
  * New fixture: Chauvet Shocker 90 IRC QRG (thanks to Frédéric Combe)
  * New fixture: Chauvet Legend 412Z (thanks to Jean-Daniel Garcia)
  * New fixture: Chauvet CORE 3x3 (thanks to Ravi Radhakrishnan)
  * New fixture: Chauvet Rogue R2 Spot (thanks to George Qualley)
  * New fixture: Equinox Gigabar (thanks to Justin Hornsby)
  * Fixture fix: Chauvet 4Bar (thanks to Jack Logue)
  * Fixture fix: Clay Paky Mini Scan HPE, Starway Axis 250 (thanks to Thierry Rodolfo)
  * Fixture fix: High End Systems Xspot Xtreme (thank to Louis Gutenschwager)

 -- Massimo Callegari <<EMAIL>>  Sat, 27 Jun 2015 20:21:22 +0200

qlcplus (4.8.5) stable; urgency=low

  * Engine: it is now possible to flash LTP channels as well
  * RGB Scripts: added new "Balls" script (thanks to Tim Cullingworth)
  * Collections: forward the startup intensity to children functions (David Garyga)
  * Function Manager: fix crash // disable Add Sequence button when function selection is empty (David Garyga)
  * Fixtures Manager: improve sorting of fixtures present more than 10 times (David Garyga)
  * Fixtures Manager: disable Add RGB Panel button when going in Operate Mode (David Garyga)
  * Fixtures Manager: fix changing the universe of a fixture and associated crash in Simple Desk (Thomas Achtner)
  * Chaser Editor: fix cutting of several steps at the same time (David Garyga)
  * Chaser Editor: fix crash when trying to paste a removed function (David Garyga)
  * Virtual Console Animation: fix some crashes when changing a running Matrix (David Garyga)
  * Virtual Console Animation: fix custom control key sequence not loading (David Garyga)
  * Virtual Console Animation: fix properties of a non-running matrix not set (David Garyga)
  * Virtual Console LiveEdit: fix live editing an XYPad properties making the pad not responsive (David Garyga)
  * Show Manager: fixed rendering and playback of sequences with common duration steps
  * Show Manager: fix sequences present 2 times in Add Function selection dialog (David Garyga)
  * UI: in kiosk mode, don't show the panels tab bar
  * Engine: correctly stop videos on Linux
  * Engine: added video Single Shot and Loop playback modes
  * Engine: preserve audio and video function names given by the user
  * Plugins/DMXUSB: Added support for Eurolite USB DMX512 Pro adapter - Linux only (thanks to Karri Kaksonen)
  * Plugins/EnttecWing: using feedbacks to sync widget values and Playback Wing sliders (thanks to offtools)
  * Fixture Remap: fixed remapping of widgets into nested frames
  * New fixtures: Martin Roboscan Pro 918, American DJ Inno Spot Pro, Clay Paky Stage Color 300 AE (thanks to Thierry Rodolfo)
  * New fixtures: Martin Mac 350 Entour, Martin Acrobat, Martin Rush MH1 Profile, Starway Minikolor, Starway MaxKolor 18 (thanks to Thierry Rodolfo)
  * New fixtures: Clay Paky CP Color 400, Clay Paky Tornado, Clay Paky Mythos, Starway EventKolor (thanks to Thierry Rodolfo)
  * New fixtures: Clay Paky: Stormy, Stormy CC, Show Batten 100, Pin Scan (thanks to Thierry Rodolfo)
  * New fixtures: Vari-Lite VL3500 Spot, Vari-Lite VL3000 Spot, Vari-Lite VL3000 Wash (thanks to Thierry Rodolfo)
  * New fixtures: ADB ACL4, Clay Paky SuperSharpy, Clay Paky A.leda-B-EYE K10-Easy/K10/K20, Laserworld RS400G (thanks to Thierry Rodolfo)
  * New fixture: Eurolite LED Flood Light 252 RGB 19° (thanks to Karri Kaksonen)
  * New fixture: Chauvet Wash FX (thanks to Robert Box)
  * New fixtures: Stairville Octagon Theater, Eurolite LED TMH-9 (thanks to Arnardo)
  * New fixtures: American DJ Monster Fun, American DJ Mega PAR profile, Chauvet SlimPAR64 RGBA, Chauvet SlimPAR 38 (thanks to 007ghg7)
  * New fixture: Chauvet COLORdash Accent RGBW (thanks to Ray Mercer)
  * New fixtures: Cameo LED RGB PAR56-9x3W, Cameo LED RGB PAR64-18x3W, Cameo LED RGB PAR64-18x8W (thanks to vag)
  * New fixture: American DJ ECO UV BAR DMX, Eurolite LED Strobe PRO DMX, JB Systems Dynamo 250 (thanks to Rob G.)
  * New fixture: Kam KMH Series (thanks to Scott McKinney)
  * New fixtures: American DJ UVLed Bar 16 and 20, Dune Lighting Fireworks Pro-II, Chauvet Scorpion Storm FX RGB (thanks to Frédéric Combe)
  * New fixtures: JB-Systems COB-Plano, American DJ UV COB Cannon (thanks to Frédéric Combe)
  * New Fixtures: Showtec LED Light Bar 8 (thanks to Adrian Kapka), Futurelight Genesis 575 (thanks to Dag Stenstad) 
  * New fixture: Showtec XB-Wave (thanks to webdb22)

 -- Massimo Callegari <<EMAIL>>  Sun, 12 Apr 2015 20:21:22 +0200

qlcplus (4.8.4) stable; urgency=low

  * RGB Matrix Editor: fixed conversion to a Sequence
  * Scene Editor: fix preview in non-tabbed mode (David Garyga)
  * Fixture Editor: added UV primary color (Jano Svitok)
  * MIDI templates: added APC mini sysex template (thanks to Joep Admiraal)
  * Experimental Loopback plugin (see docs, Jano Svitok)
  * Monitor: save both DMX view and 2D view settings (only current mode settings were saved, Jano Svitok)
  * RGB Scripts: added new Plasma script and added 'radial' orientation to Gradient (thanks to Tim Cullingworth)
  * Virtual Console: Frame: allow pages circular scrolling (David Garyga)
  * Virtual Console: Frame: Increased the maximum number of pages to 100 
  * Virtual Console: Fix issues with cut-paste from one frame to another (David Garyga)
  * Virtual Console: Solo Frame: Fix regression: frame inside a solo frame not working (David Garyga)
  * Monitor: show color of color filters and shutter state (so far only RGB/CMY channels where shown, Jano Svitok)
  * Input Profile editor: fix off-by-one for MIDI note numbers (72 is C5, Jano Svitok, spotted by Markus Baertschi)
  * Input: fix input received several times in a row after changing input profile (David Garyga)
  * Fix opening files outside of QLC+ install dir in Windows (Jano Svitok)
  * Plugins/ArtNet: fixed input from non-contiguous universes
  * Plugins/ArtNet: enabled local address to communicate with other softwares running on the same computer
  * Webaccess: fixed CSS/JS loading on OSX (4.8.2 regression)
  * Webaccess: deny the favicon, so the interface doesn't get unresponsive after a while
  * New fixture: Robe Pointe (thanks to Moehritz)
  * New fixture: Ledj Performer 18 RGBWA (thanks to Michael Clements)
  * New fixtures: Stairville DJ Lase GR-160 RGY MKII, Stairville Hz-200 DMX (thanks to Hadley Denton)
  * New fixture: lightMaxx Complete PAR56 9x12W RGBAWUV (thanks to Marco Palmisano)
  * New fixtures: Briteq BTX-180L, Martin Mac Viper Wash DX, Martin Mac Viper Performance (thanks to Thierry Rodolfo)
  * New fixtures: Blizzard Hotbox EXA, Blizzard Hotbox RGBAW, Blizzard Hotbox RGBW, Blizzard Puck RGBAW (thanks to Dough Esselburn)
  * New fixture: Varytec LED Laser 4 (thanks to Heiko Fanieng)
  * New fixture: Chauvet Hive (thanks to Vic)
  * New fixture: Studio Due Shark 150C (thanks to Dean Clough)
  * New fixture: American DJ Dotz Par (thanks to Zeph)
  * New fixture: Img Stage Line PARL-4Set (thanks to Mitsch)
  * New fixture: Varytec Thunderscan FX, Pro-Lights GoboMix (thanks to Dag Stenstad)
  * New fixture: Chauvet Omega I DMX-155 (thanks to Tom Moeller)
  * New fixture: Eurolite LED SCY-100 RGBW DMX (thanks to Kithaeron)
  * New fixture: Chauvet Q-Wash 560Z LED (thanks to Connor Faulder)
  * New fixtures: JB Systems Plano Spot 7TC, JB Systems Plano Spot LED (thanks to Robin Davis)

 -- Massimo Callegari <<EMAIL>>  Sun, 8 Feb 2015 20:21:22 +0200

qlcplus (4.8.3) stable; urgency=low

  * Engine/UI: Fixed and improved the Script Function. Please see the documentation
  * Virtual Console/Animation: added end color reset control
  * Virtual Console/Animation: added R/G/B custom control knobs for start and end colors (David Garyga) 
  * Virtual Console/Audio Triggers: fix crash when changing the input device while a trigger is running (David Garyga)
  * Virtual Console/Audio Triggers: fixed incorrect number of bars when reconfiguring the widget
  * Virtual Console/Audio Triggers: multiple triggers at the same time and with different number of bars are now supported
  * Virtual Console/Frames: added option to hide the enable/disable button (David Garyga)
  * Virtual Console/Knobs: now they can be inverted like sliders (Jano Svitok)
  * Virtual Console/Sliders: fix inverted monitor value (David Garyga)
  * Virtual Console/Live Edit: fix some widgets state when live editing (David Garyga)
  * Virtual Console/Solo Frames: can have multiple widgets associated to the same function (David Garyga)
  * Engine: It is now possible to set a network URL on videos
  * Engine: fixed channels forced to HTP/LTP multiple times
  * Engine: fixed universe passthrough flag not restored on project load
  * Fixture Manager: recompute address overlap when fixture mode changes (Jano Svitok)
  * Scene Editor: Channel's primary color rather than its name is used to determine Color Tool availability (Jano Svitok)
  * RGB Matrix Editor: correctly update colors when the algorithm is changed
  * UI: fixed time editing of milliseconds (.3 is 300ms and not 30ms)
  * Plugins/ArtNet: fixed malformed output packets (4.8.2 regression)
  * Webaccess: fix crash when trying to update a closed connection (David Garyga)
  * Application: fix doc modified flag not set on widget deletion, cut/paste, and raise (David Garyga)
  * New fixtures: Cameo LED Mini PAR CAN RGB 7x3W/RGBW 7x8W/White 7x4W (thanks to Joachim Wiedorn)
  * New fixtures: Showtec Infinity iW 715 and iW 720 (thanks to uchris)
  * New fixture: American DJ DJ Spot 250 (thanks to 007ghg7)
  * New fixture: Blizzard Lighting Weather System (thanks to James Elliott)
  * New fixtures: Starway AXIS 250 16Bits, FullKolor, NanoKolor, TourKolor, Servo Beam 5R (thanks to Thierry Rodolfo)
  * New fixtures: DNA Pro Slim 18 RGBW, Martin MAC600 NT, Starway ServoColor 600, Starway ServoColor 800 (thanks to Thierry Rodolfo)
  * New fixtures: Vari-Lite VL2500 Wash, Martin JEM K1 Hazer, Generic Smoke (thanks to Thierry Rodolfo)
  * New fixtures: Martin Mac 2000 Profile II, Martin MAC Aura, PR Lighting PR 5000 Spot (thanks to Thierry Rodolfo)
  * New fixture: Contest PSC-930 (thanks to Noel Utter)
  * New fixtures: Eurolite LED CBB-3 COB RBG 3x15W Bar, lightmaXX Platinum Line Mini PAR QUAD 7x8W, Eurolite LED PMB-4 COB RGB 30W Bar (thanks to Thomas Demmel)
  * New fixture: Pulse SlimLite 56 (thanks to Jimmy Traylor)
  * New fixtures: Chauvet Intimidator Spot LED 150, Chauvet SlimPar Tri 7 IRC (thanks to Tavon Markov)
  * New fixture: Showtec Expression 5000 (thanks to Joep Admiraal)
  * New fixture: Eurolite LED PMB-8 COB RGB 30W (thanks to Thomas Achtner)

 -- Massimo Callegari <<EMAIL>>  Sun, 21 Dec 2014 20:21:22 +0200

qlcplus (4.8.2) stable; urgency=low

  * NEW: Virtual Console: introduced new widget based on RGB Matrix to control animations (see documentation)
  * NEW: RGB Matrix: introduced API version 2. It is now possible to define Script properties accessible from QLC+ (see documentation)
  * NEW: Virtual Console: introducing EXPERIMENTAL Virtual Console live edit (thanks to David Garyga)
  * RGB Matrix Editor: added a toggle button to preview circles or squares
  * Virtual Console: improved Speed Dial layout and allow to set the visibility of each element of the widget
  * Engine: improved perfomance of chaser loading and playback (thanks to David Garyga)
  * Engine: improved loading of RGB Matrix scripts (thanks to David Garyga)
  * Simple Desk: fixed crash on speed dial window open/close
  * Chaser Editor: fixed editing cases when using infinite times (thanks to uchris)
  * Input Profiles: added KORG nanoPAD2 profile (thanks to Daniel Åhr)
  * Input Profiles: added Elation MIDIcon
  * Input Profiles: MIDI messages can be entered directly
  * Webaccess: improved UTF-8 and HTML5 conformance (thanks to Janosch Frank)
  * Webaccess: fixed crash occurring on nested multipage/non-multipage frames
  * ArtNet: fixed dmx output packet length (even number in the range 2-512), fixed input dmx packet parsing
  * New fixtures: NJD Spectre, Chauvet Eclipse RGB, American DJ Dotz TPar, Abstract Twister 4, Pulse ECO LED PAR56 (thanks to Charles Oldham)
  * New fixture: American DJ Galaxian Sky (thanks to Chicken)
  * New fixtures: Cameo Strobe 3x3.5W, Cameo Strobe 6x10W, Stairville HL-x9 Quad Color Flood 9x8W (thanks to Janosch Frank)
  * New fixture: American DJ Inno Pocket Scan (thanks to George Qualley)
  * New fixture: Stairville LED PAR56 10MM-UV (thanks to Hadley Denton)
  * New fixture: Eurolite LED SLS-180 RGB (thanks to Peter)
  * New fixture: Futurelight PHW-260 (thanks to Alessandro Grechi)
  * New fixture: Martin RUSH Par 1 RGBW (thanks to Russell)
  * New fixture: Chauvet SlimPAR QUAD 12 IRC (thanks to Robert Box)
  * New fixture: Cameo LED Colour Bar RGBA (CLBAR10RGBA) (thanks to René Brixel)
  * New fixture: Showtec Led Air Cone Pro (thanks to Joep Admiraal)
  * New fixture: American DJ Dotz Bar 1.4

 -- Massimo Callegari <<EMAIL>>  Wed, 5 Nov 2014 20:21:22 +0200

qlcplus (4.8.1) stable; urgency=high

  * Virtual Console: fixed Buttons background color on Windows
  * Show Manager: fixed audio and video playback
  * Show Manager: it is now possible to add an existing Chaser
  * Plugins/HID: Fixed FX5 input not reaching 0 value (thanks to Babbsdrebbler)
  * Fixture Editor: allow author name editing (Jano Svitok)
  * Show Manager: allow to stretch or loop EFXs and RGB Matrices when changing the duration
  * Virtual Console: fixed multipage frames cloning (thanks to David Garyga)
  * New fixtures: Chauvet-Q-Beam-260-LED, Pro-Lights Omni Tri 9 (thanks to Peter)
  * New fixtures: Chauvet 4Bar, Chauvet Intimidator Spot Duo (thanks to Shikhir Arora)
  * New fixtures: Stairville LED Flood Panel 7x3W, Clay Paky Alpha Wash 700 (thanks to Robert Scheffler)

 -- Massimo Callegari <<EMAIL>>  Fri, 10 Oct 2014 20:21:22 +0200

qlcplus (4.8.0) stable; urgency=low

  * NEW: Show Manager: it is now possible to add RGB Matrix and EFX functions to the timeline 
  * NEW: Show Manager: it is now possible to use a function several times in the timeline
  * NEW: Show Manager: added a timing tool to set a Show item start position and total duration
  * NEW: Virtual Console: Sliders in level mode can now represent the channels they control only if they have all the same value (EXPERIMENTAL)
  * NEW: Virtual Console: Frames can now be enabled/disabled
  * NEW: Input Profile Editor: a slider can now be set to behave in relative or absolute mode
  * NEW: Monitor/2D: it is now possible to set a background picture for specific QLC+ Functions
  * Monitor/2D: representation of pan/tilt is now made of circles. See documentation (Jano Svitok)
  * Plugins/DMXUSB: fixed simultaneous Input/Output on Pro devices. Added MIDI IO support to Pro Mk2
  * Plugins/ArtNet: revert universe ID numbering. QLC+ outputs data on [ArtNet universe]-1 (see documentation)
  * Plugins/OSC: Fixed crash when trying to output data on a non-specified output address
  * Plugins/DMXUSB: Fixed DMX4ALL Stageprofi MK2 freezing on Windows and malfunctioning
  * Show Manager: fixed 4.7.4 regression on time cursor position
  * Show Manager: show names in the drop down list are now sorted alphabetically (thanks to David Garyga)
  * Simple Desk: channel groups are now correctly updated
  * RGB Matrix Editor: fixed crashes when changing the matrix settings while test is running (thanks to David Garyga)
  * Channel Modifiers: added 6 new Exponential/Logarithmic templates (thanks to Luis García-Tornel)
  * Virtual Console: when going to operate mode, grab the focus to allow key combinations to work
  * Virtual Console: widgets key bindings on multipage frames respond only to the active page
  * Virtual Console: performances improvement when combining a Speed Dial with a Cue List (thanks to David Garyga)
  * Virtual Console: Speed Dial restyle. Increased the Tap button size to be more usable on touchscreens
  * Virtual Console: Speed Dial Tap button border blinks at the tapped speed
  * Virtual Console: Do not consider the first Speed Dial tap as it is most likely unwanted
  * UI: Fixed unwanted time values sent by speed dials under certain circumstances
  * Web access: correctly render and handle multipage frames
  * Web access: improved Cue List and Simple Desk look & feel
  * Web access: fixed Simple Desk changes on universes other than the first
  * Web access: added Simple Desk universe reset button 
  * New input profiles: Akai APCMini and Novation Launchpad (thanks to Maikel Boerebach)
  * New fixture: ETC Desire D40 Vivid (thanks to Richard Manner)
  * New fixtures: American DJ Ultra Hex Par3, Chauvet Intimidator Spot 400 IRC, BoomToneDJ Quattro Scan LED (thanks to Robert Scheffler)
  * New fixture: Clay Paky Sharpy
  * New fixture: ADJ Dekker LED (thanks to Chicken)
  * New fixture: Img Stage Line Parl 73-RGB (thanks to Lukakiro)
  * New fixtures: Eurolite TSL-200, Velleman Beam Driver 250, Laserworld CS-1000RGB (thanks to Rob Nieuwenhuizen)
  * New fixture: Cameo Multi PAR 1 (thanks to Santiago Benejam Torres)
  * New fixture: Stairville MH-100 Beam 36x3 LED (thanks to Jasper Zevering)
  * New fixture: Stairville SC-X50 MKII (thanks to Johan Förberg)
  * New fixture: Light Emotion LED Parbar (thanks to Dan Szabo)
  * New fixture: American DJ Inno Pocket Roll (thanks to George Qualley)

 -- Massimo Callegari <<EMAIL>>  Wed, 1 Oct 2014 20:21:22 +0200

qlcplus (4.7.4) stable; urgency=low

  * NEW: added support for DMX4ALL Nano DMX adapter (Linux only at the moment)
  * Virtual Console: Solo Frames now handle Cue Lists and Sliders/Knobs in playback mode too
  * UI: Fixed some crashes on Function Selection and Widget Selection windows (thanks to David Garyga)
  * ClickAndGo: improved popup layout when a lot of presets need to be displayed
  * Channels Configuration: it is now possible to unset a Channel Modifier
  * Channels Configuration: fixed regression causing QLC+ to hang when forcing a channel to HTP
  * Show Manager: added delete action to tracks context menu
  * Show Manager: move cursor when the view is clicked even if the grid is not enabled
  * Show Manager: it is now possible to lock/unlock items
  * Audio Editor: it is now possible to select a specific audio card for playback
  * Cue List widget: Added a progress bar showing the current running cue status
  * Slider widget: when in submaster mode, restore the saved level
  * Speed Dial widget: added time divisors in configuration panel (thanks to David Garyga)

 -- Massimo Callegari <<EMAIL>>  Sat, 26 Jul 2014 12:13:14 +0200

qlcplus (4.7.3) stable; urgency=low

  * NEW: it is now possible to assign a modifier curve to a channel
  * NEW: added Japanese translation (many thanks to Koichiro Saito)
  * Engine: Fixed fallback IO mapping messing up plugins lines
  * Engine: Fixed audio playback on non-default device working only once
  * UI: Fix saving changes when: closing QLC+, opening file, creating new file
  * VirtualConsole: fixed XY Pad tilt on universes other than 1
  * Monitor: fixed crash when showing 2D preview of a fixture that has been removed
  * Monitor: it is now possible to set a background image to the 2D preview
  * Monitor: fixed fixture positioning through the Fixture Item Editor
  * Fixture manager: fixed crash when deleting all groups/fixtures (thanks to David Garyga)
  * Fixture Editor: fixed gobos paths saved on Windows
  * Plugins/Peperoni: added preliminary input support (thanks to Jan Menzel)
  * Webaccess: button state now correctly switch depending on the sent value
  * New fixture: Beamz LCB-252, Cameo LED RGB PAR56 Can, Showtec Dynamic LED v3 (thanks to Sascha Büttner)
  * New fixture: Monacor LSX-142SRGY, Kam Laserscan 1000 3D, Chauvet DMF-10, TSSS XL35, Kam Powercan TRI54W Slim (thanks to Robert Box)
  * New fixture: Martin RoboColor-III (thanks to Boudewijn)
  * New fixture: StageTech LeaderScan Roto (thanks to Noel Utter)

 -- Massimo Callegari <<EMAIL>>  Sun, 22 Jun 2014 12:13:14 +0200

qlcplus (4.7.2) stable; urgency=low

  * DIP Switch tool: it is now possible to manually set a DIP configuration (thanks to Florian Euchner)
  * Fixture Manager: Added DIP Switch tool to fixture add panel to select an address by DIP configuration (thanks to Florian Euchner)
  * Simple Desk: fixed CPU waste and channels highlight on universes other than 1
  * Simple Desk: fixed cue stacks playback
  * Show Manager: fixed wrong track association causing copy & paste errors
  * Show Manager: fixed audio deletion crash
  * Virtual Console: Buttons can now stop all functions with a fade out time
  * Plugins/HID: fixed regression causing non working input
  * Plugins/Peperoni: added support for newer firmware versions (thanks to Jan Menzel)
  * Plugins/Peperoni: added support for 2 universes output on USBDMX21
  * Webaccess: preliminary implementation of Simple Desk
  * Webaccess: Added Web API for developers who want to control QLC+ via HTML+Javascript+Websocket
  * Added Catalan translation (thanks to Santiago Benejam Torres)
  * New fixture: Flash Butrym LED Moving Head Mini Beam 18x3W RGB CREE (thanks to laserandi)
  * New fixture: Chauvet Wedge Tri, Chauvet Gobo Zoom LED 2.0 (thanks to Jef M)
  * New fixture: Studio Due City Color 1800, Star Lights Mini Wash Moving Head (thanks to Tolmino)
  * New fixture: lightmaXX Platinum Line Mini TRI PAR 7x3W, lightmaXX Star Sphere 6 RGBAW (thanks to Flo)

 -- Massimo Callegari <<EMAIL>>  Sun, 11 May 2014 12:13:14 +0200

qlcplus (4.7.1) stable; urgency=low

  * Monitor: fixed crash when loading a project with the monitor open
  * Monitor: show channel's icon in DMX view mode
  * Monitor: added 2D view Fixture item editor. Can set position, rotation and gel color
  * Show Manager: fixed common speed mode for Sequence steps
  * Show Manager: it is no more necessary to create a Scene to add audio tracks
  * Show Manager: improved import of existing Functions
  * Show Manager: highlight the clicked step
  * Chaser Editor: Sequences in Function Manager can have Run Order and Direction
  * Function Manager: fixed nested folders failure
  * Scene Editor: fixed crash when a group used by a Scene has been removed
  * Audio/OSX: fixed crash happening at the end of playback
  * Audio/Linux: if the selected playback device is not present, fall back to the default device 
  * Webaccess: fixed Cue list play/stop sequence working only once
  * Webaccess: fixed sliders movement on a touchscreen
  * Plugins: Fixed ArtNet input universe indexing
  * Plugins: fixed ArtNet and E1.31 open/close when the same line is connected to several universes
  * Plugins: fixed ArtNet and E1.31 input association when the same line is connected to several universes
  * Plugins: implemented SPI autodetection of multiple universe channels when no fixtures are defined
  * Added fixture: Pro-Lights ARCLED7314HD (thanks to Tolmino)
  * Added fixture: American DJ Inno Spot LED (thanks to Paul Hill)
  * Added fixture: American DJ Sweeper Beam LED (thanks to George Qualley)
  * Added fixture: Contest Tri4U (thanks to Nicolas K)

 -- Massimo Callegari <<EMAIL>>  Sat, 26 April 2014 12:13:14 +0200

qlcplus (4.7.0) stable; urgency=low

  * NEW: New EFX types Square, SquareChoppy & Leaf (thanks to David Garyga)
  * NEW: Random order for chasers (Jano Svitok) 
  * Monitor: Added support for dimmer channels, Pan, Tilt, color gels and fixture labels
  * Monitor: can monitor universes even if they're not patched to an output
  * Audio Editor: added source file, speed dials and preview buttons
  * Simple Desk: highlight channels in red when manually moved
  * Fixture selection: it is now possible to add groups or a whole universe to a Scene
  * Chaser Editor: fixed some cases of manual adjustments of steps timings
  * Scene Editor: fixed copy&paste of selected values in all fixtures view mode
  * Plugins: Added enttecdmxusbopen/channels to tune the maximum number of transmitted channels for 
             Enttec Open and clones. This could solve flickering issues in some cases. (thanks to Daniel Torres)
  * Plugins: DMX USB FX5 is now supported on OSX too
  * Plugins: Added a +1 to the transmitted ArtNet universe
  * Added portuguese translation (thanks to Nuno Almeida)
  * Added fixture: Ledj Performer 18-Quad (thanks to Michael Clements)
  * Added fixture: Optima Lighting PAR64 LED (thanks to Ryan Kelley)
  * Added fixtures: Cameo Flat PAR CAN TRI 3W, Stairville maTrixx SC-100, Varytec LED Magic Mushroom (thanks to Jan Nolte)
  * Added fixture: Stairville LED bar 240/8 RGB (thanks to Mikko Toivonen)
  * Added fixture: Martin MAC 301 Wash, Martin MAC 700 Profile (thanks to Paul Evans, bwerst)
  * Added fixture: BoomToneDJ Slim PAR 7x3W LED RGB, Showtec Event Spot 1800 Q4 (thanks to Lorenzo Andreani)
  * Added fixtures: Eurolite LED PAR 56 RGB DMX, Expolite TourLed 42 (thanks to Frank Janosch)
  * Added fixture: Robe ColorSpot 700E AT (thanks to NiKoyes)
  * Added fixture: Chauvet SlimPAR TRI 12 IRC (thanks to Alessandro Grechi)
  * Added fixture: Chauvet EZPAR 64 RGBA (thanks to Ronnie Ross)
  * Added fixture: beamZ LS 3DRG (thanks to Julian Brennum)
  * Added fixtures: Chauvet Intimidator Scan LED 300, Chauvet Legend 230SR Beam (thanks to Craig Cudmore)

 -- Massimo Callegari <<EMAIL>>  Sat, 5 April 2014 12:13:14 +0200

qlcplus (4.7.0.RC1) unstable; urgency=low

  * NEW: Input/Output: QLC+ now supports infinite universes
  * NEW: Input/Output: added universe passthrough mode
  * NEW: Plugins: ArtNet and E1.31 plugins support up to 65535 universes
  * NEW: Fixture Manager: Added a dedicated dialog to quickly create RGB panels
  * NEW: Monitor: preliminary support of 2D preview monitor
  * NEW: RGBMatrix: added audio spectrum mode
  * NEW: Simple Desk: added channel groups tab
  * NEW: Plugins: Added SPI plugin (Linux only)
  * NEW: Plugins: Added FX5 support on Windows & Linux (thanks to Florian Euchner)
  * NEW: Plugins: Added Vince DMX512 USB adapter support (thanks to Jérôme Lebleu)
  * NEW: Position Tool: XY Pad-like position setting in Scene editor (Jano Svitok)
  * NEW: XY Pad shows pan/tilt coordinates (Jano Svitok)
  * NEW: XY Pad keyboard controls: Shift+arrows - fine control, Ctrl+arrows - 10x speed, Ctrl+Shift+arrows - faster fine control (Jano Svitok)
  * NEW: Fixture Manager: Fixtures channels can now be manually forced to HTP/LTP
  * Function Selection: fixed OK button greyed out and various crashes
  * MIDI plugin: Linux support for pitch wheel and note/channel aftertouch (Jano Svitok)
  * MIDI plugin: improved pitch wheel and input/output settings (thanks to Robert Scheffler)
  * Webaccess: fixed positioning of headerless frames
  * Monitor: added combo box to select which universe to monitor
  * Editors: fixed crashes when deleting running functions (thanks to plugz)
  * Plugins: added HID joysticks support on Windows
  * Documentation: Added Sound Control tutorial (Jano Svitok)
  * Added input profile: BCF2000 in Mackie Control mode - all buttons are usable (Jano Svitok)
  * Added input profile: Akai APC40 (thanks to Daniel Curry)
  * Added fixtures: DTS Max FAR, DTS Nick NRG1201 (thanks to Vasily Leushin)
  * Added fixture: AGPtek RGB 6ch Crystal Ball (thanks to Davey Daytona)
  * Added fixture: American DJ Tribar Spot2 (thanks to Rob Goudriaan)
  * Added fixture: iSolution iMove 5 Series (thanks to Dave Doyle)
  * Added fixtures: Chauvet Intimidator Beam LED 350, Spot LED 250/350 (thanks to Robert Scheffler)
  * Added fixtures: Alkalite Octopod DP-80, Eurolite LED DMX Pixel Tube 16 RGB (thanks to Ryan Gough)
  * Added fixtures: Stairville Quad Par Profile RGBW 5x8W, Stairville DDS 405 (thanks to Nicolas Rasor)
  * Added fixtures: American DJ P64 LED Plus, American DJ SP1500 MKII, Laserworld EL 200RGY, Botex DSP-405
  * Added fixture: Showtec Quatro (thanks to Florian Euchner)
  * Added fixtures: Chauvet SlimPAR 64, Chauvet Q-Spot 560-LED (thanks to Chris Laurie)
  * Added fixture: Showtec Phantom 250 Wash (thanks to Cingulingu)

 -- Massimo Callegari <<EMAIL>>  Sun, 9 March 2014 12:13:14 +0200

qlcplus (4.6.1) stable; urgency=low

  * NEW: Function Manager: it is now possible to set a startup function that is started every time QLC+ switches to operate mode
  * NEW: Clock widget can now be used to schedule a function startup
  * VC Audio triggers: fixed divisors behaviour
  * VC Slider: fixed submaster renaming
  * Engine: restored timed fade out + stop all functions functionality
  * Function selection dialog now looks like function manager (with categories and folders)
  * Show Manager: do not add a Show on popup cancel
  * Show Manager: when using an existing Scene as track create a sequence instead of a chaser
  * Fixture Manager: add quick search bar on fixture add (suggested by NiKoyes)
  * Engine: fixed Show playback not respecting fade times
  * RGB Matrix: added a whole lot of new scripts (many thanks to Plugz)
  * UI: added warning message when fixture definitions are not found during project loading
  * VC XY Pad: fixed behavior for 8bit Pan/tilt fixtures (Jano Svitok, found by Cinguligu)
  * Webaccess: fixed behaviour of buttons in solo frames
  * Virtual Console: multipage frames input associations are now contextual
  * Added input profile: KORG nano PAD (thanks to Alessandro Grechi)
  * Added fixture: Showtec ZIPP LED DMX (thanks to Marek Pleva)
  * Added fixture: Chauvet Circus 2.0 (thanks to Anthony King)
  * Added fixture: Proel Analogue Dimmer 6Ch (thanks to Lorenzo Andreani)
  * Added fixture: Ledj Tri LED back drop controller (thanks to Michael Clements)
  * Added fixture: Eurolite PST 9W RGB (thanks to Joa Palmer)
  * Added fixtures: Chauvet LED Techno Strobe RGB, Venue Scanner 4 (thanks to Davey Daytona)
  * Added fixture: American DJ Fusion FX Bar 5 (thanks to DJ Drew)
  * Added fixture: Pro-Lights LumiPAR 12Q (thanks to Alessandro Grechi)
  * Added fixture: Stairville MH-X20 (thanks to Kristo)
  * Added fixture: Showtec Indigo 4600 (Jano Svitok)

 -- Massimo Callegari <<EMAIL>>  Sun, 12 January 2014 12:13:04 +0200

qlcplus (4.6.0) stable; urgency=low

  * NEW: Plugins: Added E1.31 protocol input/output plugin
  * NEW: Virtual Console: Sliders can now be used as submasters of a frame
  * NEW: Function Manager: added folders with drag & drop functionality
  * NEW: Function Wizard has been completely rewritten
  * NEW: EFX Editor: added relative position settings (thanks to Jano Svitok)
  * NEW: MIDI plugin: added the possibility to send a SysEx initialization message on an input or output line (thanks to Joep Admiraal)
  * NEW: Added Audio fade in/out support (also in Show Manager)
  * NEW: It is now possible to control RGB Matrices intensity
  * NEW: RGB Matrices can now be converted to Sequences and imported into Shows 
  * NEW: It is now possible to control fixture heads in EFX and XY Pad (thanks to Jano Svitok)
  * NEW: Virtual Console clock can be used also as a stopwatch or a countdown
  * Virtual Console: fixed audio triggers incorrect loading
  * Input/Output: improved audio device selection
  * Audio Input: fixed crash occurring on Linux for unsupported audio devices
  * Fixture Editor: set previously selected color when editing a color macro
  * Fixture Remap: improved remapping of fixtures with of the same type
  * Fixture Manager: refresh fixtures list when loading a project on startup
  * RGB Matrix Editor: fixed crash when opening a project with the RGB Matrix editor open
  * Package: Fixed installation of RGB scripts and Windows associations (thanks to Jano Svitok)
  * Documentation: reviewed and turned into proper english (thanks to Michael Clements)
  * Web access: improved to reflect the live situation of the Virtual Console
  * Virtual Console: fixed widgets paste into a multipage frame
  * XY Pad: when focused, can be controlled with arrow keys (thanks to Alex Paul)
  * Added fixtures: Pro-Lights LedPar 64363 RGB, Pro-Lights LumiPar 7QPro, Pro-Lights Strobo 1500DMX (thanks to Lorenzo Andreani)
  * Added fixtures: Pro-Lights Explo 3000D, Pro-Lights LumiPar 18 Tri, Robe_ColorMix_240_AT (thanks to Lorenzo Andreani)
  * Added fixtures: American DJ Mega Tri Par, Clay Paky A.LEDA Wash K5, Clay Paky A.LEDA Wash K10, Clay Paky A.LEDA Wash K20
  * Added fixtures: American DJ Sweeper Beam Quad LED, American DJ Ultra Bar 6/9/12, American DJ Inno Color Beam Quad 7
  * Added fixture: American DJ Inno Color Beam LED (thanks to Louis Gutenschwafe)
  * Added fixtures: American DJ/Elation Event Bar LED, American DJ Micro Wash RGBW (thanks to George Qualley)

 -- Massimo Callegari <<EMAIL>>  Thu, 19 December 2013 12:13:04 +0200

qlcplus (4.5.1) stable; urgency=low

  * NEW: Virtual Console: audio triggers can now control Speed Dials tapping (thanks to Jano Svitok)
  * NEW: Virtual Console: added clock widget
  * NEW: RGB Matrix now supports 2 colors
  * NEW: Scene Editor: color selection tool live tracking (thanks to Stefan Riemens)
  * Live Edit: Improved live editing of Scenes and Chasers
  * Fixtures remapping: added a button to clone and autopatch a fixture from the source list
  * Function selection: added radio buttons to show all or only the running functions
  * Virtual Console: fixed regression on buttons name auto assign
  * Virtual Console: fixed multi page functionality for solo frames
  * Virtual Console: fixed audio triggers thresholds save/load on projects (thanks to Jano Svitok)
  * Virtual Console: fixed knobs rendering on OSX
  * Virtual Console: fixed crash when enabling multipage on headerless frames
  * Virtual Console: fixed copy of Click & Go feature when cloning sliders/knobs 
  * MIDI plugin: fixed beat clock detection on OSX
  * Show Manager: Improved to fit on a 1024x768 resolution
  * Engine: fixed Collection potential crash (thanks to Jano Svitok)
  * Engine: fixed Scene fade out when only one function is running
  * Engine: fixed end of audio playback not notified
  * Engine: fixed audio capture limited signal on OSX
  * Simple Desk: universe reset is now performed also on non intensity channels
  * Simple Desk: channels are now updated in full view mode too
  * Added fixture: Chauvet 4Play (thanks to Ryan Schaefer)
  * Added fixtures: American DJ Fusion FX Bar 1, Chauvet Intimidator Scan LED 200 (thanks to synkajax)
  * Added fixtures: Stairville PAR64 CX-3 RGBW, Stairville TRI LED Bundle Complete (thanks to Lars Kildholt)
  * Added fixture: Monacor CPL-3DMX (thanks to Marcus)
  * Added fixtures: Pro-Lights CromoSpot 150, Evolight Colours SL3 (thanks to Lorenzo Andreani)

 -- Massimo Callegari <<EMAIL>>  Tue, 5 November 2013 12:13:04 +0200

qlcplus (4.5.0) stable; urgency=low

  * NEW: Functions now have attributes. They can be controlled by right clicking on a Virtual Console button in operate mode
  * NEW: Added multi-page support to Virtual Console frames
  * NEW: DMX dump can now set a Scene on a Virtual Console button or slider
  * NEW: Added Live Edit functionality to modify functions while in operate mode
  * NEW: added Czech translation (thank to Jan Lachman)
  * NEW: QLC+ GUI can be customized with an external CSS-like style file (see documentation)
  * NEW: MIDI plugin can now receive Program Change and MIDI beat clock
  * NEW: Audio triggers turned to a Virtual Console widget. No backward compatibility. Please update your projects.
  * NEW: In Fixture Editor, it is now possible to copy/paste physical information
  * NEW: "Stop all functions" now has 4 presets (1s, 5s, 10s, 30s) to fade out intensity channels before stopping
  * NEW: It is now possible to set a working range window in Virtual Console XY Pads (all credits for the CTK range slider go to the Common Toolkit team)
  * NEW: Audio input and output card can be selected from Input/Output panel
  * EXPERIMENTAL: added web interface to run QLC+ on a headless system. Default port is 9999. Run QLC+ with '-w' option to enable web access.
  * Cue List "Stop" input turned into "Playback", to handle start/stop of a cue with one key
  * Improved Fixture Editor add mode channels procedure, to reduce fixture creation time
  * Fixed channel groups remapping
  * Updated Spanish translation (thank to Luis García-Tornel)
  * Added Tap keyboard mapping to Speed Dial widget
  * Fixed Show Manager viewport scaling considering audio tracks too
  * Slider Matrix can now have a custom width
  * Fixed VC slider external control when minimum value is not 0
  * Fixed dutch translation preventing to load pictures on Fixture Editor
  * Fixed Virtual Console knobs with Click & Go functionality
  * Added fixtures: American DJ Fog Storm (with FS-DMXT Remote Timer), Stellar Labs LED PAR38 RGB (thanks to X)
  * Added fixtures: American DJ Accu Spot Pro, Scanic Astute LED 108, Showtec Mini Max LED RGB, Showtec Phantom 250, lightmaXX LED MEGA PAR64 RGBW 18x8W (thanks to laserandi)
  * Added fixture: Eliminator Lighting Electro MH25R (thanks to Travis Cook)
  * Added fixtures: Geni OBY 5, Geni OBY 600, Griven Kolorado 2500, Coemar ProWash 250 LX (thanks to Goran)
  * Added fixtures: Martin JEM ZR44, Briteq W07L12 (thanks to Joep Admiraal)
  * Added fixture: Stairville LED BAR RGB 252 (thanks to Ralf Herold)
  * Added fixture: Showtec Acrobat (thanks to OddSocks)

 -- Massimo Callegari <<EMAIL>>  Sat, 12 October 2013 12:13:04 +0200

qlcplus (4.4.1) stable; urgency=low

  * NEW: Added Fixture remapping functionality. It is possibile to perform 1-to-1 or 1-to-many remapping
  * NEW: In Show Manager, tracks can be moved up or down
  * SceneEditor improvement: when checking a channel group, fixtures are automatically added and channels enabled/disabled
  * Fade out time is now considered out of a step duration
  * Fixed crash when using knobs in percentage mode
  * Fixed copy of a knob resulting in a slider again
  * Fixed Audio Trigger Factory multiple enable issue
  * Fixed most of the VC Cue List crossfade wrong behaviours
  * VC Cue list intensity can now be set before playing
  * Fixtures panel and Function Selection panel now remember their state (thanks to Jano Svitok)
  * ArtNet plugin listens to broadcast address. This makes Enttec ODE to work also on Linux and Windows
  * Fixtures groups are now saved when exporting fixtures lists
  * Improved audio volume functionality
  * Saved a lot of CPU usage during Show playback from the Show panel
  * Click & Go RGB/CMY can now be set "live" when holding the mouse left button
  * Improved OSC plugin to handle mixed data (like from PureData OSC)
  * Fixed sequence incorrect start time when dragging it
  * Added dutch translation (thanks to Raymond Van Laake)
  * Added fixture: QTX SL Series (thanks to Tom Lincoln)
  * Added fixture: Briteq BT W12L10 (thanks to Joep Admiraal)
  * Added fixture: Chauvet COLORdash Batten (thanks to Kurt Malkames)
  * Added fixture: Stairville SC-100 (thanks to Lars Kildholt)
  * Added fixtures: Contest LedColor, Dune Lighting MOVLED60W (thanks to Nicolas K)
  * Added fixtures: Lanta Fireball P64s, QTX SL [153.778], KAM LED PartyBar (thanks to Brian Ely)
  * Added fixtures: Stairville MH X25, Stairville SF 1500, Pro-Lights LumiPAR18QPRO (thanks to Veljko Stankov)
  * Added fixture: Stairville xBrick Full Colour (thanks to fintch)

 -- Massimo Callegari <<EMAIL>>  Thu, 29 August 2013 12:13:04 +0200

qlcplus (4.4.0) stable; urgency=low

  * NEW: Virtual Console sliders can now be displayed as knobs
  * Siders style improved to get closer to real consoles appearance
  * Fixed empty Sequence steps save/load causing QLC+ (and the operator) to freak out
  * Inverted VC Cue list "next step" slider to reflect external controllers faders
  * Added feedbacks to VC Cue List crossfade sliders
  * VC Cue List manual crossfade when sliders are not linked now goes to next cue when sliders reach their opposite ends
  * Reorganized documentation and introduced more user friendly guides (thanks to Jano Svitok)
  * Fixed potential crash when closing QLC+ and not using Audio input triggers
  * Fixed Show Manager sequence creation not updating Function Manager tree correctly
  * Fixed Function Manager sequence deletion removing also its parent Scene
  * Fixture Editor is now shiny and colorful (as someone said)
  * Changed function copy name to respect alphabetic order. Now it's "Function #xx (Copy)"
  * Updated german and french translations (thanks to Heiko and NiKoyes)
  * Added Fixture: Blizzard Lighting Blade RGBW (thanks to James Elliott)
  * Added Fixture: Stairville MH-X50+ (thanks to Guillaume Rousseau)

 -- Massimo Callegari <<EMAIL>>  Tue, 5 July 2013 17:03:04 +0200
  
qlcplus (4.4.0.beta1) unstable; urgency=low

  * NEW: Shows can now be started at any position in time
  * NEW: Audio files can now be added and inspected from Function Manager. They can also be added to Chasers, to create Audio Cues
  * NEW: In Scene Editor, fixture channels can be selected (CTRL+Click) for advanced copy/paste
  * NEW; Audio input support. It is now possible to associate DMX channels, functions or VC widgets to audio volume and spectrum bars
  * NEW: VC Cue list crossfade functionality. It is now possible to open a side panel with two sliders to manually control the transition between two chaser steps
  * Scene Editor now uses global clipboard too, so values can be copied from a Scene to another
  * Copy and paste is now allowed in "all fixtures view" mode too
  * Project loading speed improvement. Should be much faster on projects with lot of fixtures
  * Fixed regression of Chaser Editor paste operation, causing a major drop of performances
  * Fixed regression introduced with the hold parameter, causing "infinite" value wrong behaviour
  * Fixed 'hold' setting changed from a speed dial for RGBMatrix, EFX and Simple Desk Playback Cue
  * Fixed loading of empty sequence steps when all values are set to zero
  * Chasers can now be started/tested from any step
  * Virtual Console size is now taken into account when loading a project (including kiosk mode)
  * Scene Editor now remembers the fixtures view mode (not saved into project)
  * Fixed QLC+ 4.0.0 regression causing Function Manager to reset the tree state on show/hide
  * Fixed ArtNet input never reaching zero value (thanks to Jano Svitok)
  * Fixed MIDI feedback corrupting non zero channels (thanks to Jano Svitok)
  * Fixed EFX editor start offset overflow (thanks to Jano Svitok)
  * Added Sequences/Audio creation overlapping check on Show Manager
  * Audio functions now support volume adjustment. Not yet on Chasers though.
  * Added fixture: American DJ Mega Bar 50RGB (thanks to George Qualley)
  * Added fixture: SGM Giotto Spot 400 CMY (thanks to Willy)
  * Added fixture: PixelRANGE PixelPar 44 (thanks to bwerst)
  * Added fixture: Look Solutions Unique 2.1 (thanks to Nicolas K)
  * Added fixture: American DJ TRI64 Profile (thanks to Jed Nou)

 -- Massimo Callegari <<EMAIL>>  Tue, 18 June 2013 4:03:04 +0200

qlcplus (4.3.3) stable; urgency=low

  * NEW: Added a window to exclude the desired fixture channels from fade transitions (purple icon)
  * NEW: Added global clipboard to copy & paste steps between Chasers. Show manager allows also to copy tracks, sequence and audio items between different Shows
  * NEW: It is now possible to set Virtual Console widgets default size, button status style and speed dial default value
  * NEW: Added Click And Go functionality for CMY fixtures
  * NEW: Added list of recent project files (long press open icon to see it)
  * NEW: It is now possible to add notes to Chaser steps (either from Chaser Editor or Virtual Console Cue List)
  * NEW: Added "Snap to Grid" functionality to Show manager
  * NEW: Added "Align to cursor" option to Sequences and Audio item in Show Manager
  * NEW: Show time position when dragging a Show Manager object  
  * Gives more time to DMXking devices to respond to identification calls
  * When adding an existing Scene into Show Manager, a 10seconds Sequence is now created by default with the Scene values
  * Fixed crash when changing a fixture address or manufacturer
  * Fixed crash on Show Editor when displaying a deleted sequence
  * Fixed Show Manager playback nasty bug preventing to play sequences/audio in the right order
  * Fixed Show Manager playback crash after deleting an audio track
  * Fixed enable/disable all channels of Scene Editor "all channels view" mode
  * Fixed Virtual Console buttons conflict between flash and manual on/off (thanks to Jano Svitok)
  * Improved Click & Go preset widget to fit on the screen (thanks to Jano Svitok)
  * Added total Show duration in Show Editor
  * Added fixtures: American DJ 64B LED PRO, American DJ Profile Panel RGB
  * Added fixture: Chauvet Gyser RGB (thanks to George Qualley)
  * Added fixture: Martin MAC250 Entour (thanks to Jannik Dam Kehlet)
  * Added fixtures: American DJ Accu UFO, American DJ Double Phase, American DJ FREQ 16 Strobe, American DJ Quad Phase, American DJ Revo4 256 CH, Elation EVC MH LED (thanks to David Kuder)

 -- Massimo Callegari <<EMAIL>>  Tue, 27 Apr 2013 12:12:42 +0200

qlcplus (4.3.2) stable; urgency=low

  * NEW: Added "Hold" parameter to Chaser steps, between FadeIn and FadeOut
  * NEW: Added address tool to quickly preview a DIP switch configuration
  * NEW: OSC plugin can now send data. Either output or feedback
  * NEW: XY Pad Y-Axis inverted appearance option
  * NEW: XY Pad now sends feedbacks
  * Fade is no more applied to Pan, Tilt and Speed channels
  * Fixture address overlapping is no more permitted
  * Fixed copy of a Show. Now it copies also all the contents
  * Fixed crash when clicking on a Chaser step when Scene Editor "all channels" view is active
  * Fixed crash happening on Show playback
  * Improved Show Manager opening speed
  * Sequence items in Show Manager now highlight the selected Sequence step
  * Fixed Show Manager vertical resize when adding a big number of tracks
  * EFX improvements. Direction parameter is now taken into account (thanks to Jano Svitok)
  * Virtual Console Button icons improvements (thanks to Jano Svitok)
  * Virtual Console Frame new options: show/hide header, title font/color, set title with double click
  * Improved binary address display in fixture information panel
  * Added fixture: Eurolite LED Z-200 Beam effect (thank to Heiko Fanieng)
  * Added fixtures: American DJ Vizi Beam 5R, Martin MAC 101 (thanks to Daniel Sevelt)
  * Added fixture: Lanta Orion Link V2 (thanks to OddSocks)
  * Added fixtures: Rosco I-Cue, Ledj Colour Storm Quad, Ledj Alu Quad PAR64 (thanks to Michael Clements)
  * Added fixtures: American DJ Hypnotic RGB, Blizzard Lighting Puck Fab5, Blizzard Lighting Snowball (thanks to James Elliott)
  * Added fixtures: Generic RGB, Robe Scan 575 XT, Robe Wash 575 XT, Robe Robin 600 LED Wash, Robe Robin 600E Beam, Robe Robin 600E Spot, Robe Robin 600E Wash (thanks to NiKoyes)
  * Added fixture: High End Systems Studio Command 1200 (thanks to Tony Schuite)
  * Added fixtures: High End Systems Technobeam, High End Systems Trackspot, High End Systems Xspot Xtreme, ReelEfx DF-50 (thanks to Louis Gutenschwager)
  * Added fixtures: Mac Mah Mac Follow 1200, Proel Tarkus 575 Spot, Showtec Sunstrip Active (thanks to Julien Poilvilain)
  * Improved fixture: Elation Platinum Spot 5R (thanks to George Qualley)

 -- Massimo Callegari <<EMAIL>>  Wed, 10 Apr 2013 12:12:42 +0200

qlcplus (4.3.1) stable; urgency=low

  * Fixed DMXking devices detection
  * Improved DMX4ALL devices information
  * Fixed crash happening on OSX when opening plugin's configuration panels
  * Channel Groups order can now be changed in the Fixture Manager panel
  * Fixture Manager can now remove multiple Channel Groups
  * Sliders labels are now multi line (applies to Channel Groups and Virtual Console sliders)
  * Scene Editor now remembers the Channel Groups values. Not saved in the project.
  * Scene Editor has a new button to switch between "tab view" and "all channels view"
  * Scene Editor now shows all fixtures tabs on the screen. In case they're a lot, it will display "..." beside the name
  * On Scene Editor it is now possible to switch betwwen tabs by pressing Alt+Left, Alt+Right
  * On Scene Editor it is now possible to switch between channels values by pressing TAB
  * various fixes to avoid a crash when deleting Channel Groups
  * Added fixture: Stairville MH-360 (thanks to Jannis Achstetter)
  * Documentation updates

 -- Massimo Callegari <<EMAIL>>  Mon, 25 Mar 2013 12:12:42 +0200

qlcplus (4.3.0) stable; urgency=low

  * NEW: introduced the Click & Go technology to quick access fixtures capabilities such as gobos, effects, colours, RGB
  * NEW: added DMX dump functionality, to save the current values into a new Scene and Chaser (Shortcut: CTRL+D)
  * NEW: added support for Enttec DMX USB PRO Mk2 (thanks to Tim for providing it)
  * NEW: added support for DMXKing ultraDMX Pro (device provided by the DMXking team. Thanks)
  * NEW: added support for DMX4ALL USB-DMX adapters (tested with STAGE-PROFI MK2. Thanks to Heiko Fanieng for providing it)
  * NEW: Sliders can now jump directly to the clicked position
  * NEW: Implemented BPM view for Show Manager
  * NEW: Color selection tool now works also with channel groups
  * NEW: Added Grand Master reverse functionality
  * Fixed fast stop/start functions sequence, causing wrong behaviour (thanks to Heikki Junnila (yes!) for suggesting the solution)
  * QLC+ and Fixture Editor now remember the working directory
  * Input profiles can now be used with no input device connected
  * Fixed crash when loading a new project from a project with a Show
  * Added fixtures: Eurolite TMH-8, Showtec LED Par 56, Showtec MultiDim MK III DMX (thanks to Heiko Fanieng)
  * Added fixtures: Clay Paky Atlas, Clay Paky Beam 300, Proel Dreamlight 250 Eclipse Spot, Proel Moving Head 3Wx108 LED RGBW, Varytec RGB LED Spot 38, SGM Idea Spot 575

 -- Massimo Callegari <<EMAIL>>  Mon, 18 Mar 2013 12:12:42 +0200

qlcplus (4.2.2) stable; urgency=low

  * Rewritten OSX audio renderer code. Tested on various OSX versions and should finally work fine!
  * Fixed crash when ArtNet plugin is in output mode and receives DMX data
  * Fixed crash when changing ArtNet configuration
  * Fixed ArtNet plugin output on OSX (big thanks to David Geldreich)
  * Fixed Scene changes not updating Virtual Console Cue List
  * Fixed wrong input/output mapping that leaded to a potential crash
  * On Simple Desk reset, restore also page label to 1
  * Fixed crash when deleting a Chaser from Function Manager
  * Windows installer now remembers last installation path (thanks to Pixeldoc)
  * Fixes and improvements of documentation

 -- Massimo Callegari <<EMAIL>>  Tue, 26 Feb 2013 17:00:42 +0200

qlcplus (4.2.1) stable; urgency=low

  * NEW: Added ArtNet input support (tested on Android with Art-Net controller LITE and Controlador Artnet-DMX)
  * NEW: It is now possible to import/export fixtures to a separate file (.qxfl) to shorten the project creation
  * NEW: Added page change feature for Enttec Wing (thanks to Thomas Achtner)
  * NEW: Channel Groups can be associated to a Scene and saved into the project
  * Fixed project enconding issue for special characters (happening on Windows)
  * Added Fade In/Fade Out/Duration columns to Virtual Console Cue List widget (thanks to Thomas Achtner)
  * Fixed outbound limits of Cue Lists steps. Now Previous/Next operations will always stay in the list
  * Improved Virtual Console Cue List with more manual controls (Play/Stop/Previous/Next)
  * Since QLC+ is polite, it now responds to ArtNet Poll requests
  * Improved ArtNet configuration panel when an interface is no longer available
  * Show only available Inputs/Outputs on Input/Output Manager
  * Improved Virtual Console Slider groups selection (level mode) by adding colours channels
  * Added toolbar icon to Scene Editor and Chaser Editor to Show/Hide Speed Dials window (default: hidden)
  * Iconified Play/Stop/Previous/Next buttons to test a Chaser
  * Some more UI look & feel improvements (icons, layout, usability)
  * Added fixture: Showtec Phantom 50 (thanks to Petja Touru)
  * Added fixture: Chauvet ColorBand Pix IP (thanks to George Qualley)
  * Added fixture: American DJ Mega Tri Bar (thanks to CShark)

 -- Massimo Callegari <<EMAIL>>  Fri, 15 Feb 2013 13:00:42 +0200

qlcplus (4.2.0) stable; urgency=low

  * NEW: added ArtNet plugin. No extra software needed. And it works on Windows.
  * NEW: Simple Desk can now control all the universes
  * Virtual Console Frame collapsed state is now saved on project XML
  * Virtual Console Solo Frames can now be collapsed/expanded too
  * Fixed OSC input names
  * Fixed fixture address calculation when adding a new fixture, especially for universes other than 1
  * Improved Chaser Editor UI. Now more compact and suitable for lower resolutions
  * Improved Fixture Add UI. Values can now be expanded
  * Added fixture: Chauvet Intimidator Spot 250 (thanks to Henning Borgen)
  * Added fixture: Stairville Matrixx FL 110 DMX (thanks to Heiko Fanieng)
  
 -- Massimo Callegari <<EMAIL>>  Tue, 29 Jan 2013 18:17:42 +0200

qlcplus (4.1.3) stable; urgency=low

  * WARNING: Major rename. QLC+ now uses "qlcplus" for everything (configuration, libraries, binaries, ...)
  * fixed speed dial input source auto detection
  * added XY pad external input control (including OSC detection)
  * Simple Desk sliders style improved and synchronized with QLC+ DMX values
  * Virtual Console Frames can now be collapsed/expanded and entitled
  * improved stability of projects loading
  * updated german translation (thanks to Heiko Fanieng)
  * added fixture: Showtec Atmos F350 (thanks to Jano Svitok)
  * added fixture: Showtec LED Light Bar v3 (thanks to Jannis Achstetter)
  * added fixtures: Futurelight MH660, Eurolite LED Par64 RGBA, Stairville LED Par56 MKII RGBA, Stairville LED Par56 MKII RGBW (thanks to Oliver Ruempelein)

 -- Massimo Callegari <<EMAIL>>  Tue, 12 Jan 2013 12:46:42 +0200

qlcplus (4.1.2) unstable; urgency=low

  * Tracks can now be deleted from the Show editor
  * OSC plugin now supports multiple universes (up to 4)
  * added white colour intensity
  * fixed channels group display for partial fixtures modes
  * Implemented external input control for channels groups. Available in Design mode only
  * Improved Input/Output configuration display for lower screen resolutions
  * Inputs and Outputs can now be reset to "none" (thanks to Jano Svitok)
  * fixed Enttec Playback Wing input line connection (thanks to offtools for reporting)
  * fixed a little typo on Fixture author name
  * updated UI german translation (thanks to Heiko Fanieng)
  * updated french translation (thanks to NiKoyes)

 -- Massimo Callegari <<EMAIL>>  Tue, 8 Jan 2013 22:12:42 +0200

qlcplus (4.1.1) unstable; urgency=low

  * added OSC plugin. Input only at the moment
  * added TouchOSC Mix16 input profile, as an example to use OSC
  * fixed OSX DMG package creation (sorry about this)

 -- Massimo Callegari <<EMAIL>>  Thu, 20 Dec 2012 11:35:42 +0200

qlcplus (4.1.0) unstable; urgency=low

  * MAJOR: added audio tracks support in the Show Manager. It is also possible to preview the waveform by right clicking on an audio track
  * MIDI output and feedbacks now work again
  * unified Input/Output panels to have a complete view of devices/profiles and feedbacks
  * chaser steps can now be edited by double clicking on them in the Chaser Editor panel
  * fixed crash when opening a project with empty shows and/or empty tracks
  * improved rendering accuracy of Sequences and Audio items
  * OLA plugin is back to work
  * Windows installer refurbished for a modern look
  * Velleman support back to work on Windows
  * added a useful debug window for OSX and Windows
  * improved docs
  * added KORG nanoKONTROL 2 input profile (thanks to Jannis Achstetter)
  * added 9 new fixtures thanks to the QLC forum users: American-DJ-Par-38-Pro, Chauvet-COLORpalette, Chauvet-Slim-Par-Pro-RGBA, Chauvet-Slim-Par-Pro-VW,  Clay-Paky-Mini-Scan-HPE, Eurolite-LED-Bar-RGB-252_10_40__Indoor, HQ-Power-Power-Space-250, Litecraft-PowerBar-4, Showtec-Indigo-6500

 -- Massimo Callegari <<EMAIL>>  Sun, 16 Dec 2012 17:07:42 +0200

qlcplus (4.0.2) unstable; urgency=low

  * improved Scene channels save/load (please review your QLC+ projects)
  * fixed signals connections between Chaser Editor and Scene Editor in multitrack view
  * fixed cut/copy/paste for chaser steps
  * fixed clone function for a Sequence
  * enabled clone function in the Show Manager
  * Function wizard show an error popup on unsupported capabilities
  * cosmetic improvements of Channels Groups view
  * started to update Help docs

 -- Massimo Callegari <<EMAIL>>  Tue, 27 Nov 2012 15:31:42 +0200

qlcplus (4.0.1) unstable; urgency=low

  * Fixed channels groups for generic dimmers
  * Fixed multitrack view cursor accuracy. Now smoother as it should be.
  
 -- Massimo Callegari <<EMAIL>>  Wed, 22 Nov 2012 22:39:42 +0200

qlcplus (4.0.0) unstable; urgency=low

  * Added offline desk capability. A brand new multitrack view has 
    been added to allow disposition of scenes and sequences very easily
  * Added channels groups
  * Scene editor will be displayed always on screen bottom. Should be easier to find a Fixture now
  * when playing a Chaser, steps are highlighted accordingly to playback
  * Improved UI look & feel mostly with KDE's Humanity icon package
  * QLC forked on November 5th 2012

 -- Massimo Callegari <<EMAIL>>  Wed, 20 Nov 2012 10:44:42 +0200 
