
add_executable(artnet_test WIN32 MACOSX_BUNDLE
    ../../interfaces/qlcioplugin.cpp ../../interfaces/qlcioplugin.h
    ../../interfaces/rdmprotocol.cpp ../../interfaces/rdmprotocol.h
    ../src/artnetpacketizer.cpp
    artnet_test.cpp artnet_test.h
)
target_include_directories(artnet_test PRIVATE
    ../../interfaces
    ../src
)

target_link_libraries(artnet_test PRIVATE
    # Remove: L../src
    Qt${QT_MAJOR_VERSION}::Core
    Qt${QT_MAJOR_VERSION}::Network
    Qt${QT_MAJOR_VERSION}::Test
    artnet
)

# Consider using qt_generate_deploy_app_script() for app deployment if
# the project can use Qt 6.3. In that case rerun qmake2cmake with
# --min-qt-version=6.3.
