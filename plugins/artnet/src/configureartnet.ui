<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  configureartnet.ui

  Copyright (c) 2015 Massimo Call<PERSON>ari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>ConfigureArtNet</class>
 <widget class="QDialog" name="ConfigureArtNet">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Configure ArtNet Plugin</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>Universes Configuration</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Configuration of the patched universes</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTreeWidget" name="m_uniMapTree">
         <column>
          <property name="text">
           <string>Interface</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Universe</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>IP Address</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>ArtNet Universe</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Transmission Mode</string>
          </property>
         </column>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>Seconds to wait for an interface to be ready</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="m_waitReadySpin">
           <property name="sizePolicy">
            <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>Nodes Tree</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QTreeWidget" name="m_nodesTree">
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <column>
          <property name="text">
           <string>IP</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Short Name</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Long Name</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="m_buttonBox">
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>ConfigureArtNet</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>162</x>
     <y>75</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>ConfigureArtNet</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>266</x>
     <y>79</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
