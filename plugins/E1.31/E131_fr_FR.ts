<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="fr_FR">
<context>
    <name>ConfigureE131</name>
    <message>
        <location filename="configuree131.ui" line="33"/>
        <source>Configure E1.31 Plugin</source>
        <translation>Configuration du plugin E1.31</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="43"/>
        <source>Universes Configuration</source>
        <translation>Configuration des univers</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="56"/>
        <source>Interface</source>
        <translation>Interface</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="66"/>
        <source>Multicast</source>
        <translation>Multicast</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="71"/>
        <source>IP Address</source>
        <translation>Addresse IP</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="76"/>
        <source>Port</source>
        <translation>Port</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="81"/>
        <source>E1.31 Universe</source>
        <translation>Univers E1.31</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="86"/>
        <source>Transmission Mode</source>
        <translation>Mode de transmission</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="91"/>
        <source>Priority</source>
        <translation>Priorité</translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="101"/>
        <source>Seconds to wait for an interface to be ready</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="configuree131.ui" line="61"/>
        <source>Universe</source>
        <translation>Univers</translation>
    </message>
    <message>
        <location filename="configuree131.cpp" line="99"/>
        <source>Inputs</source>
        <translation>Entrées</translation>
    </message>
    <message>
        <location filename="configuree131.cpp" line="105"/>
        <source>Outputs</source>
        <translation>Sorties</translation>
    </message>
    <message>
        <location filename="configuree131.cpp" line="189"/>
        <source>Full</source>
        <translation>Complète</translation>
    </message>
    <message>
        <location filename="configuree131.cpp" line="190"/>
        <source>Partial</source>
        <translation>Partielle</translation>
    </message>
    <message>
        <location filename="configuree131.cpp" line="198"/>
        <source>%1 - min, %2 - default, %3 - max</source>
        <translation>%1 - min, %2 - défaut, %3 - max</translation>
    </message>
    <message>
        <location filename="configuree131.cpp" line="245"/>
        <source>Invalid IP</source>
        <translation>IP invalide</translation>
    </message>
    <message>
        <location filename="configuree131.cpp" line="245"/>
        <source>%1 is not a valid IP.
Please fix it before confirming.</source>
        <translation>%1 n&apos;est pas une IP valide.
Veuillez la corriger avant de valider.</translation>
    </message>
</context>
<context>
    <name>E131Plugin</name>
    <message>
        <location filename="e131plugin.cpp" line="97"/>
        <source>This plugin provides DMX output for devices supporting the E1.31 communication protocol.</source>
        <translation>Ce plugin offre le support des périphériques supportant le protocole E1.31.</translation>
    </message>
    <message>
        <location filename="e131plugin.cpp" line="144"/>
        <source>Output</source>
        <translation>Sortie</translation>
    </message>
    <message>
        <location filename="e131plugin.cpp" line="148"/>
        <location filename="e131plugin.cpp" line="287"/>
        <source>Status: Not open</source>
        <translation>Status : fermé</translation>
    </message>
    <message>
        <location filename="e131plugin.cpp" line="151"/>
        <location filename="e131plugin.cpp" line="290"/>
        <source>Status: Open</source>
        <translation>Status : ouvert</translation>
    </message>
    <message>
        <location filename="e131plugin.cpp" line="153"/>
        <source>Packets sent: </source>
        <translation>Paquets envoyés : </translation>
    </message>
    <message>
        <location filename="e131plugin.cpp" line="283"/>
        <source>Input</source>
        <translation>Entrée</translation>
    </message>
    <message>
        <location filename="e131plugin.cpp" line="292"/>
        <source>Packets received: </source>
        <translation>Paquets reçus : </translation>
    </message>
</context>
</TS>
