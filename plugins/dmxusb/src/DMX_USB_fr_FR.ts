<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="fr_FR">
<context>
    <name>DMXUSB</name>
    <message>
        <location filename="dmxusb.cpp" line="152"/>
        <source>This plugin provides DMX output support for</source>
        <translation>Ce plugin offre le support des interfaces</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="156"/>
        <source>and compatible devices.</source>
        <translation>et des interfaces compatibles.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="170"/>
        <source>No output support available.</source>
        <translation>Support de la sortie indisponible.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="172"/>
        <source>Make sure that you have your hardware firmly plugged in. NOTE: FTDI VCP interface is not supported by this plugin.</source>
        <translation>Assurez vous que le périphérique est bien connecté.
NOTE : L&apos;interface VCP FTDI n&apos;est pas supportée par ce plugin.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="181"/>
        <location filename="dmxusb.cpp" line="280"/>
        <source>Device is operating correctly.</source>
        <translation>L&apos;interface fonctionne correctement.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="183"/>
        <location filename="dmxusb.cpp" line="282"/>
        <source>Driver in use: %1</source>
        <translation>Pilote utilisé : %1</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="267"/>
        <source>No input support available.</source>
        <translation>Support de l&apos;entrée indisponible.</translation>
    </message>
</context>
<context>
    <name>DMXUSBConfig</name>
    <message>
        <location filename="dmxusbconfig.cpp" line="47"/>
        <source>Refresh</source>
        <translation>Rafraichir</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="48"/>
        <source>Close</source>
        <translation>Fermer</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Serial</source>
        <translation>N° de série</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Mode</source>
        <translation>Mode</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Output frequency</source>
        <translation>Fréquence de sortie</translation>
    </message>
</context>
<context>
    <name>DMXUSBOpenRx</name>
    <message>
        <location filename="dmxusbopenrx.cpp" line="124"/>
        <source>Protocol</source>
        <translation>Protocole</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="130"/>
        <source>Stopped</source>
        <translation>À l&apos;arrêt</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="132"/>
        <source>Idling</source>
        <translation>En pause</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="134"/>
        <source>Calibrating</source>
        <translation>Calibration</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="136"/>
        <source>Receiving</source>
        <translation>Réception</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="138"/>
        <source>Receiver state</source>
        <translation>État du récepteur</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="143"/>
        <source>Received DMX Channels</source>
        <translation>Canaux DMX Reçus</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="148"/>
        <source>DMX Frame Frequency</source>
        <translation>Fréquence de trame DMX</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="154"/>
        <source>Bad</source>
        <translation>Mauvaise</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="156"/>
        <source>Good</source>
        <translation>Bonne</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="158"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation>Patchez ce plugin à un univers pour le découvrir.</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="160"/>
        <source>System Timer Accuracy</source>
        <translation>Précision de l&apos;horloge système</translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBOpen</name>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="116"/>
        <source>Protocol</source>
        <translation>Protocole</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="121"/>
        <source>DMX Channels</source>
        <translation>Canaux DMX</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="124"/>
        <source>DMX Frame Frequency</source>
        <translation>Fréquence de trame DMX</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="128"/>
        <source>Bad</source>
        <translation>Mauvaise</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="130"/>
        <source>Good</source>
        <translation>Bonne</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="132"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation>Patchez ce plugin à un univers pour la découvrir.</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="133"/>
        <source>System Timer Accuracy</source>
        <translation>Précision de l&apos;horloge système</translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBPro</name>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="115"/>
        <location filename="enttecdmxusbpro.cpp" line="117"/>
        <source>Protocol</source>
        <translation>Protocole</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="120"/>
        <source>Manufacturer</source>
        <translation>Fabricant</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="122"/>
        <source>Serial number</source>
        <translation>N° de série</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="171"/>
        <location filename="nanodmx.cpp" line="236"/>
        <location filename="stageprofi.cpp" line="141"/>
        <location filename="vinceusbdmx512.cpp" line="233"/>
        <source>Protocol</source>
        <translation>Protocole</translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="178"/>
        <location filename="nanodmx.cpp" line="243"/>
        <location filename="stageprofi.cpp" line="148"/>
        <location filename="vinceusbdmx512.cpp" line="237"/>
        <source>Serial number</source>
        <translation>N° de série</translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="173"/>
        <location filename="nanodmx.cpp" line="238"/>
        <location filename="stageprofi.cpp" line="143"/>
        <location filename="vinceusbdmx512.cpp" line="235"/>
        <source>Output</source>
        <translation>Sortie</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="126"/>
        <location filename="enttecdmxusbopen.cpp" line="118"/>
        <location filename="euroliteusbdmxpro.cpp" line="175"/>
        <location filename="nanodmx.cpp" line="240"/>
        <location filename="stageprofi.cpp" line="145"/>
        <source>Manufacturer</source>
        <translation>Fabricant</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="325"/>
        <source>MIDI Input</source>
        <translation>Entrée MIDI</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="327"/>
        <source>DMX Input</source>
        <translation>Entrée DMX</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="332"/>
        <source>MIDI Output</source>
        <translation>Sortie MIDI</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="334"/>
        <source>DMX Output</source>
        <translation>Sortie DMX</translation>
    </message>
</context>
</TS>
