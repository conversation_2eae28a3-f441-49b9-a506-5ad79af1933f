<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="pt_PT">
<context>
    <name>DMXUSB</name>
    <message>
        <location filename="dmxusb.cpp" line="152"/>
        <source>This plugin provides DMX output support for</source>
        <translation>Este plugin fornece suporte de saída DMX para</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="156"/>
        <source>and compatible devices.</source>
        <translation>e dispositivos compatíveis.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="170"/>
        <source>No output support available.</source>
        <translation>Suoporte de saída não disponível</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="172"/>
        <source>Make sure that you have your hardware firmly plugged in. NOTE: FTDI VCP interface is not supported by this plugin.</source>
        <translation>Certefique-se que tem o equipamento bem ligado. NOTA: As interfaces FTDI VCP não são suportadas por este plugin.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="181"/>
        <location filename="dmxusb.cpp" line="280"/>
        <source>Device is operating correctly.</source>
        <translation>O dispositivo está a funcionar correctamente.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="183"/>
        <location filename="dmxusb.cpp" line="282"/>
        <source>Driver in use: %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="267"/>
        <source>No input support available.</source>
        <translation>Suoporte de entrada não disponível</translation>
    </message>
</context>
<context>
    <name>DMXUSBConfig</name>
    <message>
        <location filename="dmxusbconfig.cpp" line="47"/>
        <source>Refresh</source>
        <translation>Actualizar</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="48"/>
        <source>Close</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Name</source>
        <translation>Nome</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Serial</source>
        <translation>Série</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Output frequency</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DMXUSBOpenRx</name>
    <message>
        <location filename="dmxusbopenrx.cpp" line="124"/>
        <source>Protocol</source>
        <translation type="unfinished">Protocolo</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="130"/>
        <source>Stopped</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="132"/>
        <source>Idling</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="134"/>
        <source>Calibrating</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="136"/>
        <source>Receiving</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="138"/>
        <source>Receiver state</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="143"/>
        <source>Received DMX Channels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="148"/>
        <source>DMX Frame Frequency</source>
        <translation type="unfinished">Frequência de frames DMX</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="154"/>
        <source>Bad</source>
        <translation type="unfinished">Má</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="156"/>
        <source>Good</source>
        <translation type="unfinished">Boa</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="158"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation type="unfinished">Efectuar patch deste dispositivo a um universo para descobir.</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="160"/>
        <source>System Timer Accuracy</source>
        <translation type="unfinished">Precisão do relógio de sistema </translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBOpen</name>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="116"/>
        <source>Protocol</source>
        <translation>Protocolo</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="121"/>
        <source>DMX Channels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="124"/>
        <source>DMX Frame Frequency</source>
        <translation>Frequência de frames DMX</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="128"/>
        <source>Bad</source>
        <translation>Má</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="130"/>
        <source>Good</source>
        <translation>Boa</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="132"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation>Efectuar patch deste dispositivo a um universo para descobir.</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="133"/>
        <source>System Timer Accuracy</source>
        <translation>Precisão do relógio de sistema </translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBPro</name>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="115"/>
        <location filename="enttecdmxusbpro.cpp" line="117"/>
        <source>Protocol</source>
        <translation type="unfinished">Protocolo</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="120"/>
        <source>Manufacturer</source>
        <translation type="unfinished">Fabricante</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="122"/>
        <source>Serial number</source>
        <translation type="unfinished">Número de série</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="171"/>
        <location filename="nanodmx.cpp" line="236"/>
        <location filename="stageprofi.cpp" line="141"/>
        <location filename="vinceusbdmx512.cpp" line="233"/>
        <source>Protocol</source>
        <translation>Protocolo</translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="178"/>
        <location filename="nanodmx.cpp" line="243"/>
        <location filename="stageprofi.cpp" line="148"/>
        <location filename="vinceusbdmx512.cpp" line="237"/>
        <source>Serial number</source>
        <translation>Número de série</translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="173"/>
        <location filename="nanodmx.cpp" line="238"/>
        <location filename="stageprofi.cpp" line="143"/>
        <location filename="vinceusbdmx512.cpp" line="235"/>
        <source>Output</source>
        <translation>Saída</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="126"/>
        <location filename="enttecdmxusbopen.cpp" line="118"/>
        <location filename="euroliteusbdmxpro.cpp" line="175"/>
        <location filename="nanodmx.cpp" line="240"/>
        <location filename="stageprofi.cpp" line="145"/>
        <source>Manufacturer</source>
        <translation>Fabricante</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="325"/>
        <source>MIDI Input</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="327"/>
        <source>DMX Input</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="332"/>
        <source>MIDI Output</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="334"/>
        <source>DMX Output</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
