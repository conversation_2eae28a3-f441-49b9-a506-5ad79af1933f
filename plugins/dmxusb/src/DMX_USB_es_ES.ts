<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="es_ES">
<context>
    <name>DMXUSB</name>
    <message>
        <location filename="dmxusb.cpp" line="152"/>
        <source>This plugin provides DMX output support for</source>
        <translation>Este plugin provee soporte de Salida DMX para</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="156"/>
        <source>and compatible devices.</source>
        <translation>y dispositivos compatibles.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="170"/>
        <source>No output support available.</source>
        <translation>Soporte de Salida no disponible.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="172"/>
        <source>Make sure that you have your hardware firmly plugged in. NOTE: FTDI VCP interface is not supported by this plugin.</source>
        <translation>Asegúrese que tiene su hardware firmemente conectado. NOTA: las interfaces FTDI VCP no están soportadas por este plugin.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="181"/>
        <location filename="dmxusb.cpp" line="280"/>
        <source>Device is operating correctly.</source>
        <translation>El dispositivo está funcionando correctamente.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="183"/>
        <location filename="dmxusb.cpp" line="282"/>
        <source>Driver in use: %1</source>
        <translation>Driver en uso: %1</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="267"/>
        <source>No input support available.</source>
        <translation>Soporte de Entrada no disponible.</translation>
    </message>
</context>
<context>
    <name>DMXUSBConfig</name>
    <message>
        <location filename="dmxusbconfig.cpp" line="47"/>
        <source>Refresh</source>
        <translation>Actualizar</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="48"/>
        <source>Close</source>
        <translation>Cerrar</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Name</source>
        <translation>Nombre</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Serial</source>
        <translation>Serie</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Mode</source>
        <translation>Modo</translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Output frequency</source>
        <translation>Frecuencia de salida</translation>
    </message>
</context>
<context>
    <name>DMXUSBOpenRx</name>
    <message>
        <location filename="dmxusbopenrx.cpp" line="124"/>
        <source>Protocol</source>
        <translation>Protocolo</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="130"/>
        <source>Stopped</source>
        <translation>Detenido</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="132"/>
        <source>Idling</source>
        <translation>En espera</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="134"/>
        <source>Calibrating</source>
        <translation>Calibrando</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="136"/>
        <source>Receiving</source>
        <translation>Recibiendo</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="138"/>
        <source>Receiver state</source>
        <translation>Estado del receptor</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="143"/>
        <source>Received DMX Channels</source>
        <translation>Canales DMX recibidos</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="148"/>
        <source>DMX Frame Frequency</source>
        <translation>Frecuencia de Frames DMX</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="154"/>
        <source>Bad</source>
        <translation>Mal</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="156"/>
        <source>Good</source>
        <translation>Bien</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="158"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation>Patchear este dispositivo a un universo a encontrar.</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="160"/>
        <source>System Timer Accuracy</source>
        <translation>Precisión del Reloj de Sistema</translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBOpen</name>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="116"/>
        <source>Protocol</source>
        <translation>Protocolo</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="121"/>
        <source>DMX Channels</source>
        <translation>Canales DMX</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="124"/>
        <source>DMX Frame Frequency</source>
        <translation>Frecuencia de Frames DMX</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="128"/>
        <source>Bad</source>
        <translation>Mal</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="130"/>
        <source>Good</source>
        <translation>Bien</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="132"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation>Patchear este dispositivo a un universo a encontrar.</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="133"/>
        <source>System Timer Accuracy</source>
        <translation>Precisión del Reloj de Sistema</translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBPro</name>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="115"/>
        <location filename="enttecdmxusbpro.cpp" line="117"/>
        <source>Protocol</source>
        <translation>Protocolo</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="120"/>
        <source>Manufacturer</source>
        <translation>Fabricante</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="122"/>
        <source>Serial number</source>
        <translation>Número de Serie</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="171"/>
        <location filename="nanodmx.cpp" line="236"/>
        <location filename="stageprofi.cpp" line="141"/>
        <location filename="vinceusbdmx512.cpp" line="233"/>
        <source>Protocol</source>
        <translation>Protocolo</translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="178"/>
        <location filename="nanodmx.cpp" line="243"/>
        <location filename="stageprofi.cpp" line="148"/>
        <location filename="vinceusbdmx512.cpp" line="237"/>
        <source>Serial number</source>
        <translation>Número de Serie</translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="173"/>
        <location filename="nanodmx.cpp" line="238"/>
        <location filename="stageprofi.cpp" line="143"/>
        <location filename="vinceusbdmx512.cpp" line="235"/>
        <source>Output</source>
        <translation>Salida</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="126"/>
        <location filename="enttecdmxusbopen.cpp" line="118"/>
        <location filename="euroliteusbdmxpro.cpp" line="175"/>
        <location filename="nanodmx.cpp" line="240"/>
        <location filename="stageprofi.cpp" line="145"/>
        <source>Manufacturer</source>
        <translation>Fabricante</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="325"/>
        <source>MIDI Input</source>
        <translation>Entrada MIDI</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="327"/>
        <source>DMX Input</source>
        <translation>Entrada DMX</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="332"/>
        <source>MIDI Output</source>
        <translation>Salida MIDI</translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="334"/>
        <source>DMX Output</source>
        <translation>Salida DMX</translation>
    </message>
</context>
</TS>
