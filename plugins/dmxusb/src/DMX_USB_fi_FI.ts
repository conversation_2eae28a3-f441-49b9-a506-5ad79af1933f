<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="fi_FI">
<context>
    <name>DMXUSB</name>
    <message>
        <location filename="dmxusb.cpp" line="152"/>
        <source>This plugin provides DMX output support for</source>
        <translation type="unfinished">Tämä liitännäinen tukee</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="156"/>
        <source>and compatible devices.</source>
        <translation type="unfinished">Ei tunnistettuja laitteita.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="170"/>
        <source>No output support available.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="172"/>
        <source>Make sure that you have your hardware firmly plugged in. NOTE: FTDI VCP interface is not supported by this plugin.</source>
        <translation type="unfinished">Varmista, että laitteisto on kytkettynä. Huomaa: <PERSON>ä<PERSON><PERSON> liitännäinen ei tue FTDI VCP-interfacea.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="181"/>
        <location filename="dmxusb.cpp" line="280"/>
        <source>Device is operating correctly.</source>
        <translation type="unfinished">Laite toimii oikein.</translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="183"/>
        <location filename="dmxusb.cpp" line="282"/>
        <source>Driver in use: %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusb.cpp" line="267"/>
        <source>No input support available.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DMXUSBConfig</name>
    <message>
        <location filename="dmxusbconfig.cpp" line="47"/>
        <source>Refresh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="48"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Serial</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbconfig.cpp" line="55"/>
        <source>Output frequency</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DMXUSBOpenRx</name>
    <message>
        <location filename="dmxusbopenrx.cpp" line="124"/>
        <source>Protocol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="130"/>
        <source>Stopped</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="132"/>
        <source>Idling</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="134"/>
        <source>Calibrating</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="136"/>
        <source>Receiving</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="138"/>
        <source>Receiver state</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="143"/>
        <source>Received DMX Channels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="148"/>
        <source>DMX Frame Frequency</source>
        <translation type="unfinished">DMX Framen taajuus</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="154"/>
        <source>Bad</source>
        <translation type="unfinished">Huono</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="156"/>
        <source>Good</source>
        <translation type="unfinished">Hyvä</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="158"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation type="unfinished">Kytke tämä laite johonkin universumiin selvittääksesi tilanteen.</translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="160"/>
        <source>System Timer Accuracy</source>
        <translation type="unfinished">Järjestelmän kellon tarkkuus</translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBOpen</name>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="116"/>
        <source>Protocol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="121"/>
        <source>DMX Channels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="124"/>
        <source>DMX Frame Frequency</source>
        <translation>DMX Framen taajuus</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="128"/>
        <source>Bad</source>
        <translation>Huono</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="130"/>
        <source>Good</source>
        <translation>Hyvä</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="132"/>
        <source>Patch this widget to a universe to find out.</source>
        <translation>Kytke tämä laite johonkin universumiin selvittääksesi tilanteen.</translation>
    </message>
    <message>
        <location filename="enttecdmxusbopen.cpp" line="133"/>
        <source>System Timer Accuracy</source>
        <translation>Järjestelmän kellon tarkkuus</translation>
    </message>
</context>
<context>
    <name>EnttecDMXUSBPro</name>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="115"/>
        <location filename="enttecdmxusbpro.cpp" line="117"/>
        <source>Protocol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="120"/>
        <source>Manufacturer</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="122"/>
        <source>Serial number</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="171"/>
        <location filename="nanodmx.cpp" line="236"/>
        <location filename="stageprofi.cpp" line="141"/>
        <location filename="vinceusbdmx512.cpp" line="233"/>
        <source>Protocol</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="178"/>
        <location filename="nanodmx.cpp" line="243"/>
        <location filename="stageprofi.cpp" line="148"/>
        <location filename="vinceusbdmx512.cpp" line="237"/>
        <source>Serial number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="euroliteusbdmxpro.cpp" line="173"/>
        <location filename="nanodmx.cpp" line="238"/>
        <location filename="stageprofi.cpp" line="143"/>
        <location filename="vinceusbdmx512.cpp" line="235"/>
        <source>Output</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="dmxusbopenrx.cpp" line="126"/>
        <location filename="enttecdmxusbopen.cpp" line="118"/>
        <location filename="euroliteusbdmxpro.cpp" line="175"/>
        <location filename="nanodmx.cpp" line="240"/>
        <location filename="stageprofi.cpp" line="145"/>
        <source>Manufacturer</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="325"/>
        <source>MIDI Input</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="327"/>
        <source>DMX Input</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="332"/>
        <source>MIDI Output</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="enttecdmxusbpro.cpp" line="334"/>
        <source>DMX Output</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
