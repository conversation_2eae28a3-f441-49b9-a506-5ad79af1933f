#ifndef QLCCONFIG_H
#define Q<PERSON><PERSON>NFIG_H

#define APPNAME "${APPNAME}"
#define FXEDNAME "${FXEDNAME}"
#define APPVERSION "${APPVERSION}"
#define DOCSDIR "${DOCSDIR}"
#define INPUTPROFILEDIR "${INPUTPROFILEDIR}"
#define USER<PERSON>CPLUSDIR "${USERDATADIR}"
#define USERINPUTPROFILEDIR "${USERINPUTPROFILEDIR}"
#define MIDITEMPLATEDIR "${MIDITEMPLATEDIR}"
#define USERMIDITEMPLATEDIR "${USERMIDITEMPLATEDIR}"
#define MODIFIERSTEMPLATEDIR "${MODIFIERSTEMPLATEDIR}"
#define USERMODIFIERSTEMPLATEDIR "${USERMODIFIERSTEMPLATEDIR}"
#define FIXTUREDIR "${FIXTUREDIR}"
#define USERFIXTUREDIR "${USERFIXTUREDIR}"
#define PLUGINDIR "${PLUGINDIR}"
#define AUDIOP<PERSON>UGINDIR "${AUDIOPLUGINDIR}"
#define TRANSLATIONDIR "${TRANSLATIONDIR}"
#define RGBSCRIPTDIR "${RGBSCRIPTDIR}"
#define USERRGBSCRIPTDIR "${USERRGBSCRIPTDIR}"
#define GOBODIR "${GOBODIR}"
#define WEBFILESDIR "${WEBFILESDIR}"

#ifdef QMLUI
#define MESHESDIR "${MESHESDIR}"
#define COLORFILTERSDIR "${COLORFILTERSDIR}"
#define USERCOLORFILTERSDIR "${USERCOLORFILTERSDIR}"
#endif	/* QMLUI */

#endif	/* QLCCONFIG_H */
