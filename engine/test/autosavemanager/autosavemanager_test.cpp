#include <QtTest>
#include <QCoreApplication>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>
#include <QTemporaryDir>
#include <QDir>
#include <QFile>
#include <QSignalSpy>

#include "autosavemanager_test.h"
#include "../common/resource_paths.h"

#include "../../src/autosavemanager.h"
#include "../../src/doc.h"

void AutoSaveManager_Test::initTestCase()
{
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    
    m_testWorkspaceFile = m_tempDir->path() + "/test_workspace.qxw";
}

void AutoSaveManager_Test::cleanupTestCase()
{
    delete m_tempDir;
}

void AutoSaveManager_Test::init()
{
    m_doc = new Doc(this);
    m_doc->setCurrentWorkspaceFile(m_testWorkspaceFile);

    m_autoSaveManager = new AutoSaveManager(m_doc, this);

    // Reset to default values for consistent testing
    m_autoSaveManager->setEnabled(AutoSaveManager::DEFAULT_AUTOSAVE_ENABLED);
    m_autoSaveManager->setIntervalMinutes(AutoSaveManager::DEFAULT_AUTOSAVE_INTERVAL);
    m_autoSaveManager->setUseBackupFiles(AutoSaveManager::DEFAULT_AUTOSAVE_USE_BACKUP);
    m_autoSaveManager->setMaxBackupFiles(AutoSaveManager::DEFAULT_AUTOSAVE_MAX_BACKUPS);
}

void AutoSaveManager_Test::cleanup()
{
    delete m_autoSaveManager;
    m_autoSaveManager = nullptr;
    
    delete m_doc;
    m_doc = nullptr;
}

void AutoSaveManager_Test::initial()
{
    // Test initial state
    QCOMPARE(m_autoSaveManager->isEnabled(), AutoSaveManager::DEFAULT_AUTOSAVE_ENABLED);
    QCOMPARE(m_autoSaveManager->intervalMinutes(), AutoSaveManager::DEFAULT_AUTOSAVE_INTERVAL);
    QCOMPARE(m_autoSaveManager->useBackupFiles(), AutoSaveManager::DEFAULT_AUTOSAVE_USE_BACKUP);
    QCOMPARE(m_autoSaveManager->maxBackupFiles(), AutoSaveManager::DEFAULT_AUTOSAVE_MAX_BACKUPS);
}

void AutoSaveManager_Test::settings()
{
    // Test settings persistence
    m_autoSaveManager->setEnabled(true);
    m_autoSaveManager->setIntervalMinutes(10);
    m_autoSaveManager->setUseBackupFiles(false);
    m_autoSaveManager->setMaxBackupFiles(5);
    
    m_autoSaveManager->saveSettings();
    
    // Create new manager and load settings
    delete m_autoSaveManager;
    m_autoSaveManager = new AutoSaveManager(m_doc, this);
    
    QCOMPARE(m_autoSaveManager->isEnabled(), true);
    QCOMPARE(m_autoSaveManager->intervalMinutes(), 10);
    QCOMPARE(m_autoSaveManager->useBackupFiles(), false);
    QCOMPARE(m_autoSaveManager->maxBackupFiles(), 5);
}

void AutoSaveManager_Test::enableDisable()
{
    QSignalSpy settingsChangedSpy(m_autoSaveManager, &AutoSaveManager::settingsChanged);

    // Test enabling
    m_autoSaveManager->setEnabled(true);
    QCOMPARE(m_autoSaveManager->isEnabled(), true);
    QCOMPARE(settingsChangedSpy.count(), 1);

    // Test disabling
    m_autoSaveManager->setEnabled(false);
    QCOMPARE(m_autoSaveManager->isEnabled(), false);
    QCOMPARE(settingsChangedSpy.count(), 2);

    // Test setting same value (should not emit signal)
    m_autoSaveManager->setEnabled(false);
    QCOMPARE(settingsChangedSpy.count(), 2);
}

void AutoSaveManager_Test::intervalChange()
{
    QSignalSpy settingsChangedSpy(m_autoSaveManager, &AutoSaveManager::settingsChanged);

    // Test valid interval
    m_autoSaveManager->setIntervalMinutes(15);
    QCOMPARE(m_autoSaveManager->intervalMinutes(), 15);
    QCOMPARE(settingsChangedSpy.count(), 1);

    // Test minimum interval (should be clamped to 1)
    m_autoSaveManager->setIntervalMinutes(0);
    QCOMPARE(m_autoSaveManager->intervalMinutes(), 1);
    QCOMPARE(settingsChangedSpy.count(), 2);

    // Test negative interval (should be clamped to 1, but since it's already 1, no signal)
    m_autoSaveManager->setIntervalMinutes(-5);
    QCOMPARE(m_autoSaveManager->intervalMinutes(), 1);
    QCOMPARE(settingsChangedSpy.count(), 2); // No change from 1 to 1
}

void AutoSaveManager_Test::backupSettings()
{
    QSignalSpy settingsChangedSpy(m_autoSaveManager, &AutoSaveManager::settingsChanged);

    // Test backup file setting (change from default true to false)
    m_autoSaveManager->setUseBackupFiles(false);
    QCOMPARE(m_autoSaveManager->useBackupFiles(), false);
    QCOMPARE(settingsChangedSpy.count(), 1);

    // Test max backup files
    m_autoSaveManager->setMaxBackupFiles(7);
    QCOMPARE(m_autoSaveManager->maxBackupFiles(), 7);
    QCOMPARE(settingsChangedSpy.count(), 2);

    // Test minimum backup files (should be clamped to 1)
    m_autoSaveManager->setMaxBackupFiles(0);
    QCOMPARE(m_autoSaveManager->maxBackupFiles(), 1);
    QCOMPARE(settingsChangedSpy.count(), 3);
}

void AutoSaveManager_Test::autosaveRequest()
{
    QSignalSpy autosaveRequestedSpy(m_autoSaveManager, &AutoSaveManager::autosaveRequested);
    QSignalSpy autosaveCompletedSpy(m_autoSaveManager, &AutoSaveManager::autosaveCompleted);
    
    // Enable autosave and set document as modified
    m_autoSaveManager->setEnabled(true);
    m_doc->setModified();
    
    // Force autosave
    m_autoSaveManager->forceAutosave();
    
    // Should emit autosave requested signal
    QCOMPARE(autosaveRequestedSpy.count(), 1);
    
    // Simulate successful autosave completion
    QString filePath = autosaveRequestedSpy.first().first().toString();
    m_autoSaveManager->onAutosaveResult(true, filePath);
    
    // Should emit autosave completed signal
    QCOMPARE(autosaveCompletedSpy.count(), 1);
    QCOMPARE(autosaveCompletedSpy.first().first().toBool(), true);
    
    // Check that last autosave time was updated
    QVERIFY(!m_autoSaveManager->lastAutosaveTime().isNull());
}

void AutoSaveManager_Test::documentModification()
{
    // Test that autosave only triggers when document is modified
    m_autoSaveManager->setEnabled(true);
    
    QSignalSpy autosaveRequestedSpy(m_autoSaveManager, &AutoSaveManager::autosaveRequested);
    
    // Force autosave when document is not modified
    m_autoSaveManager->forceAutosave();
    QCOMPARE(autosaveRequestedSpy.count(), 0);
    
    // Set document as modified and try again
    m_doc->setModified();
    m_autoSaveManager->forceAutosave();
    QCOMPARE(autosaveRequestedSpy.count(), 1);
}

void AutoSaveManager_Test::cleanup_old_backups()
{
    // This test would require creating actual backup files
    // For now, just test that the settings work correctly
    m_autoSaveManager->setUseBackupFiles(true);
    m_autoSaveManager->setMaxBackupFiles(3);
    
    QCOMPARE(m_autoSaveManager->useBackupFiles(), true);
    QCOMPARE(m_autoSaveManager->maxBackupFiles(), 3);
}

QTEST_MAIN(AutoSaveManager_Test)
