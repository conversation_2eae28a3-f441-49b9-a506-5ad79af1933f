/*
  Q Light Controller - Unit tests
  qlcinputprofile_test.h

  Copyright (C) <PERSON><PERSON><PERSON>

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

#ifndef QLCINPUTPROFILE_TEST_H
#define QLCINPUTPROFILE_TEST_H

#include <QObject>

class QLCInputProfile_Test : public QObject
{
    Q_OBJECT

private slots:
    void manufacturer();
    void model();
    void name();
    void addChannel();
    void removeChannel();
    void remapChannel();
    void channel();
    void channels();
    void channelNumber();
    void copy();
    void assign();
    void load();
    void loadNoProfile();
    void loader();
    void save();
};

#endif
