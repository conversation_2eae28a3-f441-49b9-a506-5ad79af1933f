/*
  Q Light Controller - Unit test
  dmxsource_stub.cpp

  Copyright (c) <PERSON><PERSON><PERSON>

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

#include "dmxsource_stub.h"
#include "mastertimer.h"
#include "universe.h"

DMXSource_Stub::DMXSource_Stub()
        : m_writeCalls(0)
{
}

DMXSource_Stub::~DMXSource_Stub()
{
}

void DMXSource_Stub::writeDMX(MasterTimer* timer, QList<Universe*> universes)
{
    Q_UNUSED(timer);
    Q_UNUSED(universes);

    m_writeCalls++;
}
