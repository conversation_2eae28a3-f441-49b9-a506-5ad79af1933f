TEMPLATE = subdirs
SUBDIRS += bus
SUBDIRS += chaser
SUBDIRS += chaserrunner
SUBDIRS += chaserstep
SUBDIRS += collection
SUBDIRS += cue
SUBDIRS += cuestack
SUBDIRS += doc
SUBDIRS += efx
SUBDIRS += efxfixture
SUBDIRS += fadechannel
SUBDIRS += fixture
SUBDIRS += fixturegroup
SUBDIRS += function
SUBDIRS += genericfader
SUBDIRS += grandmaster
SUBDIRS += inputoutputmap
SUBDIRS += inputpatch
SUBDIRS += keypadparser
SUBDIRS += mastertimer
SUBDIRS += outputpatch
SUBDIRS += qlccapability
SUBDIRS += qlcchannel
SUBDIRS += qlcfile
SUBDIRS += qlcfixturedef
SUBDIRS += qlcfixturedefcache
SUBDIRS += qlcfixturehead
SUBDIRS += qlcfixturemode
SUBDIRS += qlci18n
SUBDIRS += qlcinputchannel
SUBDIRS += qlcinputprofile
SUBDIRS += qlcmacros
SUBDIRS += qlcpalette
SUBDIRS += qlcphysical
SUBDIRS += qlcpoint
SUBDIRS += rgbalgorithm
SUBDIRS += rgbmatrix
SUBDIRS += rgbscript
SUBDIRS += rgbtext
SUBDIRS += scene
SUBDIRS += scenevalue
SUBDIRS += script
SUBDIRS += sequence
SUBDIRS += show
SUBDIRS += showfunction
SUBDIRS += track
SUBDIRS += universe

# Stubs
SUBDIRS += iopluginstub
