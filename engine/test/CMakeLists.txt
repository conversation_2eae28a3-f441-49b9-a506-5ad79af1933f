project(test)

add_subdirectory(autosavemanager)
add_subdirectory(bus)
add_subdirectory(chaser)
add_subdirectory(chaserrunner)
add_subdirectory(chaserstep)
add_subdirectory(collection)
add_subdirectory(cue)
add_subdirectory(cuestack)
add_subdirectory(doc)
add_subdirectory(efx)
add_subdirectory(efxfixture)
add_subdirectory(fadechannel)
add_subdirectory(fixture)
add_subdirectory(fixturegroup)
add_subdirectory(function)
add_subdirectory(genericfader)
add_subdirectory(grandmaster)
add_subdirectory(inputoutputmap)
add_subdirectory(inputpatch)
add_subdirectory(keypadparser)
add_subdirectory(mastertimer)
add_subdirectory(outputpatch)
add_subdirectory(qlccapability)
add_subdirectory(qlcchannel)
add_subdirectory(qlcfile)
add_subdirectory(qlcfixturedef)
add_subdirectory(qlcfixturedefcache)
add_subdirectory(qlcfixturehead)
add_subdirectory(qlcfixturemode)
add_subdirectory(qlci18n)
add_subdirectory(qlcinputchannel)
add_subdirectory(qlcinputprofile)
add_subdirectory(qlcmacros)
add_subdirectory(qlcpalette)
add_subdirectory(qlcphysical)
add_subdirectory(qlcpoint)
add_subdirectory(rgbalgorithm)
add_subdirectory(rgbmatrix)
add_subdirectory(rgbscript)
add_subdirectory(rgbtext)
add_subdirectory(scene)
add_subdirectory(scenevalue)
add_subdirectory(script)
add_subdirectory(sequence)
add_subdirectory(show)
add_subdirectory(showfunction)
add_subdirectory(track)
add_subdirectory(universe)
add_subdirectory(iopluginstub)
