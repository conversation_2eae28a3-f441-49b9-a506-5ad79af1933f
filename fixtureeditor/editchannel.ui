<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  editchannel.ui

  Copyright (c) 2015 Massimo Callegari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>EditChannel</class>
 <widget class="QDialog" name="EditChannel">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Edit Channel</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="9" column="0" colspan="2">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="7" column="0" colspan="2">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>Capabilities</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0" rowspan="6">
       <widget class="QTableWidget" name="m_capabilityList">
        <property name="rowCount">
         <number>10</number>
        </property>
        <property name="columnCount">
         <number>3</number>
        </property>
        <attribute name="horizontalHeaderCascadingSectionResizes">
         <bool>false</bool>
        </attribute>
        <attribute name="horizontalHeaderDefaultSectionSize">
         <number>140</number>
        </attribute>
        <attribute name="horizontalHeaderStretchLastSection">
         <bool>true</bool>
        </attribute>
        <attribute name="verticalHeaderVisible">
         <bool>false</bool>
        </attribute>
        <row/>
        <row/>
        <row/>
        <row/>
        <row/>
        <row/>
        <row/>
        <row/>
        <row/>
        <row/>
        <column>
         <property name="text">
          <string>Minimum value</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Maximum value</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Description</string>
         </property>
        </column>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="m_invalidMinMax">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>&lt;font color=&quot;red&quot;&gt;&lt;b&gt;Capability overlapping detected. Please fix.&lt;/b&gt;&lt;/font&gt;</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QToolButton" name="m_wizardButton">
        <property name="toolTip">
         <string>Capability wizard</string>
        </property>
        <property name="text">
         <string notr="true">...</string>
        </property>
        <property name="icon">
         <iconset resource="../ui/src/qlcui.qrc">
          <normaloff>:/wizard.png</normaloff>:/wizard.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>32</width>
          <height>32</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="2" column="1" rowspan="5">
       <spacer>
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>84</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="1">
       <widget class="QToolButton" name="m_removeCapabilityButton">
        <property name="toolTip">
         <string>Remove the selected capability</string>
        </property>
        <property name="text">
         <string notr="true">...</string>
        </property>
        <property name="icon">
         <iconset resource="../ui/src/qlcui.qrc">
          <normaloff>:/edit_remove.png</normaloff>:/edit_remove.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>32</width>
          <height>32</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="0" colspan="2">
    <layout class="QGridLayout" name="gridLayout_3">
     <item row="2" column="1">
      <widget class="QSpinBox" name="m_defaultValSpin">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="maximum">
        <number>255</number>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="m_nameLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>Name</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QComboBox" name="m_presetCombo">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
     <item row="1" column="3">
      <widget class="QComboBox" name="m_typeCombo">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>The group this channel belongs to</string>
       </property>
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>Default value</string>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QLabel" name="m_groupLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>Type</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="m_presetLabel">
       <property name="text">
        <string>Preset</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1" colspan="3">
      <widget class="QLineEdit" name="m_nameEdit">
       <property name="toolTip">
        <string>The name of this channel</string>
       </property>
      </widget>
     </item>
     <item row="2" column="2">
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>Role</string>
       </property>
      </widget>
     </item>
     <item row="2" column="3">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QRadioButton" name="m_msbRadio">
         <property name="text">
          <string>Coarse (MSB)</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="m_lsbRadio">
         <property name="text">
          <string>Fine (LSB)</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item row="8" column="0" colspan="2">
    <layout class="QGridLayout" name="m_presetGrid">
     <item row="0" column="0">
      <widget class="QLabel" name="m_capPresetLabel">
       <property name="text">
        <string>Preset</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1" colspan="2">
      <widget class="QComboBox" name="m_capPresetCombo">
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
      </widget>
     </item>
     <item row="1" column="0" colspan="3">
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLabel" name="m_pictureLabel">
         <property name="text">
          <string>Picture</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QToolButton" name="m_pictureButton">
         <property name="text">
          <string notr="true">...</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="m_color1Label">
         <property name="text">
          <string>Color 1</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QToolButton" name="m_color1Button">
         <property name="text">
          <string notr="true"/>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/color.png</normaloff>:/color.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="m_color2Label">
         <property name="text">
          <string>Color 2</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QToolButton" name="m_color2Button">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="text">
          <string notr="true"/>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/color.png</normaloff>:/color.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="m_val1Label">
         <property name="text">
          <string>Value 1</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QDoubleSpinBox" name="m_val1Spin">
         <property name="maximum">
          <double>999.990000000000009</double>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="m_val2Label">
         <property name="text">
          <string>Value 2</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QDoubleSpinBox" name="m_val2Spin">
         <property name="maximum">
          <double>999.990000000000009</double>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item row="0" column="3" rowspan="2">
      <widget class="QGroupBox" name="m_resourceGroup">
       <property name="title">
        <string>Preview</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <layout class="QGridLayout" name="gridLayout1">
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="QToolButton" name="m_resourceButton">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="minimumSize">
           <size>
            <width>64</width>
            <height>64</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="iconSize">
           <size>
            <width>64</width>
            <height>64</height>
           </size>
          </property>
          <property name="arrowType">
           <enum>Qt::NoArrow</enum>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>m_nameEdit</tabstop>
  <tabstop>m_presetCombo</tabstop>
  <tabstop>m_typeCombo</tabstop>
  <tabstop>m_defaultValSpin</tabstop>
  <tabstop>m_msbRadio</tabstop>
  <tabstop>m_lsbRadio</tabstop>
  <tabstop>m_capabilityList</tabstop>
  <tabstop>m_capPresetCombo</tabstop>
  <tabstop>m_pictureButton</tabstop>
  <tabstop>m_color1Button</tabstop>
  <tabstop>m_color2Button</tabstop>
  <tabstop>m_val1Spin</tabstop>
  <tabstop>m_val2Spin</tabstop>
  <tabstop>m_removeCapabilityButton</tabstop>
  <tabstop>m_wizardButton</tabstop>
  <tabstop>m_resourceButton</tabstop>
 </tabstops>
 <resources>
  <include location="../ui/src/qlcui.qrc"/>
  <include location="../ui/src/qlcui.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>EditChannel</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>257</x>
     <y>447</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>EditChannel</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>325</x>
     <y>447</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
