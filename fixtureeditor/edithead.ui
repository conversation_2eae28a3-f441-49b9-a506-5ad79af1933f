<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  edithead.ui

  Copyright (c) 2015 Massimo Callegari

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>EditHead</class>
 <widget class="QDialog" name="EditHead">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>560</width>
    <height>451</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Edit Head</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTreeWidget" name="m_tree">
     <property name="rootIsDecorated">
      <bool>false</bool>
     </property>
     <property name="itemsExpandable">
      <bool>false</bool>
     </property>
     <property name="allColumnsShowFocus">
      <bool>true</bool>
     </property>
     <property name="expandsOnDoubleClick">
      <bool>false</bool>
     </property>
     <column>
      <property name="text">
       <string>Channel</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Name</string>
      </property>
     </column>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="m_buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>EditHead</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>EditHead</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
