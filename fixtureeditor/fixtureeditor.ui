<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  fixtureeditor.ui

  Copyright (c) 2015 Massimo Callegari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>FixtureEditor</class>
 <widget class="QWidget" name="FixtureEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>550</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Fixture</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="0" column="0">
    <widget class="QTabWidget" name="m_tab">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="General">
      <attribute name="title">
       <string>General</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="m_manufacturerLabel">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>Manufacturer</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="m_manufacturerEdit">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="toolTip">
          <string>The manufacturer name for this fixture (e.g. Martin)</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="m_modelLabel">
         <property name="text">
          <string>Model</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="m_modelEdit">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="toolTip">
          <string>The model name for this fixture (e.g. MAC250)</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="m_typeLabel">
         <property name="text">
          <string>Type</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QComboBox" name="m_typeCombo">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="toolTip">
          <string>The general type of this fixture</string>
         </property>
         <property name="iconSize">
          <size>
           <width>24</width>
           <height>24</height>
          </size>
         </property>
         <item>
          <property name="text">
           <string notr="true">Color Changer</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/fixture.png</normaloff>:/fixture.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Dimmer</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/dimmer.png</normaloff>:/dimmer.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Effect</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/effect.png</normaloff>:/effect.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Fan</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/fan.png</normaloff>:/fan.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Flower</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/flower.png</normaloff>:/flower.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Hazer</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/hazer.png</normaloff>:/hazer.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Laser</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/laser.png</normaloff>:/laser.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Moving Head</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/movinghead.png</normaloff>:/movinghead.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Other</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/other.png</normaloff>:/other.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Scanner</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/scanner.png</normaloff>:/scanner.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Smoke</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/smoke.png</normaloff>:/smoke.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">Strobe</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/strobe.png</normaloff>:/strobe.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">LED Bar (Beams)</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/other.png</normaloff>:/other.png</iconset>
          </property>
         </item>
         <item>
          <property name="text">
           <string notr="true">LED Bar (Pixels)</string>
          </property>
          <property name="icon">
           <iconset resource="../ui/src/qlcui.qrc">
            <normaloff>:/other.png</normaloff>:/other.png</iconset>
          </property>
         </item>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="m_authorLabel">
         <property name="text">
          <string>Author</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="m_authorEdit">
         <property name="toolTip">
          <string>The name of the author of this fixture definition</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <spacer>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="Physical">
      <attribute name="title">
       <string>Physical</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QVBoxLayout" name="physicalLayout"/>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="Channels">
      <attribute name="title">
       <string>Channels</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0" rowspan="8">
        <widget class="QTreeWidget" name="m_channelList">
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="rootIsDecorated">
          <bool>true</bool>
         </property>
         <property name="itemsExpandable">
          <bool>true</bool>
         </property>
         <property name="allColumnsShowFocus">
          <bool>true</bool>
         </property>
         <property name="expandsOnDoubleClick">
          <bool>false</bool>
         </property>
         <column>
          <property name="text">
           <string>Name</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Group</string>
          </property>
         </column>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QToolButton" name="m_addChannelButton">
         <property name="toolTip">
          <string>Add a channel</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/edit_add.png</normaloff>:/edit_add.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QToolButton" name="m_removeChannelButton">
         <property name="toolTip">
          <string>Remove the selected channel</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/edit_remove.png</normaloff>:/edit_remove.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QToolButton" name="m_editChannelButton">
         <property name="toolTip">
          <string>Edit the selected channel</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/edit.png</normaloff>:/edit.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="Line" name="line_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QToolButton" name="m_copyChannelButton">
         <property name="toolTip">
          <string>Copy the selected channel to the clipboard</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/editcopy.png</normaloff>:/editcopy.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QToolButton" name="m_pasteChannelButton">
         <property name="toolTip">
          <string>Paste the channel in clipboard to this fixture</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/editpaste.png</normaloff>:/editpaste.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <spacer>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>124</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="7" column="1">
        <widget class="QToolButton" name="m_expandChannelsButton">
         <property name="toolTip">
          <string>Open/close all channel items</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/check.png</normaloff>:/check.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="Modes">
      <attribute name="title">
       <string>Modes</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="0" column="0" rowspan="7">
        <widget class="QTreeWidget" name="m_modeList">
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="rootIsDecorated">
          <bool>true</bool>
         </property>
         <property name="allColumnsShowFocus">
          <bool>true</bool>
         </property>
         <property name="expandsOnDoubleClick">
          <bool>false</bool>
         </property>
         <column>
          <property name="text">
           <string>Name</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Channels</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Heads</string>
          </property>
         </column>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QToolButton" name="m_addModeButton">
         <property name="toolTip">
          <string>Add a mode</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/edit_add.png</normaloff>:/edit_add.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QToolButton" name="m_removeModeButton">
         <property name="toolTip">
          <string>Remove the selected mode</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/edit_remove.png</normaloff>:/edit_remove.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QToolButton" name="m_editModeButton">
         <property name="toolTip">
          <string>Edit the selected mode</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/edit.png</normaloff>:/edit.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="Line" name="line">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QToolButton" name="m_cloneModeButton">
         <property name="toolTip">
          <string>Create a copy of the selected mode</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/editcopy.png</normaloff>:/editcopy.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <spacer>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>170</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="6" column="1">
        <widget class="QToolButton" name="m_expandModesButton">
         <property name="toolTip">
          <string>Open/close all mode items</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../ui/src/qlcui.qrc">
           <normaloff>:/check.png</normaloff>:/check.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>Aliases</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_5">
       <item row="0" column="0">
        <layout class="QGridLayout" name="m_aliasGroup">
         <item row="1" column="3">
          <widget class="QComboBox" name="m_modeChannels">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="m_aliasModeLabel">
           <property name="text">
            <string>In mode</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QComboBox" name="m_modesCombo">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
         <item row="1" column="6">
          <widget class="QToolButton" name="m_addAliasButton">
           <property name="toolTip">
            <string>Add a new alias</string>
           </property>
           <property name="text">
            <string notr="true"/>
           </property>
           <property name="icon">
            <iconset resource="../ui/src/qlcui.qrc">
             <normaloff>:/edit_add.png</normaloff>:/edit_add.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="m_aliasOverrideLabel">
           <property name="text">
            <string>with</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="m_aliasReplaceLabel">
           <property name="text">
            <string>replace</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0" colspan="8">
          <widget class="QTreeWidget" name="m_aliasTree">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <attribute name="headerDefaultSectionSize">
            <number>115</number>
           </attribute>
           <column>
            <property name="text">
             <string>Alias</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Mode</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Base channel</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Override channel</string>
            </property>
           </column>
          </widget>
         </item>
         <item row="1" column="5">
          <widget class="QComboBox" name="m_allChannels">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
         <item row="1" column="7">
          <widget class="QToolButton" name="m_removeAliasButton">
           <property name="toolTip">
            <string>Remove the currently selected alias</string>
           </property>
           <property name="text">
            <string notr="true"/>
           </property>
           <property name="icon">
            <iconset resource="../ui/src/qlcui.qrc">
             <normaloff>:/edit_remove.png</normaloff>:/edit_remove.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label">
           <property name="text">
            <string>Alias</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1" colspan="7">
          <widget class="QComboBox" name="m_aliasCapCombo"/>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../ui/src/qlcui.qrc"/>
  <include location="../ui/src/qlcui.qrc"/>
 </resources>
 <connections/>
</ui>
