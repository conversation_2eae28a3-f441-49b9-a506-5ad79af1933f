<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  addchannelsdialog.ui

  Copyright (c) 2015 Massimo <PERSON>ari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>AddChannelsDialog</class>
 <widget class="QDialog" name="AddChannelsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>759</width>
    <height>514</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Fixture Mode Channels Editor</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="2" column="0" colspan="4">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QToolButton" name="m_addChannel">
       <property name="text">
        <string notr="true">&gt;&gt;</string>
       </property>
       <property name="iconSize">
        <size>
         <width>32</width>
         <height>32</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="m_removeChannel">
       <property name="text">
        <string notr="true">&lt;&lt;</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <widget class="QTreeWidget" name="m_allTree">
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="rootIsDecorated">
      <bool>false</bool>
     </property>
     <column>
      <property name="text">
       <string>Name</string>
      </property>
     </column>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Available channels</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Mode channels</string>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QTreeWidget" name="m_modeTree">
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="rootIsDecorated">
      <bool>false</bool>
     </property>
     <column>
      <property name="text">
       <string>Name</string>
      </property>
     </column>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>AddChannelsDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>AddChannelsDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
