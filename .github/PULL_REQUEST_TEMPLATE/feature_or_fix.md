<!--
Thank you for contributing to QLC+!

Please ensure your code changes adhere to the project's standards and guidelines to facilitate a smooth review process.
-->

## Description

**Summary of Changes:**
<!-- Provide a concise description of the changes introduced by this pull request. -->

**Related Issues:**
<!-- Reference any related issues or discussions, e.g., "Fixes #1234" or "Related to #5678". -->

## Checklist

- [ ] I have read and followed the [QLC+ Coding Guidelines](https://github.com/mcallegari/qlcplus/wiki/Coding-guidelines).
- [ ] My code adheres to the project's coding style, including:
  - [ ] Placing opening braces `{` on a new line for functions and class definitions.
  - [ ] Consistent use of spaces and indentation.
- [ ] I have tested my changes on the following platforms:
  - [ ] Linux
  - [ ] Windows
  - [ ] macOS
- [ ] I have added or [updated documentation](https://docs.qlcplus.org/) as necessary.

## Testing

**Test Cases:**
<!-- Describe the test cases you have implemented or run to verify your changes. -->

**Test Results:**
<!-- Provide the results of your testing, including any screenshots or logs if applicable. -->

## Additional Notes

<!-- Include any additional information, context, or considerations for the reviewers. -->

---

Thank you for your contribution to QLC+!
