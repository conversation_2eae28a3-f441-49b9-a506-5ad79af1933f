<!DOCTYPE html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>QLC+ Web API Test</title>
<script type="text/javascript">

// the WebSocket instance
var websocket;
var isConnected = false;
// the websocket host location
var wshost = "http://127.0.0.1:9999";

// helper function to send QLC+ API commands
function requestAPI(cmd)
{
  if (isConnected === true)
    websocket.send("QLC+API|" + cmd);
  else
    alert("You must connect to QLC+ WebSocket first!");
}

// helper function to send a QLC+ API with one parameter.
// The specified parameter is not a value, but a CSS object
// from which a value is retrieved (usually a <input> box)
function requestAPIWithParam(cmd, paramObjName)
{
  var obj = document.getElementById(paramObjName);
  if (obj)
  {
    if (isConnected === true)
      websocket.send("QLC+API|" + cmd + "|" + obj.value);
    else
      alert("You must connect to QLC+ WebSocket first!");
  }
}

function requestAPIWith2Params(cmd, paramObjName, param2ObjName)
{
  var obj1 = document.getElementById(paramObjName);
  var obj2 = document.getElementById(param2ObjName);
  if (obj1 && obj2)
  {
    if (isConnected === true)
      websocket.send("QLC+API|" + cmd + "|" + obj1.value + "|" + obj2.value);
    else
      alert("You must connect to QLC+ WebSocket first!");
  }
}

function requestChannelsRange(cmd, uniObjName, addressObjName, rangeObjName)
{
  var uniObj = document.getElementById(uniObjName);
  var addrObj = document.getElementById(addressObjName);
  var rangeObj = document.getElementById(rangeObjName);
  if (uniObj && addrObj && rangeObj)
  {
    if (isConnected === true)
      websocket.send("QLC+API|" + cmd + "|" + uniObj.value + "|" + addrObj.value + "|" + rangeObj.value);
    else
      alert("You must connect to QLC+ WebSocket first!");
  }
}

function setSimpleDeskChannel(addressObjName, channelValueObjName)
{
  var addrObj = document.getElementById(addressObjName);
  var valObj = document.getElementById(channelValueObjName);
  if (addrObj && valObj)
  {
    if (isConnected === true)
      websocket.send("CH|" + addrObj.value + "|" + valObj.value);
    else
      alert("You must connect to QLC+ WebSocket first!");
  }
}

function vcWidgetSetValue(wIDObjName, wValueObjName)
{
  var wObj = document.getElementById(wIDObjName);
  var valObj = document.getElementById(wValueObjName);
  if (wObj && valObj)
  {
    if (isConnected === true)
      websocket.send(wObj.value + "|" + valObj.value);
    else
      alert("You must connect to QLC+ WebSocket first!");
  }
}

function vcCueListControl(clIDObjName, clOpObjName, clStepObjName)
{
  var clObj = document.getElementById(clIDObjName);
  var opObj = document.getElementById(clOpObjName);
  var stepObj = document.getElementById(clStepObjName);
  if (clObj && opObj)
  {
    if (isConnected === true)
    {
      if (opObj.value === "STEP")
        websocket.send(clObj.value + "|" + opObj.value + "|" + stepObj.value);
      else
        websocket.send(clObj.value + "|" + opObj.value);
    }
    else
      alert("You must connect to QLC+ WebSocket first!");
  }
}

function vcFrameControl(frIDObjName, frOperation)
{
  var frObj = document.getElementById(frIDObjName);
  var opObj = document.getElementById(frOperation);

  if (frObj && opObj)
  {
    if (isConnected === true)
    {
        websocket.send(frObj.value + "|" + opObj.value);
    }
    else
      alert("You must connect to QLC+ WebSocket first!");
  }
}

function connectToWebSocket(host) {
  var url = 'ws://' + host + '/qlcplusWS';
  websocket = new WebSocket(url);
  // update the host information
  wshost = "http://" + host;

  websocket.onopen = function(ev) {
    //alert("QLC+ connection successful");
    document.getElementById('connStatus').innerHTML = "<font color=green>Connected</font>";
    isConnected = true;
  };

  websocket.onclose = function(ev) {
    alert("QLC+ connection lost!");
  };

  websocket.onerror = function(ev) {
    alert("QLC+ connection error!");
  };

  // WebSocket message handler. This is where async events
  // will be shown or processed as needed
  websocket.onmessage = function(ev) {
    // Uncomment the following line to display the received message
    //alert(ev.data);

    // Event data is formatted as follows: "QLC+API|API name|arguments"
    // Arguments vary depending on the API called

    var msgParams = ev.data.split('|');

    if (msgParams[0] === "QLC+API")
    {
      if (msgParams[1] === "getFunctionsNumber")
      {
        document.getElementById('getFunctionsNumberBox').innerHTML = msgParams[2];
      }
      // Arguments is an array formatted as follows:
      // Function ID|Function name|Function ID|Function name|...
      else if (msgParams[1] === "getFunctionsList")
      {
        var tableCode = "<table class='apiTable'><tr><th>ID</th><th>Name</th></tr>";
        for (i = 2; i < msgParams.length; i+=2)
        {
            tableCode = tableCode + "<tr><td>" + msgParams[i] + "</td><td>" + msgParams[i + 1] + "</td></tr>";
        }
        tableCode += "</table>";
        document.getElementById('getFunctionsListBox').innerHTML = tableCode;
      }
      else if (msgParams[1] === "getFunctionType")
      {
        document.getElementById('getFunctionTypeBox').innerHTML = msgParams[2];
      }
      else if (msgParams[1] === "getFunctionStatus")
      {
        document.getElementById('getFunctionStatusBox').innerHTML = msgParams[2];
      }
      else if (msgParams[1] === "getWidgetsNumber")
      {
        document.getElementById('getWidgetsNumberBox').innerHTML = msgParams[2];
      }
      // Arguments is an array formatted as follows:
      // Widget ID|Widget name|Widget ID|Widget name|...
      else if (msgParams[1] === "getWidgetsList")
      {
        var tableCode = "<table class='apiTable'><tr><th>ID</th><th>Name</th></tr>";
        for (i = 2; i < msgParams.length; i+=2)
        {
            tableCode = tableCode + "<tr><td>" + msgParams[i] + "</td><td>" + msgParams[i + 1] + "</td></tr>";
        }
        tableCode += "</table>";
        document.getElementById('getWidgetsListBox').innerHTML = tableCode;
      }
      else if (msgParams[1] === "getWidgetType")
      {
        document.getElementById('getWidgetTypeBox').innerHTML = msgParams[2];
      }
      else if (msgParams[1] === "getWidgetStatus")
      {
        var status = msgParams[2];
        if (msgParams[2] === "PLAY")
            status = msgParams[2] + "(Step: " + msgParams[3] + ")";
        document.getElementById('getWidgetStatusBox').innerHTML = status;
      }
      else if (msgParams[1] === "getChannelsValues")
      {
        var tableCode = "<table class='apiTable'><tr><th>Index</th><th>Value</th><th>Type</th></tr>";
        for (i = 2; i < msgParams.length; i+=3)
        {
            tableCode = tableCode + "<tr><td>" + msgParams[i] + "</td><td>" + msgParams[i + 1] + "</td><td>" + msgParams[i + 2] + "</td></tr>";
        }
        tableCode += "</table>";
        document.getElementById('requestChannelsRangeBox').innerHTML = tableCode;
      }
    }
  };
};

function loadProject () {
  var formAction = wshost + "/loadProject";
  document.getElementById('lpForm').action = formAction;
}

</script>

<style type="text/css">

body {
  background-color: #45484d;
  color: white;
  font:normal 18px/1.2em sans-serif;
}

iframe {
  position: absolute;
  display: block;

  height: 100%;
  width: 100%;
}

#prjBox {
  position: absolute;
  width: 50%;
  height: 70%;
  margin-top: 0px;
  margin-left: 47%;
}

.apiTable {
  border-collapse: collapse;
}

.apiTable th {
  font-size: 18px;
  color: white;
  border: solid 1px white;
}

.apiTable tr {
  font-size: 14px;
  border: solid 1px white;
}

.apiTable td {
  border: solid 1px white;
  padding: 2px 5px 2px 5px;
  margin: 0 5px 0 5px;
}

.apiButton {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  font-weight: bold;
  color: black;
  cursor:pointer;
  height: 30px;
  padding: 0 10px 0 10px;
  background: #7497b6;
}

.apiButton:hover {
  background: #87b1d5;
}

.resultBox {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  color: #000;
  width: 150px;
  height: 30px;
  background-color: #aaaaaa;
  border-radius: 6px;
}

</style>

<body>

<h2>Q Light Controller+ Web API test page</h2>

<!-- ############## Project box to display what QLC+ is doing ####################### -->
<div id="prjBox"><iframe name="projectFrame" src="" id="projectFrame"></iframe></div>

<!-- ############## Websocket connection code ####################### -->
QLC+ IP:
<input type="text" id="qlcplusIP" value="127.0.0.1:9999"/>
<input type="button" value="Connect" onclick="javascript:connectToWebSocket(document.getElementById('qlcplusIP').value);">
<div id="connStatus" style="display: inline-block;"><font color=red>Not connected</font></div>
<br><br>

<!-- ############## Project load code ####################### -->
<form id="lpForm" onsubmit="loadProject()" method="POST" enctype="multipart/form-data" target="projectFrame">
Load a project:
<input id="loadTrigger" type="file" onchange="document.getElementById('submitTrigger').click();" name="qlcprj">
<input id="submitTrigger" type="submit">
</form>
<br><br>

<!-- ############## Individual API tests ####################### -->

<table class="apiTable" width=45%>
 <tr>
  <th width=30%><b>API Function</b></th>
  <th width=30%><b>Description</b></th>
  <th width=40%><b>Result</b></th>
 </tr>

<!-- ############## Channels API tests ####################### -->

 <tr>
  <td colspan="3" align="center"><b>Channels APIs</b></td>
 </tr>
  <tr>
  <td>
    <div class="apiButton" onclick="javascript:requestChannelsRange('getChannelsValues', 'chUniIdx', 'chDMXaddr', 'chRange');">getChannelsValues</div><br>
    Universe index: <input id="chUniIdx" type="text" value="1" size="6"><br>
    DMX start address: <input id="chDMXaddr" type="text" value="1" size="6"><br>
    Channels count: <input id="chRange" type="text" value="16" size="6">
  </td>
  <td>Retrieve the specified number of DMX values for the given universe, starting at the given address.
      Note that indices start from 1 and not from 0.</td>
  <td><div id="requestChannelsRangeBox" style="height: 150px; overflow-y: scroll;"></div></td>
 </tr>

<!-- ############## Functions API tests ####################### -->

 <tr>
  <td colspan="3" align="center"><b>Function APIs</b></td>
 </tr>
 <tr>
  <td><div class="apiButton" onclick="javascript:requestAPI('getFunctionsNumber');">getFunctionsNumber</div></td>
  <td>Retrieve the number of functions loaded</td>
  <td><div id="getFunctionsNumberBox" class="resultBox"></div></td>
 </tr>
 <tr>
  <td><div class="apiButton" onclick="javascript:requestAPI('getFunctionsList');">getFunctionsList</div></td>
  <td>Retrieve the list of functions with their ID and name</td>
  <td><div id="getFunctionsListBox" style="height: 150px; overflow-y: scroll;"></div></td>
 </tr>
 <tr>
  <td>
    <div class="apiButton" onclick="javascript:requestAPIWithParam('getFunctionType', 'fTypeID');">getFunctionType</div><br>
    Function ID: <input id="fTypeID" type="text" value="0" size="6">
  </td>
  <td>Retrieve the type of a function with the given ID</td>
  <td><div id="getFunctionTypeBox" class="resultBox"></div></td>
 </tr>
 <tr>
  <td>
    <div class="apiButton" onclick="javascript:requestAPIWithParam('getFunctionStatus', 'gfFuncID');">getFunctionStatus</div><br>
    Function ID: <input id="gfFuncID" type="text" value="0" size="6">
  </td>
  <td>Retrieve the running status of a function with the given ID. Possible values are "Running", "Stopped" and "Undefined"</td>
  <td><div id="getFunctionStatusBox" class="resultBox"></div></td>
 </tr>
 <tr>
  <td>
    <div class="apiButton" onclick="javascript:requestAPIWith2Params('setFunctionStatus', 'sfFuncID', 'sfStatus');">setFunctionStatus</div><br>
    Function ID: <input id="sfFuncID" type="text" value="0" size="6"><br>
    Status: <input id="sfStatus" type="text" value="0" size="6">
  </td>
  <td>Set the running status of a function with the given ID. Possible values are: 0 (stop) or 1 (run)</td>
  <td></td>
 </tr>

<!-- ############## Widgets API tests ####################### -->

 <tr>
  <td colspan="3" align="center"><b>Virtual Console Widget APIs</b></td>
 </tr>
 <tr>
  <td><div class="apiButton" onclick="javascript:requestAPI('getWidgetsNumber');">getWidgetsNumber</div></td>
  <td>Retrieve the number of widgets loaded</td>
  <td><div id="getWidgetsNumberBox" class="resultBox"></div></td>
 </tr>
 <tr>
  <td><div class="apiButton" onclick="javascript:requestAPI('getWidgetsList');">getWidgetsList</div></td>
  <td>Retrieve the list of Virtual Console Widgets with their ID and name</td>
  <td><div id="getWidgetsListBox" style="height: 150px; overflow-y: scroll;"></div></td>
 </tr>
 <tr>
  <td>
    <div class="apiButton" onclick="javascript:requestAPIWithParam('getWidgetType', 'wTypeID');">getWidgetType</div><br>
    Widget ID: <input id="wTypeID" type="text" value="0" size="6">
  </td>
  <td>Retrieve the type of a Virtual Console Widget with the given ID</td>
  <td><div id="getWidgetTypeBox" class="resultBox"></div></td>
 </tr>
 <tr>
  <td>
    <div class="apiButton" onclick="javascript:requestAPIWithParam('getWidgetStatus', 'wStatusID');">getWidgetStatus</div><br>
    Widget ID: <input id="wStatusID" type="text" value="0" size="6">
  </td>
  <td>Retrieve the status of a Virtual Console Widget with the given ID</td>
  <td><div id="getWidgetStatusBox" class="resultBox"></div></td>
 </tr>

<!-- ############## High rate API tests ####################### -->

 <tr>
  <td colspan="3" align="center"><b>High rate APIs</b></td>
 </tr>
 <tr>
  <td colspan="3">Due to the nature of some type of transmissions (for example a slider changing rapidly),
                  there are a few WebSocket operations stripped down to avoid useless overhead of data.<br>
                  So, instead of transmitting every time the "QLC+API|API name" information, direct calls
                  are here used to accomplish fast operations.
  </td>
 </tr>

 <tr>
  <td>
   <div class="apiButton" onclick="javascript:setSimpleDeskChannel('sdDMXAddress', 'sdDMXValue');">Simple Desk channel set</div><br>
   Absolute DMX address: <input id="sdDMXAddress" type="text" value="1" size="6"><br>
   Value: <input id="sdDMXValue" type="text" value="100" size="6">
  </td>
  <td colspan="2">
   This API sets the value of a single channel of the QLC+ Simple Desk. The parameters to send are:<br>
   <b>Absolute DMX address</b>: this is the address of the DMX channel you want to set. It is absolute in the sense
   that the universe information is implicit in the address itself. So for example addresses on the first
   universe will range from 1 to 512, while addresses on the second universe will range from 513 to 1024,
   and so on.<br>
   <b>Value</b>: the value of the DMX channel to set in a range from 0 to 255.
  </td>
 </tr>

 <tr>
  <td>
   <div class="apiButton" onclick="javascript:vcWidgetSetValue('basicWidgetID', 'basicWidgetValue');">Basic widget value set</div><br>
   Widget ID: <input id="basicWidgetID" type="text" value="0" size="6"><br>
   Value: <input id="basicWidgetValue" type="text" value="255" size="6">
  </td>
  <td colspan="2">
    This API is the direct way to set a Virtual Console widget value. It can be used for Buttons, Sliders and
    Audio Triggers. The value to set depends on the widget type itself. Buttons will only support values 255
    (= button is pressed) and 0 (= button is released), Audio triggers will support values 0 (= off) and 255 (= on)
    and Sliders will accept all the values in the 0-255 range.
  </td>
 </tr>

 <tr>
  <td>
   <div class="apiButton" onclick="javascript:vcCueListControl('clWidgetID', 'clOperation', 'clStep');">Cue list control</div><br>
   Cue List ID: <input id="clWidgetID" type="text" value="0" size="6"><br>
   Operation: <input id="clOperation" type="text" value="PLAY" size="6"><br>
   Step (optional): <input id="clStep" type="text" value="1" size="6">
  </td>
  <td colspan="2">
    This API demonstrates how to control a Virtual Console Cue List widget. The parameters to be used are:<br>
    <b>Cue List ID</b>: The Cue List widget ID as retrieved with the 'getWidgetsList' API<br>
    <b>Operation</b>: The Cue List operation to perform. Possible values are 'PLAY', 'NEXT', 'PREV' and 'STEP'.
    Only the 'STEP' operation requires a third parameter. The 'PLAY' operation will stop the Cue List if called
    twice.<br>
    <b>Step</b>: The Cue List step index to play. Index starts from 0.
  </td>
 </tr>

 <tr>
  <td>
   <div class="apiButton" onclick="javascript:vcFrameControl('frWidgetID', 'frOperation');">Multipage frame control</div><br>
   Frame ID: <input id="frWidgetID" type="text" value="0" size="6"><br>
   Operation: <input id="frOperation" type="text" value="NEXT_PG" size="6">
  </td>
  <td colspan="2">
    This API demonstrates how to change page of a Virtual Console Frame widget in multipage mode.
    The parameters to be used are:<br>
    <b>Frame ID</b>: The Frame widget ID as retrieved with the 'getWidgetsList' API<br>
    <b>Operation</b>: The Frame operation to perform. Accepted values are 'NEXT_PG' and 'PREV_PG'.
  </td>
 </tr>

</table>

</body>
</html>
