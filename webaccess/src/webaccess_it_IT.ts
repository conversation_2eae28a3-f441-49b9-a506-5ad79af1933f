<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="it_IT">
<context>
    <name>WebAccess</name>
    <message>
        <location filename="webaccess.cpp" line="169"/>
        <source>Loading project...</source>
        <translation>Caricamento progetto...</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="205"/>
        <source>Fixture stored and loaded</source>
        <translation>Fixture salvata e caricata</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="396"/>
        <source>Username and password are required fields.</source>
        <translation>Nome utente e password sono parametri richiesti.</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="402"/>
        <location filename="webaccess.cpp" line="427"/>
        <source>User level has to be a positive integer.</source>
        <translation>Il livello dell&apos;utente deve essere un intero positivo.</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="421"/>
        <source>Username is required.</source>
        <translation>Il nome utente è richiesto.</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="439"/>
        <source>Error while saving passwords file.</source>
        <translation>Errore durante il salvataggio del file delle password.</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="454"/>
        <source>Network configuration changed. Reboot to apply the changes.</source>
        <translation>La configurazione di rete è stata modificata. Riavviare per applicare le modifiche.</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="473"/>
        <source>Autostart configuration changed</source>
        <translation>La configurazione di avvio è stata modificata</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="822"/>
        <source>Widget not supported (yet) for web access</source>
        <translation>Oggetto non ancora supportato via web</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="873"/>
        <location filename="webaccess.cpp" line="925"/>
        <source>Page</source>
        <translation>Pagina</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1088"/>
        <source>Enable</source>
        <translation>Abilita</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1120"/>
        <source>Name</source>
        <translation>Nome</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1121"/>
        <source>Fade In</source>
        <translation>Fade In</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1122"/>
        <source>Fade Out</source>
        <translation>Fade Out</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1123"/>
        <source>Duration</source>
        <translation>Durata</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1124"/>
        <source>Notes</source>
        <translation>Note</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1360"/>
        <source>Load project</source>
        <translation>Carica progetto</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1362"/>
        <source>Simple Desk</source>
        <translation>Banco Semplice</translation>
    </message>
    <message>
        <location filename="webaccess.cpp" line="1364"/>
        <source>Configuration</source>
        <translation>Configurazione</translation>
    </message>
</context>
<context>
    <name>WebAccessConfiguration</name>
    <message>
        <location filename="webaccessconfiguration.cpp" line="136"/>
        <source>Passthrough</source>
        <translation>Passthrough</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="210"/>
        <source>Load fixture</source>
        <translation>Carica fixture</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="221"/>
        <source>Username</source>
        <translation>Nome utente</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="222"/>
        <source>Password</source>
        <translation>Password</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="223"/>
        <source>Access level</source>
        <translation>Livello di accesso</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="224"/>
        <source>Action</source>
        <translation>Azione</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="235"/>
        <source>Leave blank to not change</source>
        <translation>Lasciare in bianco per non modificare</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="242"/>
        <location filename="webaccessconfiguration.cpp" line="272"/>
        <source>Only Virtual Console</source>
        <translation>Solo Console Virtuale</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="247"/>
        <location filename="webaccessconfiguration.cpp" line="274"/>
        <source>Virtual Console and Simple Desk</source>
        <translation>Console Virtuale e Banco Semplice</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="252"/>
        <location filename="webaccessconfiguration.cpp" line="276"/>
        <source>Everything</source>
        <translation>Tutto</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="258"/>
        <location filename="webaccessconfiguration.cpp" line="283"/>
        <source>Change</source>
        <translation>Modifica</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="260"/>
        <location filename="webaccessconfiguration.cpp" line="283"/>
        <source>Delete user</source>
        <translation>Elimina utente</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="266"/>
        <source>New username...</source>
        <translation>Nuovo nome utente...</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="267"/>
        <location filename="webaccessconfiguration.cpp" line="285"/>
        <source>New password...</source>
        <translation>Nuova password...</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="284"/>
        <source>Username and password are required fields.</source>
        <translation>Nome utente e password sono parametri richiesti.</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="287"/>
        <source>Add user</source>
        <translation>Aggiungi utente</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="292"/>
        <source>Note: if there isn&apos;t at least one user with access level &quot;Everything&quot; on the list authorization will be disabled.</source>
        <translation>Nota: se nella lista non esiste almeno un utente con accesso a &quot;Tutto&quot;, l&apos;accesso autorizzato verrà disabilitato.</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="323"/>
        <source>System</source>
        <translation>Sistema</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="332"/>
        <source>Back</source>
        <translation>Indietro</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="340"/>
        <source>Universes configuration</source>
        <translation>Configurazione degli universi</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="347"/>
        <source>Audio configuration</source>
        <translation>Configurazione audio</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="355"/>
        <source>User loaded fixtures</source>
        <translation>Fixture caricate dall&apos;utente</translation>
    </message>
    <message>
        <location filename="webaccessconfiguration.cpp" line="364"/>
        <source>Authorized users</source>
        <translation>Utenti autorizzati</translation>
    </message>
</context>
<context>
    <name>WebAccessNetwork</name>
    <message>
        <location filename="webaccessnetwork.cpp" line="101"/>
        <source>Network interface: </source>
        <translation>Interfaccia di rete:</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="106"/>
        <source>Access point name (SSID): </source>
        <translation>Nome dell&apos;access point (SSID):</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="108"/>
        <source>WPA-PSK Password: </source>
        <translation>Password WPA-PSK:</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="113"/>
        <source>Dynamic (DHCP)</source>
        <translation>Dinamico (DHCP)</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="115"/>
        <source>Static</source>
        <translation>Statico</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="119"/>
        <source>IP Address: </source>
        <translation>Indirizzo IP:</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="121"/>
        <source>Netmask: </source>
        <translation>Maschera:</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="123"/>
        <source>Gateway: </source>
        <translation>Gateway:</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="126"/>
        <location filename="webaccessnetwork.cpp" line="344"/>
        <source>Apply changes</source>
        <translation>Applica le modifiche</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="327"/>
        <source>Back</source>
        <translation>Indietro</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="333"/>
        <source>Network configuration</source>
        <translation>Configurazione di rete</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="338"/>
        <source>Project autostart</source>
        <translation>Progetto di avvio automatico</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="342"/>
        <source>No project</source>
        <translation>Nessun progetto</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="343"/>
        <source>Use current project</source>
        <translation>Usa il progetto corrente</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="348"/>
        <source>Reboot</source>
        <translation>Riavvia</translation>
    </message>
    <message>
        <location filename="webaccessnetwork.cpp" line="349"/>
        <source>Shutdown</source>
        <translation>Arresta</translation>
    </message>
</context>
<context>
    <name>WebAccessSimpleDesk</name>
    <message>
        <location filename="webaccesssimpledesk.cpp" line="50"/>
        <source>Back</source>
        <translation>Indietro</translation>
    </message>
    <message>
        <location filename="webaccesssimpledesk.cpp" line="56"/>
        <source>Page</source>
        <translation>Pagina</translation>
    </message>
    <message>
        <location filename="webaccesssimpledesk.cpp" line="57"/>
        <source>Previous page</source>
        <translation>Pagina precedente</translation>
    </message>
    <message>
        <location filename="webaccesssimpledesk.cpp" line="65"/>
        <source>Next page</source>
        <translation>Pagina successiva</translation>
    </message>
    <message>
        <location filename="webaccesssimpledesk.cpp" line="68"/>
        <source>Reset universe</source>
        <translation>Reimposta universo</translation>
    </message>
    <message>
        <location filename="webaccesssimpledesk.cpp" line="70"/>
        <source>Universe</source>
        <translation>Universo</translation>
    </message>
</context>
</TS>
