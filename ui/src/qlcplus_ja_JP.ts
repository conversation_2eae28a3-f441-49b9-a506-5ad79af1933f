<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="ja_<PERSON>">
<context>
    <name>AboutBox</name>
    <message>
        <location filename="aboutbox.ui" line="33"/>
        <source>About Q Light Controller Plus</source>
        <oldsource>About Q Light Controller</oldsource>
        <translation>QLC+ について</translation>
    </message>
    <message>
        <location filename="aboutbox.ui" line="88"/>
        <source>Contributors</source>
        <translation>手伝ってくれた方々</translation>
    </message>
    <message>
        <location filename="aboutbox.ui" line="135"/>
        <source>This application is licensed under the terms of the Apache 2.0 license.</source>
        <oldsource>This application is licensed under the terms of GNU GPL version 2.</oldsource>
        <translation>This application is licensed under the terms of the Apache 2.0 license.</translation>
    </message>
    <message>
        <location filename="aboutbox.cpp" line="42"/>
        <source>and contributors:</source>
        <translation>協力してくださった方々</translation>
    </message>
    <message>
        <location filename="aboutbox.cpp" line="43"/>
        <source>Website: %1</source>
        <translation>ウェブサイト: %1</translation>
    </message>
</context>
<context>
    <name>AddChannelsGroup</name>
    <message>
        <location filename="addchannelsgroup.ui" line="33"/>
        <source>Select Channels</source>
        <translation>グループにするチャンネルを選択</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="43"/>
        <source>Group Name</source>
        <translation>グループ名</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="67"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="72"/>
        <source>Type</source>
        <translation>属性</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="77"/>
        <source>Group</source>
        <translation>グループ</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="87"/>
        <source>Apply changes to fixtures of the same type and mode</source>
        <oldsource>Apply changes to fixtures of the same type</oldsource>
        <translation>同じ機種・同じモードの機器に変更を適用</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="94"/>
        <source>Collapse all</source>
        <translation>全て折りたたむ</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="101"/>
        <source>Expand all</source>
        <translation>全て開く</translation>
    </message>
</context>
<context>
    <name>AddFixture</name>
    <message>
        <location filename="addfixture.ui" line="33"/>
        <source>Add fixture</source>
        <translation>DMX機器の追加</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="183"/>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;ERROR: Address already used!&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;エラー: そのアドレスは既に使われています&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="200"/>
        <source>Fixture Model</source>
        <translatorcomment>Fixture est traduit ici par projecteur et regoupe tous les types de projecteurs: asservis et traditionnels.</translatorcomment>
        <translation>機種名</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="45"/>
        <source>Fixture Properties</source>
        <translation>機器の詳細</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="51"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="58"/>
        <source>A friendly name for the new fixture</source>
        <translation>機器の名前または通称</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="65"/>
        <source>Mode</source>
        <translation>モード</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="72"/>
        <source>Selected fixture mode</source>
        <translation>モード</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="103"/>
        <source>Address</source>
        <translation>スタートアドレス</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="112"/>
        <source>The starting address of the (first) added fixture</source>
        <translation>この機器のDMXスタートアドレス</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="125"/>
        <source>Address Tool</source>
        <translation>ディップスイッチ設定支援ツール</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="138"/>
        <source>Channels</source>
        <translation>チャンネル数</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="148"/>
        <source>Number of channels in the selected fixture</source>
        <translation>選択したフィクスチャーのチャンネル数</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="170"/>
        <source>List of channels in the selected fixture mode</source>
        <translation>チャンネル/機能一覧</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="208"/>
        <source>Quick search</source>
        <translation>クイック検索</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="86"/>
        <source>Universe</source>
        <translation>Universe</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="221"/>
        <source>Multiple Fixtures</source>
        <translation>複数台まとめて追加</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="227"/>
        <source>Quantity</source>
        <translation>数</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="240"/>
        <source>Number of fixtures to add</source>
        <translation>追加する機器の数</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="253"/>
        <source>Address gap</source>
        <translation>アドレス間隔</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="260"/>
        <source>Number of empty channels to leave between added fixtures</source>
        <translation>機器間のアドレス間隔</translation>
    </message>
    <message>
        <location filename="addfixture.cpp" line="104"/>
        <source>Fixtures found: %1</source>
        <translation>Fixtures found: %1</translation>
    </message>
    <message>
        <location filename="addfixture.cpp" line="625"/>
        <source>Dimmers</source>
        <translation>ディマー</translation>
    </message>
</context>
<context>
    <name>AddRGBPanel</name>
    <message>
        <location filename="addrgbpanel.ui" line="33"/>
        <source>Add RGB Panel</source>
        <translation>RGBパネルの追加</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="137"/>
        <source>Width</source>
        <translation>幅</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="160"/>
        <source>Height</source>
        <translation>高さ</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="43"/>
        <source>Panel properties</source>
        <translation>パネルの詳細</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="64"/>
        <source>RGB Panel</source>
        <translation>RGBパネル</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="98"/>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;ERROR: Address already used!&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;エラー:そのアドレスは既に使われています&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="198"/>
        <source>Size</source>
        <translation>サイズ</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="212"/>
        <source>Columns</source>
        <oldsource>Columns:</oldsource>
        <translation>行</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="219"/>
        <source>Rows</source>
        <oldsource>Rows:</oldsource>
        <translation>列</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="226"/>
        <source>Total pixels</source>
        <oldsource>Total pixels:</oldsource>
        <translation>合計ドット数</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="299"/>
        <source>Direction</source>
        <translation>方向</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="305"/>
        <source>Horizontal</source>
        <translation>水平</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="315"/>
        <source>Vertical</source>
        <translation>垂直</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="327"/>
        <source>Orientation</source>
        <translation>方向</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="333"/>
        <source>Top-Right</source>
        <translation>右上から</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="340"/>
        <source>Top-Left</source>
        <translation>左上から</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="350"/>
        <source>Bottom-Left</source>
        <translation>左下から</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="357"/>
        <source>Bottom-Right</source>
        <translation>右下から</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="120"/>
        <source>Physical</source>
        <translation>実際の大きさ</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="57"/>
        <source>Universe</source>
        <translation>Universe</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="71"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="81"/>
        <source>Address</source>
        <translation>スタートアドレス</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="144"/>
        <location filename="addrgbpanel.ui" line="167"/>
        <source>mm</source>
        <translation> mm</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="105"/>
        <source>Components</source>
        <translation>種類</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="273"/>
        <source>Displacement</source>
        <translation>配置</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="279"/>
        <source>Snake</source>
        <translation>蛇行</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="289"/>
        <source>Zig Zag</source>
        <translation>ジグザグ</translation>
    </message>
</context>
<context>
    <name>AddVCButtonMatrix</name>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="33"/>
        <source>Add Button Matrix</source>
        <translation>複数ボタンをまとめて配置</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="40"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="45"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="53"/>
        <source>Add functions to be attached to the buttons in the matrix</source>
        <translation>ファンクションの割り当て</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="73"/>
        <source>Remove selected functions from the list of functions to attach</source>
        <translation>選択したファンクションの割り当てを解除します</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="106"/>
        <source>Dimensions</source>
        <translation>配置</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="112"/>
        <source>Horizontal button count</source>
        <translation>横方向</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="119"/>
        <source>Number of buttons per horizontal row</source>
        <translation>横方向のボタン数</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="132"/>
        <source>Button size</source>
        <translation>大きさ</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="139"/>
        <source>Created buttons&apos; size</source>
        <translation>ボタンの大きさ</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="142"/>
        <source> px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="155"/>
        <source>Vertical button count</source>
        <translation>縦方向</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="162"/>
        <source>Number of buttons per vertical column</source>
        <translation>縦方向のボタン数</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="172"/>
        <source>Allocation</source>
        <translation>ファンクション数/ボタン数</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="179"/>
        <source>Functions / Buttons</source>
        <translation>ファンクション数/ボタン数</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="192"/>
        <source>Frame</source>
        <translation>フレーム</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="198"/>
        <source>Place the buttons inside a normal frame</source>
        <translation>通常フレームの中にボタンを配置</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="201"/>
        <source>Normal</source>
        <translation>通常</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="208"/>
        <source>Place the buttons inside a frame that ensures that only one of them is pressed at a time</source>
        <translation>ソロフレームの中にボタンを配置</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="211"/>
        <source>Solo</source>
        <translation>ソロ</translation>
    </message>
</context>
<context>
    <name>AddVCSliderMatrix</name>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="33"/>
        <source>Add Slider Matrix</source>
        <translation>複数フェーダーをまとめて配置</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="39"/>
        <source>Sliders</source>
        <translation>フェーダー</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="45"/>
        <source>Amount</source>
        <translation>数</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="52"/>
        <source>Number of sliders to create</source>
        <translation>作成するフェーダーの数</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="62"/>
        <source>Height</source>
        <translation>高さ</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="69"/>
        <source>Vertical height of each slider</source>
        <translation>フェーダーの縦方向の長さ</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="72"/>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="95"/>
        <source>px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="88"/>
        <source>Width</source>
        <translation>幅</translation>
    </message>
</context>
<context>
    <name>AddressTool</name>
    <message>
        <location filename="addresstool.ui" line="33"/>
        <source>Address Tool</source>
        <translation>ディップスイッチ設定用ツール</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="41"/>
        <source>Reverse vertically</source>
        <translation>上下反転</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="54"/>
        <source>Colour</source>
        <oldsource>Color</oldsource>
        <translation>色</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="70"/>
        <source>Address</source>
        <translation>DMXアドレス</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="117"/>
        <source>Reverse Horizontally</source>
        <translation>左右反転</translation>
    </message>
</context>
<context>
    <name>App</name>
    <message>
        <location filename="app.cpp" line="371"/>
        <source>Cannot exit in Operate mode</source>
        <translation>本番モード中は終了できません</translation>
    </message>
    <message>
        <location filename="app.cpp" line="372"/>
        <source>You must switch back to Design mode to close the application.</source>
        <translation>仕込みモードに戻ってから閉じてください。</translation>
    </message>
    <message>
        <location filename="app.cpp" line="380"/>
        <source>Close</source>
        <translation>閉じる</translation>
    </message>
    <message>
        <location filename="app.cpp" line="380"/>
        <source>Do you wish to save the current workspace before closing the application?</source>
        <translation>閉じる前に、現在のプロジェクトを保存しますか？</translation>
    </message>
    <message>
        <location filename="app.cpp" line="437"/>
        <source>Starting Q Light Controller Plus</source>
        <oldsource>Starting Q Light Controller</oldsource>
        <translation>QLC+ を開始</translation>
    </message>
    <message>
        <location filename="app.cpp" line="523"/>
        <source> - New Workspace</source>
        <translation> - 新規プロジェクト</translation>
    </message>
    <message>
        <location filename="app.cpp" line="591"/>
        <source>Switch to Design Mode</source>
        <translation>仕込みモードにする</translation>
    </message>
    <message>
        <location filename="app.cpp" line="592"/>
        <source>There are still running functions.
Really stop them and switch back to Design mode?</source>
        <translation>再生中のシーンがあります。
すべて停止して仕込みモードに戻りますか？</translation>
    </message>
    <message>
        <location filename="app.cpp" line="627"/>
        <source>Design</source>
        <translation>仕込みモード</translation>
    </message>
    <message>
        <location filename="app.cpp" line="628"/>
        <source>Switch to design mode</source>
        <translation>仕込みモードにする</translation>
    </message>
    <message>
        <location filename="app.cpp" line="639"/>
        <source>Operate</source>
        <translation>本番モード</translation>
    </message>
    <message>
        <location filename="app.cpp" line="640"/>
        <location filename="app.cpp" line="668"/>
        <source>Switch to operate mode</source>
        <translation>本番モードにする</translation>
    </message>
    <message>
        <location filename="app.cpp" line="651"/>
        <source>&amp;New</source>
        <translation>新規</translation>
    </message>
    <message>
        <location filename="app.cpp" line="652"/>
        <source>CTRL+N</source>
        <comment>File|New</comment>
        <translation>CTRL+N</translation>
    </message>
    <message>
        <location filename="app.cpp" line="655"/>
        <source>&amp;Open</source>
        <translation>開く</translation>
    </message>
    <message>
        <location filename="app.cpp" line="656"/>
        <source>CTRL+O</source>
        <comment>File|Open</comment>
        <translation>CTRL+O</translation>
    </message>
    <message>
        <location filename="app.cpp" line="659"/>
        <source>&amp;Save</source>
        <translation>上書き保存</translation>
    </message>
    <message>
        <location filename="app.cpp" line="660"/>
        <source>CTRL+S</source>
        <comment>File|Save</comment>
        <translation>CTRL+S</translation>
    </message>
    <message>
        <location filename="app.cpp" line="663"/>
        <source>Save &amp;As...</source>
        <translation>名前を付けて保存</translation>
    </message>
    <message>
        <location filename="app.cpp" line="667"/>
        <source>&amp;Operate</source>
        <translation>本番モード</translation>
    </message>
    <message>
        <location filename="app.cpp" line="672"/>
        <source>&amp;Monitor</source>
        <translation>モニタ</translation>
    </message>
    <message>
        <location filename="app.cpp" line="679"/>
        <source>Toggle &amp;Blackout</source>
        <translation>暗転</translation>
    </message>
    <message>
        <location filename="app.cpp" line="669"/>
        <source>CTRL+F12</source>
        <comment>Control|Toggle operate/design mode</comment>
        <translation>CTRL+F12</translation>
    </message>
    <message>
        <location filename="app.cpp" line="673"/>
        <source>CTRL+M</source>
        <comment>Control|Monitor</comment>
        <translation>CTRL+M</translation>
    </message>
    <message>
        <location filename="app.cpp" line="684"/>
        <source>Live edit a function</source>
        <translation>再生中修正</translation>
    </message>
    <message>
        <location filename="app.cpp" line="724"/>
        <source>Toggle Full Screen</source>
        <translation>フルスクリーン</translation>
    </message>
    <message>
        <location filename="app.cpp" line="726"/>
        <source>CTRL+F11</source>
        <comment>Control|Toggle Full Screen</comment>
        <translation>CTRL+F11</translation>
    </message>
    <message>
        <location filename="app.cpp" line="730"/>
        <source>&amp;Index</source>
        <translation>ヘルプ</translation>
    </message>
    <message>
        <location filename="app.cpp" line="731"/>
        <source>SHIFT+F1</source>
        <comment>Help|Index</comment>
        <translation>SHIFT+F1</translation>
    </message>
    <message>
        <location filename="app.cpp" line="734"/>
        <source>&amp;About QLC+</source>
        <oldsource>&amp;About QLC</oldsource>
        <translation>&amp;QLC+ について</translation>
    </message>
    <message>
        <location filename="app.cpp" line="282"/>
        <source>Fixtures</source>
        <translation>機器</translation>
    </message>
    <message>
        <location filename="app.cpp" line="284"/>
        <source>Functions</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="app.cpp" line="286"/>
        <source>Shows</source>
        <translation>タイムライン</translation>
    </message>
    <message>
        <location filename="app.cpp" line="288"/>
        <source>Virtual Console</source>
        <translation>バーチャルコンソール</translation>
    </message>
    <message>
        <location filename="app.cpp" line="290"/>
        <source>Simple Desk</source>
        <translation>シンプル卓</translation>
    </message>
    <message>
        <location filename="app.cpp" line="292"/>
        <source>Inputs/Outputs</source>
        <translation>入力/出力設定</translation>
    </message>
    <message>
        <location filename="app.cpp" line="394"/>
        <source>Close the application?</source>
        <translation>QLC+ を終了</translation>
    </message>
    <message>
        <location filename="app.cpp" line="395"/>
        <source>Do you wish to close the application?</source>
        <translation>QLC+ を終了しますか？</translation>
    </message>
    <message>
        <location filename="app.cpp" line="574"/>
        <source>Exit</source>
        <translation>閉じる</translation>
    </message>
    <message>
        <location filename="app.cpp" line="676"/>
        <source>Address Tool</source>
        <translation>ディップスイッチ設定用ツール</translation>
    </message>
    <message>
        <location filename="app.cpp" line="688"/>
        <source>Toggle Virtual Console Live edit</source>
        <translation>本番モード時にバーチャルコンソールを編集</translation>
    </message>
    <message>
        <location filename="app.cpp" line="693"/>
        <source>Dump DMX values to a function</source>
        <translation>現在のDMX値をシーンにする</translation>
    </message>
    <message>
        <location filename="app.cpp" line="694"/>
        <source>CTRL+D</source>
        <comment>Control|Dump DMX</comment>
        <translation>CTRL+D</translation>
    </message>
    <message>
        <location filename="app.cpp" line="697"/>
        <source>Stop ALL functions!</source>
        <translation>全ファンクション停止</translation>
    </message>
    <message>
        <location filename="app.cpp" line="702"/>
        <source>Fade 1 second and stop</source>
        <translation>1秒フェードで停止</translation>
    </message>
    <message>
        <location filename="app.cpp" line="707"/>
        <source>Fade 5 seconds and stop</source>
        <translation>5秒フェードで停止</translation>
    </message>
    <message>
        <location filename="app.cpp" line="712"/>
        <source>Fade 10 second and stop</source>
        <translation>10秒フェードで停止</translation>
    </message>
    <message>
        <location filename="app.cpp" line="717"/>
        <source>Fade 30 second and stop</source>
        <translation>30秒フェードで停止</translation>
    </message>
    <message>
        <location filename="app.cpp" line="739"/>
        <source>Quit QLC+</source>
        <translation>QLC+を終了します</translation>
    </message>
    <message>
        <location filename="app.cpp" line="747"/>
        <source>Workspace</source>
        <translation>プロジェクト</translation>
    </message>
    <message>
        <location filename="app.cpp" line="805"/>
        <source>Unable to read from file</source>
        <translation>ファイル読み取り不可</translation>
    </message>
    <message>
        <location filename="app.cpp" line="808"/>
        <source>Unable to write to file</source>
        <translation>ファイル書き込み不可</translation>
    </message>
    <message>
        <location filename="app.cpp" line="811"/>
        <source>A fatal error occurred</source>
        <translatorcomment>...la vilaine!</translatorcomment>
        <translation>深刻なエラーが発生しました</translation>
    </message>
    <message>
        <location filename="app.cpp" line="814"/>
        <source>Unable to access resource</source>
        <translation>リソースにアクセスできません</translation>
    </message>
    <message>
        <location filename="app.cpp" line="817"/>
        <source>Unable to open file for reading or writing</source>
        <translation>読み書きのためのファイルを開けません</translation>
    </message>
    <message>
        <location filename="app.cpp" line="820"/>
        <source>Operation was aborted</source>
        <translation>操作は中断されました</translation>
    </message>
    <message>
        <location filename="app.cpp" line="823"/>
        <source>Operation timed out</source>
        <translation>操作はタイムアウトしました</translation>
    </message>
    <message>
        <location filename="app.cpp" line="827"/>
        <source>An unspecified error has occurred. Nice.</source>
        <translation>予期しないエラーが発生しました。な、何だってー！？</translation>
    </message>
    <message>
        <location filename="app.cpp" line="831"/>
        <source>File error</source>
        <translation>ファイルエラー</translation>
    </message>
    <message>
        <location filename="app.cpp" line="925"/>
        <location filename="app.cpp" line="941"/>
        <location filename="app.cpp" line="1257"/>
        <source>Do you wish to save the current workspace?
Changes will be lost if you don&apos;t save them.</source>
        <translatorcomment>Tiens tiens...perspicace!</translatorcomment>
        <translation>開く前に、現在のプロジェクトを保存しますか？</translation>
    </message>
    <message>
        <location filename="app.cpp" line="927"/>
        <source>New Workspace</source>
        <translation>新規プロジェクト</translation>
    </message>
    <message>
        <location filename="app.cpp" line="943"/>
        <location filename="app.cpp" line="951"/>
        <location filename="app.cpp" line="1259"/>
        <source>Open Workspace</source>
        <translation>プロジェクトを開く</translation>
    </message>
    <message>
        <location filename="app.cpp" line="959"/>
        <location filename="app.cpp" line="1042"/>
        <source>Workspaces (*%1)</source>
        <translation>プロジェクト (*%1)</translation>
    </message>
    <message>
        <location filename="app.cpp" line="961"/>
        <location filename="app.cpp" line="1044"/>
        <source>All Files (*.*)</source>
        <translation>すべてのファイル (*.*)</translation>
    </message>
    <message>
        <location filename="app.cpp" line="963"/>
        <location filename="app.cpp" line="1046"/>
        <source>All Files (*)</source>
        <translation>すべてのファイル (*)</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1036"/>
        <source>Save Workspace As</source>
        <translation>プロジェクトを名前を付けて保存</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1250"/>
        <source>Error</source>
        <translation>エラー</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1251"/>
        <source>File not found!
The selected file has been moved or deleted.</source>
        <translation>ファイルが見つかりません。
移動または削除された可能性があります。</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1427"/>
        <source>Warning</source>
        <translation>警告</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1428"/>
        <source>Some errors occurred while loading the project:</source>
        <translation>次のプロジェクトを読み込み中に何らかのエラーが発生しました:</translation>
    </message>
</context>
<context>
    <name>AssignHotKey</name>
    <message>
        <location filename="assignhotkey.ui" line="33"/>
        <source>Assign a key combination to button</source>
        <translation>ボタンにキーボードショートカットを設定</translation>
    </message>
    <message>
        <location filename="assignhotkey.ui" line="42"/>
        <source>Key combination</source>
        <translation>キーボードショートカット</translation>
    </message>
    <message>
        <location filename="assignhotkey.ui" line="62"/>
        <source>Close automatically on key press</source>
        <translation>キー設定後、自動的に閉じる</translation>
    </message>
    <message>
        <location filename="assignhotkey.cpp" line="50"/>
        <source>Assign Key</source>
        <translation>キーを押してください(Shift などとの複合も可)</translation>
    </message>
    <message>
        <location filename="assignhotkey.cpp" line="51"/>
        <source>Hit the key combination that you wish to assign. You may hit either a single key or a combination using %1, %2, and %3.</source>
        <translation>ショートカットに設定したいキーを押してください。 %1, %2, %3 との複合も可能です。</translation>
    </message>
</context>
<context>
    <name>AudioEditor</name>
    <message>
        <location filename="audioeditor.ui" line="33"/>
        <source>Audio editor</source>
        <translation>オーディオエディタ</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="41"/>
        <source>Playback mode</source>
        <translation>プレイバックモード</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="47"/>
        <source>Single shot</source>
        <translation>一方通行</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="57"/>
        <source>Loop</source>
        <translation>繰り返し</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="209"/>
        <source>Bitrate</source>
        <oldsource>Bitrate:</oldsource>
        <translation>ビットレート</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="226"/>
        <source>Play the audio file</source>
        <translation>音声ファイル</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="300"/>
        <source>Duration</source>
        <oldsource>Duration:</oldsource>
        <translation>継続時間</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="279"/>
        <source>File name</source>
        <translation>ファイル名</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="113"/>
        <source>Audio name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="293"/>
        <source>Sample rate</source>
        <oldsource>Sample rate:</oldsource>
        <translation>サンプリングレート</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="173"/>
        <source>Name of the function being edited</source>
        <translation>ファンクションの名前</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="127"/>
        <source>Audio device</source>
        <translation>再生デバイス</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="74"/>
        <source>Channels</source>
        <oldsource>Channels:</oldsource>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="246"/>
        <source>Show/Hide speed dial window</source>
        <translation>スピードダイアルの表示/非表示</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="314"/>
        <source>Fade in</source>
        <oldsource>Fade in:</oldsource>
        <translation>フェードイン</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="134"/>
        <source>Fade out</source>
        <oldsource>Fade out:</oldsource>
        <translation>フェードアウト</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="84"/>
        <source>Default device</source>
        <translation>既定のデバイス</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="140"/>
        <source>Open Audio File</source>
        <translation>オーディオファイルを開く</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="148"/>
        <source>Audio Files (%1)</source>
        <translation>オーディオファイル (%1)</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="150"/>
        <source>All Files (*.*)</source>
        <translation>すべてのファイル (*.*)</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="152"/>
        <source>All Files (*)</source>
        <translation>すべてのファイル (*)</translation>
    </message>
</context>
<context>
    <name>AudioItem</name>
    <message>
        <location filename="showmanager/audioitem.cpp" line="55"/>
        <location filename="showmanager/audioitem.cpp" line="196"/>
        <source>Preview Left Channel</source>
        <translation>左チャンネルを可視化</translation>
    </message>
    <message>
        <location filename="showmanager/audioitem.cpp" line="59"/>
        <source>Preview Right Channel</source>
        <translation>右チャンネルを可視化</translation>
    </message>
    <message>
        <location filename="showmanager/audioitem.cpp" line="63"/>
        <source>Preview Stereo Channels</source>
        <translation>ステレオ音声を可視化</translation>
    </message>
    <message>
        <location filename="showmanager/audioitem.cpp" line="192"/>
        <source>Preview Mono</source>
        <translation>モノラル音声を可視化</translation>
    </message>
</context>
<context>
    <name>AudioTriggersConfiguration</name>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="33"/>
        <source>Audio Triggers Configuration</source>
        <translation>オーディオトリガーの設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="116"/>
        <source>Number of spectrum bars:</source>
        <translation>スペクトラムバーの数(バンド数)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="59"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="43"/>
        <source>Triggers</source>
        <translation>トリガー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="51"/>
        <source>Widget name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="64"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="69"/>
        <source>Assign</source>
        <translation>アサイン</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="74"/>
        <source>Info</source>
        <translation>状態</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="79"/>
        <source>Disable threshold</source>
        <translation>無効になる閾値</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="84"/>
        <source>Enable threshold</source>
        <translation>有効になる閾値</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="89"/>
        <source>Divisor</source>
        <translation>Divisor</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="132"/>
        <source>Input</source>
        <translation>入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="115"/>
        <source>None</source>
        <translation>無し</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="116"/>
        <source>DMX</source>
        <translation>DMX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="117"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="118"/>
        <source>VC Widget</source>
        <translation>部品</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="131"/>
        <source>%1 channels</source>
        <translation>%1 チャンネル</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="147"/>
        <source>No function</source>
        <translation>ファンクション無し</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="165"/>
        <source>No widget</source>
        <translation>部品無し</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="171"/>
        <source>Not assigned</source>
        <translation>アサイン無し</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="225"/>
        <source>Volume Bar</source>
        <translation>ボリュームバー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="234"/>
        <source>#%1 (%2Hz - %3Hz)</source>
        <translation>#%1 (%2Hz - %3Hz)</translation>
    </message>
</context>
<context>
    <name>ChannelModifierEditor</name>
    <message>
        <location filename="channelmodifiereditor.ui" line="33"/>
        <source>Channel Modifier Editor</source>
        <translation>ディマーカーブエディタ</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="50"/>
        <source>Modified DMX value</source>
        <translation>出力されるDMX値</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="63"/>
        <source>Original DMX value</source>
        <translation>入力されるDMX値</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="81"/>
        <source>Remove the selected handler</source>
        <translation>選択したハンドルを削除します</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="98"/>
        <source>Overwrite the current template</source>
        <translation>テンプレートを上書きます</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="115"/>
        <source>Add a new handler</source>
        <translation>ハンドルを追加します</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="149"/>
        <source>Templates</source>
        <translation>テンプレート</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="161"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="168"/>
        <source>New Template</source>
        <translation>新しいテンプレートの作成</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="181"/>
        <source>Unset Modifier</source>
        <translation>リセット</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.cpp" line="183"/>
        <source>Error</source>
        <translation>エラー</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.cpp" line="184"/>
        <source>You are trying to overwrite a system template! Please choose another name and the template will be saved in your channel modifier&apos;s user folder.</source>
        <translation>QLC+のデフォルトから入っているテンプレートは上書きできません、別名で保存してください</translation>
    </message>
</context>
<context>
    <name>ChannelsSelection</name>
    <message>
        <location filename="channelsselection.ui" line="33"/>
        <source>Channels selection</source>
        <oldsource>Channels Fade Configuration</oldsource>
        <translation>チャンネルフェード設定</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="47"/>
        <location filename="channelsselection.cpp" line="50"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="52"/>
        <location filename="channelsselection.cpp" line="50"/>
        <source>Type</source>
        <translation>属性</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="62"/>
        <source>Apply changes to fixtures of the same type</source>
        <translation>同じ属性の機器にも変更を適用</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="69"/>
        <source>Collapse all</source>
        <translation>全て折りたたむ</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="76"/>
        <source>Expand all</source>
        <translation>全て開く</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="54"/>
        <source>Selected</source>
        <translation>選択</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="58"/>
        <source>Channel properties configuration</source>
        <translation>チャンネルの設定を開く</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="60"/>
        <source>Can fade</source>
        <translation>フェード操作を許可する</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="60"/>
        <source>Behaviour</source>
        <translation>ブレンドモード</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="60"/>
        <source>Modifier</source>
        <translation>ディマーカーブ</translation>
    </message>
</context>
<context>
    <name>ChaserEditor</name>
    <message>
        <location filename="chasereditor.ui" line="33"/>
        <source>Chaser editor</source>
        <translation>チェイスエディタ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="142"/>
        <source>Chaser name</source>
        <translation>チェイス名</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="155"/>
        <source>Name of the chaser being edited</source>
        <translation>チェイス名</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="79"/>
        <source>Step</source>
        <translation>#</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="84"/>
        <source>Function</source>
        <translation>ステップ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="89"/>
        <location filename="chasereditor.cpp" line="983"/>
        <source>Fade In</source>
        <translation>フェードイン</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="99"/>
        <location filename="chasereditor.cpp" line="984"/>
        <source>Fade Out</source>
        <translation>フェードアウト</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="104"/>
        <source>Duration</source>
        <translation>継続時間</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="741"/>
        <source>Add step(s) to the current position</source>
        <translation>ステップの追加</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="595"/>
        <source>Show/Hide speed dial window</source>
        <translation>スピードダイヤルの表示/非表示</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="94"/>
        <location filename="chasereditor.cpp" line="985"/>
        <source>Hold</source>
        <translation>ホールド</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="109"/>
        <source>Notes</source>
        <translation>メモ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="228"/>
        <source>Switch direction at both ends</source>
        <translation>往復(1→2→3→2→1→…)</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="238"/>
        <source>Execute steps in random order</source>
        <translation>ランダム</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="241"/>
        <source>Random</source>
        <translation>ランダム</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="326"/>
        <source>Fade In Speed</source>
        <translation>フェードイン時間</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="353"/>
        <source>All steps have common fade in speed set by the chaser</source>
        <translation>全ステップで共通のフェードイン時間を設定</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="356"/>
        <location filename="chasereditor.ui" line="431"/>
        <location filename="chasereditor.ui" line="509"/>
        <source>Common</source>
        <translation>共通</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="369"/>
        <source>Each step has its own fade in speed set by the chaser</source>
        <translation>ステップごとに個別のフェードイン時間を設定</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="372"/>
        <location filename="chasereditor.ui" line="447"/>
        <location filename="chasereditor.ui" line="528"/>
        <source>Per Step</source>
        <translation>個別</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="385"/>
        <source>Use each function&apos;s own fade in speed</source>
        <translation>引用元シーン(orファンクション)で設定したフェードイン時間を流用</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="388"/>
        <location filename="chasereditor.ui" line="463"/>
        <source>Default</source>
        <translation>既定</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="401"/>
        <source>Fade Out Speed</source>
        <translation>フェードアウト時間</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="428"/>
        <source>All steps have common fade out speed set by the chaser</source>
        <translation>全ステップで共通のフェードアウト時間を設定</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="444"/>
        <source>Each step has its own fade out speed set by the chaser</source>
        <translation>ステップごとに個別のフェードアウト時間を設定</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="460"/>
        <source>Use each function&apos;s own fade out speed</source>
        <translation>引用元シーン(orファンクション)で設定したフェードアウト時間を流用</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="476"/>
        <source>Step Duration</source>
        <translation>ステップ継続時間</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="506"/>
        <source>All steps have common step duration set by the chaser</source>
        <translation>全ステップで共通の継続時間を設定</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="525"/>
        <source>Each step has its own duration set by the chaser</source>
        <translation>ステップごとに個別の継続時間を設定</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="622"/>
        <source>See what the chaser does when it is run</source>
        <translation>プレビュー</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="639"/>
        <source>Stop the chaser if running</source>
        <translation>プレビュー停止</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="673"/>
        <source>Skip to the next step</source>
        <translation>次のステップへ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="782"/>
        <source>Shuffle steps</source>
        <translation>ランダム</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="656"/>
        <source>Skip to the previous step</source>
        <translation>前のステップへ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="117"/>
        <source>Remove the selected step</source>
        <translation>選択したステップの削除</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="758"/>
        <source>Raise the selected step once</source>
        <translation>選択したステップを1つ上へ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="556"/>
        <source>Lower the selected step once</source>
        <translation>選択したステップを1つ下へ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="166"/>
        <source>Run Order</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="193"/>
        <source>Run through over and over again</source>
        <translation>繰り返し(1→2→3→1→2→3→…)</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="196"/>
        <source>Loop</source>
        <translation>繰り返し</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="212"/>
        <source>Run through once and stop</source>
        <translation>1回だけ再生(1→2→3)</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="215"/>
        <source>Single Shot</source>
        <translation>一方通行</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="231"/>
        <source>Ping Pong</source>
        <translation>往復</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="251"/>
        <source>Direction</source>
        <translation>順/逆</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="278"/>
        <source>Start from the first step</source>
        <translation>ステップ1から再生</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="281"/>
        <source>Forward</source>
        <translation>順再生</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="297"/>
        <source>Start from the last step</source>
        <translation>最後のステップから再生</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="300"/>
        <source>Backward</source>
        <translation>逆再生</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="75"/>
        <source>Cut</source>
        <translation>カット</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="80"/>
        <source>Copy</source>
        <translation>コピー</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="85"/>
        <source>Paste</source>
        <translation>ペースト</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="754"/>
        <source>Paste error</source>
        <translation>ペーストエラー</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="754"/>
        <source>Trying to paste on an incompatible Scene. Operation canceled.</source>
        <translation>ペーストできないシーンです。</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="986"/>
        <source>Common Fade In</source>
        <translation>全体F.I.時間</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="987"/>
        <source>Common Fade Out</source>
        <translation>全体F.O.時間</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="988"/>
        <source>Common Hold</source>
        <translation>全体ホールド時間</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="1004"/>
        <source>Multiple Steps</source>
        <translation>複数ステップ</translation>
    </message>
</context>
<context>
    <name>CollectionEditor</name>
    <message>
        <location filename="collectioneditor.ui" line="33"/>
        <source>Collection editor</source>
        <translation>コレクションエディタ</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="39"/>
        <source>Move the selected functions down</source>
        <translation>1つ下へ移動</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="56"/>
        <source>Move the selected functions up</source>
        <translation>1つ上へ移動</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="79"/>
        <source>Collection name</source>
        <translation>コレクション名</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="86"/>
        <source>Name of the function being edited</source>
        <translation>コレクション名</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="146"/>
        <source>See what the Collection does when it is run</source>
        <translation>コレクション実行時の動作を確認</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="188"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="93"/>
        <source>Add function(s) to the collection</source>
        <translation>ファンクションをコレクションに追加</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="113"/>
        <source>Remove the selected function</source>
        <translation>選択したファンクションを削除</translation>
    </message>
</context>
<context>
    <name>ConsoleChannel</name>
    <message>
        <location filename="consolechannel.cpp" line="163"/>
        <source>Intensity</source>
        <translation>明るさ</translation>
    </message>
    <message>
        <location filename="consolechannel.cpp" line="313"/>
        <source>Reset this channel</source>
        <translation>リセット</translation>
    </message>
</context>
<context>
    <name>CreateFixtureGroup</name>
    <message>
        <location filename="createfixturegroup.ui" line="33"/>
        <source>Create Fixture Group</source>
        <translation>機器グループを作成</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="39"/>
        <source>Group name</source>
        <translation>グループ名</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="51"/>
        <source>Initial size</source>
        <translation>初期サイズ</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="57"/>
        <source>Width</source>
        <translation>幅</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="74"/>
        <source>Height</source>
        <translation>高さ</translation>
    </message>
</context>
<context>
    <name>CueStackModel</name>
    <message>
        <location filename="cuestackmodel.cpp" line="144"/>
        <source>Number</source>
        <translation>No.</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="146"/>
        <source>Fade In</source>
        <translation>フェードイン</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="148"/>
        <source>Fade Out</source>
        <translation>フェードアウト</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="150"/>
        <source>Duration</source>
        <translation>再生時間</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="152"/>
        <source>Cue</source>
        <translation>キュー</translation>
    </message>
</context>
<context>
    <name>DmxDumpFactory</name>
    <message>
        <location filename="dmxdumpfactory.ui" line="33"/>
        <source>Dump DMX values</source>
        <translation>現在のDMX値をシーンにする(Ctrl+D)</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="43"/>
        <source>Dump only non-zero values</source>
        <translation>0でないチャンネルのみ出力</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="94"/>
        <source>Dump options</source>
        <translation>オプション</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="177"/>
        <source>Dump selected channels</source>
        <oldsource>Dump selected DMX values</oldsource>
        <translation>選択したチャンネルのみ出力</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="121"/>
        <source>Add to:</source>
        <translation>このシーンを追加:</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="72"/>
        <source>Select an existing Scene to overwrite</source>
        <translation>選択したシーンを上書き</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="104"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="128"/>
        <source>Chaser</source>
        <translation>チェイス</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="138"/>
        <source>Button</source>
        <translation>ボタン</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="145"/>
        <source>Slider</source>
        <translation>フェーダー</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="62"/>
        <source>Scene name:</source>
        <translation>シーン名</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.cpp" line="74"/>
        <source>Dump all channels (%1 Universes, %2 Fixtures, %3 Channels)</source>
        <oldsource>Dump all DMX values (%1 Universes, %2 Fixtures, %3 Channels)</oldsource>
        <translation>すべてのチャンネルを出力 (%1 個のuniverse, %2 個のDMX機器, %3 チャンネル)</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.cpp" line="77"/>
        <source>New Scene From Live %1</source>
        <translation>New Scene From Live %1</translation>
    </message>
</context>
<context>
    <name>DocBrowser</name>
    <message>
        <location filename="docbrowser.cpp" line="105"/>
        <source>%1 - Document Browser</source>
        <translation>%1 - ヘルプ</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="127"/>
        <source>Backward</source>
        <translation>前へ</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="128"/>
        <source>Forward</source>
        <translation>次へ</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="129"/>
        <source>Index</source>
        <translation>Aide</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="130"/>
        <source>About Qt</source>
        <translation>Qt について</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="131"/>
        <source>Close this window</source>
        <translation>ウィンドウを閉じる</translation>
    </message>
</context>
<context>
    <name>EFXEditor</name>
    <message>
        <location filename="efxeditor.ui" line="33"/>
        <source>EFX Editor</source>
        <translation>EFXエディタ</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="46"/>
        <source>General</source>
        <translatorcomment>...mon Caporal!</translatorcomment>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="715"/>
        <source>EFX name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="722"/>
        <source>The name of the function being edited</source>
        <translation>このEFXの名前</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="182"/>
        <source>Step</source>
        <translation>ステップ</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="187"/>
        <source>Fixture</source>
        <translation>機器</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="197"/>
        <source>Reverse</source>
        <translation>逆</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="101"/>
        <source>Fixture order</source>
        <translation>並び順</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="107"/>
        <source>All fixtures move in parallel</source>
        <translatorcomment>&quot;bouger&quot; n&apos;est peut-etre pas le meilleur verbe</translatorcomment>
        <translation>すべての機器が同じ動きをします。</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="110"/>
        <source>Parallel</source>
        <translation>並行</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="120"/>
        <source>The pattern propagates to each fixture in a sequential order</source>
        <translation>このパターンの拡散方向</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="123"/>
        <source>Serial</source>
        <translation>連続</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="224"/>
        <source>Movement</source>
        <translation>動き</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="202"/>
        <location filename="efxeditor.ui" line="303"/>
        <source>Start Offset</source>
        <translation>スタートオフセット</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="130"/>
        <source>Each fixture starts moving immediately with an offset</source>
        <translation>すぐに全てのフィクスチャーにオフセットを設定します</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="133"/>
        <source>Asymmetric</source>
        <translation>非対称</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="252"/>
        <source>Pattern</source>
        <translation>パターン</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="264"/>
        <source>Pattern for moving the mirror/head</source>
        <translation>ミラースキャン/ムービングヘッドの動作パターン</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="274"/>
        <source>Parameters</source>
        <translation>パラメータ</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="296"/>
        <source>Width</source>
        <translation>幅</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="501"/>
        <source>Value width of the pattern</source>
        <translation>パターンの幅</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="467"/>
        <source>Height</source>
        <translation>高さ</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="514"/>
        <source>Value height of the pattern</source>
        <translation>パターンの高さ</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="481"/>
        <source>X offset</source>
        <translation>オフセット X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="534"/>
        <source>Pattern&apos;s center point on the X axis</source>
        <translation>X軸方向の中心点</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="474"/>
        <source>Y offset</source>
        <translation>オフセット Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="488"/>
        <source>Pattern&apos;s center point on the Y axis</source>
        <translation>Y軸方向の中心点</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="527"/>
        <source>Rotation</source>
        <translation>回転</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="323"/>
        <source>Rotation of the pattern&apos;s starting point</source>
        <translation>パターンの回転</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="326"/>
        <location filename="efxeditor.ui" line="424"/>
        <source>°</source>
        <translation>°</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="316"/>
        <source>X frequency</source>
        <translation>周波数 X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="404"/>
        <source>Lissajous pattern&apos;s X frequency</source>
        <translation>リサジュー図形の周波数 X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="394"/>
        <source>Y frequency</source>
        <translation>周波数 Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="378"/>
        <source>Lissajous pattern&apos;s Y frequency</source>
        <translation>リサジュー図形の周波数 Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="368"/>
        <source>X phase</source>
        <translation>位相差 X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="339"/>
        <source>Lissajous pattern&apos;s X phase</source>
        <translation>リサジュー図形の位相差 X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="437"/>
        <source>Y phase</source>
        <translation>位相差 Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="669"/>
        <source>Show/Hide speed dial window</source>
        <translation>スピードダイヤルの表示/非表示</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="454"/>
        <source>Lissajous pattern&apos;s Y phase</source>
        <translation>リサジュー図形の位相差 Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="192"/>
        <source>Mode</source>
        <translation>モード</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="417"/>
        <source>Relative</source>
        <translation>Relative</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="547"/>
        <source>Color Background</source>
        <translation>背景色</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="557"/>
        <source>Direction</source>
        <translation>方向</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="563"/>
        <source>Run the pattern forwards</source>
        <translation>順再生</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="566"/>
        <source>Forward</source>
        <translation>順再生</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="576"/>
        <source>Run the pattern backwards</source>
        <translation>逆再生</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="579"/>
        <source>Backward</source>
        <translation>逆再生</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="605"/>
        <source>Run order</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="611"/>
        <source>Run through over and over again</source>
        <translation>繰り返し</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="614"/>
        <source>Loop</source>
        <translation>繰り返し</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="624"/>
        <source>Run through once and stop</source>
        <translation>一方通行</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="627"/>
        <source>Single shot</source>
        <translation>一方通行</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="634"/>
        <source>First run forwards, then backwards, again forwards, etc...</source>
        <translation>往復</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="637"/>
        <source>Ping pong</source>
        <translation>往復</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="689"/>
        <source>See what the EFX does when it is run</source>
        <translation>作成したEFXのプレビュー</translation>
    </message>
    <message>
        <location filename="efxeditor.cpp" line="736"/>
        <source>Remove fixtures</source>
        <translation>機器の削除</translation>
    </message>
    <message>
        <location filename="efxeditor.cpp" line="737"/>
        <source>Do you want to remove the selected fixture(s)?</source>
        <translation>選択した機器を削除しますか？</translation>
    </message>
</context>
<context>
    <name>FixtureGroupEditor</name>
    <message>
        <location filename="fixturegroupeditor.ui" line="33"/>
        <source>Fixture Group</source>
        <translation>機器グループ</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="183"/>
        <source>Fixture group name</source>
        <translation>機器グループ名</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="58"/>
        <source>Remove selected fixture/head</source>
        <translation>選択した機器の削除</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="88"/>
        <source>Width</source>
        <translation>幅</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="95"/>
        <location filename="fixturegroupeditor.ui" line="128"/>
        <source>px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="108"/>
        <source>Height</source>
        <translation>高さ</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="141"/>
        <source>Add/replace fixtures to current row, starting from selected cell</source>
        <translation>選択したセルか、横方向の端にフィクスチャーを追加します。</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="158"/>
        <source>Add/replace fixtures to current column, starting from selected cell</source>
        <translation>選択したセルか、縦方向の端にフィクスチャーを追加します。</translation>
    </message>
</context>
<context>
    <name>FixtureManager</name>
    <message>
        <location filename="fixturemanager.cpp" line="366"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="362"/>
        <source>Fixtures Groups</source>
        <translation>機器グループ</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="366"/>
        <source>Channels</source>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="378"/>
        <source>Channels Groups</source>
        <translation>チャンネルグループ</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="611"/>
        <source>&lt;H1&gt;Multiple fixtures selected&lt;/H1&gt;&lt;P&gt;Click &lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt; to remove the selected fixtures.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;複数の機器が選択されています&lt;/H1&gt;&lt;P&gt;&lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt;で選択した機器を削除できます。&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="637"/>
        <source>&lt;H1&gt;Multiple fixtures selected&lt;/H1&gt;&lt;P&gt;Fixture list modification is not permitted in operate mode.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;複数の機器が選択されています&lt;/H1&gt;&lt;P&gt;本番モードの時は、機器リストの編集はできません。&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="646"/>
        <source>&lt;H1&gt;No fixtures&lt;/H1&gt;&lt;P&gt;Click &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; to add fixtures.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;機器を追加しましょう&lt;/H1&gt;&lt;P&gt;&lt;IMG SRC=&quot;:/edit_add.png&quot;&gt;をクリックして、DMX機器を追加しましょう。&lt;BR&gt;
ムービングライト、LED灯体、レーザーなどはメーカー名・機種名を選択します。&lt;BR&gt;
ディマーユニットを使う一般灯体は、Generic &gt; Generic (ディマー)として追加します。&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="652"/>
        <source>&lt;H1&gt;Nothing selected&lt;/H1&gt;&lt;P&gt;Select a fixture from the list or click &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; to add fixtures.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;機器を追加しましょう&lt;/H1&gt;&lt;P&gt;&lt;IMG SRC=&quot;:/edit_add.png&quot;&gt;をクリックして、DMX機器を追加しましょう。&lt;BR&gt;
ムービングライト、LED灯体、レーザーなどはメーカー名・機種名を選択します。&lt;BR&gt;
ディマーユニットを使う一般灯体は、Generic &gt; Generic (ディマー)として追加します。&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="705"/>
        <source>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;Multiple groups selected&lt;/H1&gt;&lt;P&gt;Click &lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt; to remove the selected groups.&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</source>
        <translation>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;複数グループが選択されています&lt;/H1&gt;&lt;P&gt;&lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt;で選択したグループを削除できます。&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="713"/>
        <source>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;Nothing selected&lt;/H1&gt;&lt;P&gt;Select a channel group from the list or click &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; to add a new channels group.&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</source>
        <translation>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;チャンネルグループの追加&lt;/H1&gt;&lt;P&gt;&lt;IMG SRC=&quot;:/edit_add.png&quot;&gt;をクリックして、チャンネルグループを追加できます。&lt;br&gt;複数の機器に同じ機能がある場合、グループ化しておくと便利です。&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="738"/>
        <source>Add group...</source>
        <translation>グループの追加</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="744"/>
        <location filename="fixturemanager.cpp" line="850"/>
        <source>Add fixture...</source>
        <translation>機器の追加</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="855"/>
        <source>Add RGB panel...</source>
        <translation>RGBパネルを追加</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="860"/>
        <source>Delete items</source>
        <translation>削除</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="865"/>
        <source>Properties...</source>
        <translation>詳細...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="870"/>
        <source>Channels Fade Configuration...</source>
        <translation>チャンネルフェード設定</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="876"/>
        <source>Add fixture to group...</source>
        <translation>機器をグループに追加</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="879"/>
        <source>Remove fixture from group</source>
        <translation>機器をグループから削除</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="883"/>
        <source>New Group...</source>
        <translation>新しいグループ</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="886"/>
        <source>Move group up...</source>
        <translation>グループを上へ</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="892"/>
        <source>Move group down...</source>
        <translation>グループを下へ</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="898"/>
        <source>Import fixtures...</source>
        <translation>機器データをインポート</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="903"/>
        <source>Export fixtures...</source>
        <translation>機器データをエクスポート</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="909"/>
        <source>Remap fixtures...</source>
        <translation>機器のリマップ</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1167"/>
        <source>%1 - Row %2</source>
        <translation>%1 - 列 %2</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1242"/>
        <source>Do you want to delete the selected items?</source>
        <translation>選択したアイテムを削除しますか？</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1297"/>
        <source>Delete Channels Group</source>
        <translation>チャンネルグループの削除</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="975"/>
        <location filename="fixturemanager.cpp" line="1416"/>
        <source>Error</source>
        <translation>エラー</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="587"/>
        <source>This group contains all fixtures of</source>
        <translation>グループ内の全てのフィクスチャーを追加</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="591"/>
        <location filename="fixturemanager.cpp" line="632"/>
        <source>Total estimated weight</source>
        <translation>総重量の目安</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="592"/>
        <location filename="fixturemanager.cpp" line="633"/>
        <source>Maximum estimated power consumption</source>
        <translation>総消費電力の目安</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="976"/>
        <location filename="fixturemanager.cpp" line="1417"/>
        <source>Please enter a valid address</source>
        <translation>有効なアドレスを入力してください。</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1493"/>
        <source>Ungroup fixtures?</source>
        <translation>機器のグループ解除</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1494"/>
        <source>Do you want to ungroup the selected fixtures?</source>
        <translation>選択した機器をグループ解除しますか？</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1609"/>
        <source>Import Fixtures List</source>
        <oldsource>Import Fixture Definition</oldsource>
        <translation>機器リストのインポート</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1614"/>
        <source>Export Fixtures List As</source>
        <oldsource>Export Fixture Definition As</oldsource>
        <translation>機器リストを保存</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1620"/>
        <source>Fixtures List (*%1)</source>
        <oldsource>Fixture Definitions (*%1)</oldsource>
        <translation>機器リスト (*%1)</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1622"/>
        <source>All Files (*.*)</source>
        <translation>すべてのファイル (*.*)</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1624"/>
        <source>All Files (*)</source>
        <translation>すべてのファイル (*)</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="942"/>
        <source>Fixture manager</source>
        <translation>機器マネージャー</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1018"/>
        <source>Generic Dimmer</source>
        <translation>一般ディマー</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1241"/>
        <source>Delete Fixtures</source>
        <translation>機器の削除</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1298"/>
        <source>Do you want to delete the selected groups?</source>
        <translation>選択したグループを削除しますか？</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1358"/>
        <source>Change fixture properties</source>
        <translation>機器の詳細を変更</translation>
    </message>
</context>
<context>
    <name>FixtureRemap</name>
    <message>
        <location filename="fixtureremap.ui" line="33"/>
        <source>Fixtures Remap</source>
        <translation>機器のリマップ</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="47"/>
        <source>Add target fixture...</source>
        <translation>リマップ先の機器を追加</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="64"/>
        <source>Remove target fixture...</source>
        <translation>リマップ先の機器を削除</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="88"/>
        <source>Clone and auto-remap the selected source fixture</source>
        <translation>選択したリマップ元をリマップ先に複製</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="112"/>
        <source>Connect selections...</source>
        <translation>リマップ元(左)とリマップ先(右)をクリックしてから、このボタンをクリックして接続</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="129"/>
        <source>Disconnect selections...</source>
        <translation>リマップ解除</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="284"/>
        <source>Destination project name</source>
        <translation>リマップ後のプロジェクトファイル名</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="180"/>
        <source>Remapped Fixtures</source>
        <translation>リマップ先の機器</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="185"/>
        <location filename="fixtureremap.ui" line="217"/>
        <source>Address</source>
        <translation>DMXアドレス(リマップ後)</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="212"/>
        <source>Source Fixtures</source>
        <translation>リマップ元の機器</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="272"/>
        <source>Remap fixture names</source>
        <translation>機器の名前をリマップ元に合わせる</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="131"/>
        <location filename="fixtureremap.cpp" line="133"/>
        <source> (remapped)</source>
        <translation> (remapped)</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="226"/>
        <source>Generic Dimmer</source>
        <translation>一般ディマー</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="297"/>
        <source>Delete Fixtures</source>
        <translation>機器の削除</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="298"/>
        <source>Do you want to delete the selected items?</source>
        <translation>選択したアイテムを削除しますか？</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="346"/>
        <source>Invalid operation</source>
        <translation>無効な操作</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="347"/>
        <source>You are trying to clone a fixture on an address already in use. Please fix the target list first.</source>
        <translation>そのアドレスはリマップ先で既に使われています。</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="409"/>
        <location filename="fixtureremap.cpp" line="425"/>
        <location filename="fixtureremap.cpp" line="448"/>
        <location filename="fixtureremap.cpp" line="538"/>
        <source>Invalid selection</source>
        <translation>無効な選択</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="410"/>
        <location filename="fixtureremap.cpp" line="426"/>
        <location filename="fixtureremap.cpp" line="539"/>
        <source>Please select a source and a target fixture or channel to perform this operation.</source>
        <translation>リマップ元とリマップ先の機器またはチャンネルをそれぞれ選択してください。</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="449"/>
        <source>To perform a fixture remap, please select fixtures on both lists.</source>
        <translation>機器のリマップを実行するには、左右のリストからそれぞれ機器を選択してください。</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="666"/>
        <source>This might take a while...</source>
        <translation>しばらくお待ちください</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="666"/>
        <source>Cancel</source>
        <translation>キャンセル</translation>
    </message>
</context>
<context>
    <name>FixtureSelection</name>
    <message>
        <location filename="fixtureselection.ui" line="33"/>
        <source>Select fixture</source>
        <translation>機器の選択</translation>
    </message>
    <message>
        <location filename="fixtureselection.cpp" line="74"/>
        <source>No fixtures available</source>
        <translation>機器がありません</translation>
    </message>
    <message>
        <location filename="fixtureselection.cpp" line="76"/>
        <source>Go to the Fixture Manager and add some fixtures first.</source>
        <translation>まずは「機器」タブをクリックし、機器を追加してください。</translation>
    </message>
</context>
<context>
    <name>FixtureTreeWidget</name>
    <message>
        <location filename="fixturetreewidget.cpp" line="66"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="71"/>
        <source>Universe</source>
        <translation>Universe</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="76"/>
        <source>Address</source>
        <translation>スタートアドレス</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="81"/>
        <source>Type</source>
        <translation>種類</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="86"/>
        <source>Heads</source>
        <translation>Heads</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="91"/>
        <source>Manufacturer</source>
        <translation>メーカー名</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="96"/>
        <source>Model</source>
        <translation>機種名</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="214"/>
        <location filename="fixturetreewidget.cpp" line="222"/>
        <source>Generic</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="234"/>
        <source>Head</source>
        <translation>Head</translation>
    </message>
</context>
<context>
    <name>FunctionLiveEditDialog</name>
    <message>
        <location filename="functionliveeditdialog.cpp" line="46"/>
        <source>Function Live Edit</source>
        <translation>本番モード時にファンクションを編集</translation>
    </message>
</context>
<context>
    <name>FunctionManager</name>
    <message>
        <location filename="functionmanager.cpp" line="206"/>
        <source>New &amp;scene</source>
        <translation>新しいシーン</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="212"/>
        <source>New c&amp;haser</source>
        <translation>新しいチェイス</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="218"/>
        <source>New se&amp;quence</source>
        <translation>新しいシーケンス</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="230"/>
        <source>New c&amp;ollection</source>
        <translation>新しいコレクション</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="224"/>
        <source>New E&amp;FX</source>
        <translation>新しいE&amp;FX</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="236"/>
        <source>New &amp;RGB Matrix</source>
        <translation>新しいRGBマトリックス</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="242"/>
        <source>New scrip&amp;t</source>
        <translation>新しいスクリプト</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="333"/>
        <source>New Scene</source>
        <translation>新しいシーン</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="346"/>
        <source>New Chaser</source>
        <translation>新しいチェイス</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="369"/>
        <source>New Sequence</source>
        <translation>新しいシーケンス</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="279"/>
        <source>&amp;Clone</source>
        <translation>複製</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="248"/>
        <source>New au&amp;dio</source>
        <translation>新しいオーディオ</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="255"/>
        <source>New vid&amp;eo</source>
        <translation>新しいビデオ</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="261"/>
        <source>New fo&amp;lder</source>
        <translation>新しいフォルダ</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="267"/>
        <source>Select Startup Function</source>
        <translation>スタートアップファンクションの設定</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="272"/>
        <source>Function &amp;Wizard</source>
        <translation>ファンクションウィザード</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="285"/>
        <source>&amp;Delete</source>
        <translation>削除</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="291"/>
        <source>Select &amp;all</source>
        <translation>すべて選択</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="383"/>
        <source>New Collection</source>
        <translation>新しいコレクション</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="396"/>
        <source>New EFX</source>
        <translation>新しいEFX</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="409"/>
        <source>New RGB Matrix</source>
        <translation>新しいRGBマトリックス</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="422"/>
        <source>New Script</source>
        <translation>新しいスクリプト</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="432"/>
        <source>Open Audio File</source>
        <translation>オーディオファイルを開く</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="441"/>
        <source>Audio Files (%1)</source>
        <translation>オーディオファイル (%1)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="443"/>
        <location filename="functionmanager.cpp" line="497"/>
        <source>All Files (*.*)</source>
        <translation>すべてのファイル (*.*)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="445"/>
        <location filename="functionmanager.cpp" line="499"/>
        <source>All Files (*)</source>
        <translation>すべてのファイル (*)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="465"/>
        <source>Unsupported audio file</source>
        <translation>サポートしていないファイル</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="465"/>
        <source>This audio file cannot be played with QLC+. Sorry.</source>
        <translation>このオーディオファイルは QLC+ では再生できません。</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="486"/>
        <source>Open Video File</source>
        <translation>ビデオファイルを開く</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="495"/>
        <source>Video Files (%1)</source>
        <translation>ビデオファイル (%1)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="519"/>
        <source>Unsupported video file</source>
        <translation>サポートしていないファイル</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="519"/>
        <source>This video file cannot be played with QLC+. Sorry.</source>
        <translation>このビデオファイルは QLC+ では再生できません。</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="593"/>
        <source>Do you want to DELETE folder:</source>
        <oldsource>Do you want to DELETE foler:</oldsource>
        <translation>以下のフォルダを削除しますか？ :</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="595"/>
        <source>Do you want to DELETE functions:</source>
        <translation>以下のファンクションを削除しますか？ :</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="607"/>
        <source>(This will also DELETE: </source>
        <translation>(以下も同時に削除されます :</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="619"/>
        <source>Delete Functions</source>
        <translation>ファンクションの削除</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="725"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="862"/>
        <source> (Copy)</source>
        <translation> (コピー)</translation>
    </message>
</context>
<context>
    <name>FunctionSelection</name>
    <message>
        <location filename="functionselection.ui" line="33"/>
        <source>Select Function</source>
        <translation>ファンクションの選択</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="41"/>
        <source>All functions</source>
        <translation>すべてのファンクション</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="51"/>
        <source>Running functions</source>
        <translation>再生中のファンクション</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="93"/>
        <source>Filter</source>
        <translation>フィルタ</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="122"/>
        <source>EFX</source>
        <translation>EFX</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="129"/>
        <source>Display collections in the list</source>
        <translation>コレクションを表示</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="139"/>
        <source>Display scripts in the list</source>
        <translation>スクリプトを表示</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="142"/>
        <source>Scripts</source>
        <translation>スクリプト</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="180"/>
        <source>Sequences</source>
        <translation>シークエンス</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="99"/>
        <source>Display chasers in the list</source>
        <translation>チェイスを表示</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="170"/>
        <source>Display RGB Matrices in the list</source>
        <oldsource>Display RGB Matrixes in the list</oldsource>
        <translation>RGBマトリックスを表示</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="173"/>
        <source>RGB matrices</source>
        <oldsource>RGB matrixes</oldsource>
        <translation>RGBマトリックス</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="119"/>
        <source>Display EFX&apos;s in the list</source>
        <translation>EFXを表示</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="109"/>
        <source>Display scenes in the list</source>
        <translation>シーンを表示</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="112"/>
        <source>Scenes</source>
        <translation>シーン</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="163"/>
        <source>Shows</source>
        <translation>タイムライン</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="149"/>
        <source>Audio</source>
        <translation>オーディオ</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="156"/>
        <source>Video</source>
        <translation>ビデオ</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="102"/>
        <source>Chasers</source>
        <translation>チェイス</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="132"/>
        <source>Collections</source>
        <translation>コレクション</translation>
    </message>
    <message>
        <location filename="functionselection.cpp" line="78"/>
        <source>Functions</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="functionselection.cpp" line="315"/>
        <source>&lt;No function&gt;</source>
        <translation>&lt;ファンクション無し&gt;</translation>
    </message>
    <message>
        <location filename="functionselection.cpp" line="324"/>
        <source>&lt;Create a new track&gt;</source>
        <translation>新しいトラックを追加</translation>
    </message>
</context>
<context>
    <name>FunctionWizard</name>
    <message>
        <location filename="functionwizard.ui" line="33"/>
        <source>Function Wizard</source>
        <translation>ファンクションウィザード</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="57"/>
        <source>Next</source>
        <translation>次へ</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="71"/>
        <source>OK</source>
        <translation>OK</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="85"/>
        <source>Cancel</source>
        <translation>キャンセル</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="105"/>
        <source>Introduction</source>
        <translation>イントロダクション</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="114"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Welcome to the QLC+ wizard!&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;This is a guided procedure that will allow you to start using QLC+ in a few minutes.&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;It basically consists in three simple steps:&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;1- add fixtures&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;2- select capabilities to create functions&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;3- add widgets to the Virtual Console&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;To move from a step to another press the &amp;quot;Next&amp;quot; button&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;ファンクションウィザードへようこそ！&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;これはQLC+ 初心者のために、簡単な手順で機器に応じたファンクションを自動生成できるウィザードです。&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;3ステップで、ファンクションを自動生成します。&lt;br&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;1.「機器」を追加する。&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;2.機能を選んで、「ファンクション」を自動生成。&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;3.「バーチャルコンソール」に、ファンクションに対応するボタンやフェーダーを作る。&lt;br&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;次のステップに進むには、「次へ」ボタンをクリックしてください。&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="207"/>
        <source>Fixtures that will be included in automatic function creation</source>
        <translation>ファンクション自動生成に使う機器</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="254"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Add the fixtures for which you want to create functions and widgets&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt;ファンクション自動生成に使う機器を選んでください。&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="266"/>
        <source>Functions</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="273"/>
        <source>Results</source>
        <translation>生成されるファンクション</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="282"/>
        <source>Available</source>
        <translation>使用可能な機器</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="287"/>
        <source>Odd/Even</source>
        <translation>偶数/奇数</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="307"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Based on the fixtures you added, I can create the functions listed on the left. Just select what you need and see the results on the right!&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt;同じ機種が2台以上ある場合、「偶数/奇数」にチェックを入れると、偶数番目の機器のみ/奇数番目の機器のみ点灯するシーンも自動生成されます。&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="319"/>
        <source>Virtual Console</source>
        <translation>バーチャルコンソール</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="337"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Based on the functions you selected, I can create the following widgets on your Virtual Console. Just check which ones you need.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; 生成されたファンクションを再生できるように、バーチャルコンソール上にボタンやフェーダーを自動配置します。&lt;br&gt;本番モードにして、実際に操作してみてください。&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="349"/>
        <source>Widgets</source>
        <translation>部品</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="148"/>
        <location filename="functionwizard.ui" line="223"/>
        <source>Fixtures</source>
        <translation>機器</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="228"/>
        <source>Supported capabilities</source>
        <translation>使用可能な機能</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="187"/>
        <source>Select fixtures that will be included in the automatically created functions</source>
        <translation>ファンクション自動生成に使う機器の選択</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="190"/>
        <source>Add</source>
        <translation>追加</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="154"/>
        <source>Don&apos;t include selected fixtures in the created functions</source>
        <translation>選択した機器の除外</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="157"/>
        <source>Remove</source>
        <translation>削除</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="176"/>
        <source>%1 group</source>
        <translation>機器グループ「 %1 」</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="193"/>
        <source>Error</source>
        <translation>エラー</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="193"/>
        <source>%1 has no capability supported by this wizard.</source>
        <translation>%1 は、ファンクション自動生成には対応していない機器です。</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="467"/>
        <source>Presets solo frame</source>
        <translation>ソロフレームに配置</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="520"/>
        <source>Click &amp; Go RGB</source>
        <translation>Click &amp; Go を利用: RGB</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="524"/>
        <source>Click &amp; Go Macro</source>
        <translation>Click &amp; Go を利用: マクロ</translation>
    </message>
</context>
<context>
    <name>GrandMasterSlider</name>
    <message>
        <location filename="grandmasterslider.cpp" line="90"/>
        <source>GM</source>
        <translation>GM</translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="145"/>
        <source>Grand Master &lt;B&gt;limits&lt;/B&gt; the maximum value of</source>
        <translation>&lt;B&gt;limits&lt;/B&gt; / </translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="148"/>
        <source>Grand Master &lt;B&gt;reduces&lt;/B&gt; the current value of</source>
        <translation>&lt;B&gt;乗算&lt;/B&gt;</translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="157"/>
        <source>intensity channels</source>
        <translation>明るさチャンネルのみ</translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="160"/>
        <source>all channels</source>
        <translation>すべてのチャンネルに有効</translation>
    </message>
</context>
<context>
    <name>InputChannelEditor</name>
    <message>
        <location filename="inputchanneleditor.ui" line="33"/>
        <source>Input Channel Editor</source>
        <translation>外部入力の編集</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="39"/>
        <source>Input Channel</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="55"/>
        <source>Number</source>
        <translation>Number</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="88"/>
        <source>Midi</source>
        <translation>Midi</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="94"/>
        <source>Channel</source>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="111"/>
        <source>Message</source>
        <translation>Message</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="121"/>
        <source>Parameter</source>
        <translation>Parameter</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="129"/>
        <source>Control Change</source>
        <translation>Control Change</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="134"/>
        <source>Note On/Off</source>
        <translation>Note On/Off</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="139"/>
        <source>Note Aftertouch</source>
        <translation>Note Aftertouch</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="144"/>
        <source>Program Change</source>
        <translation>Program Change</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="149"/>
        <source>Channel Aftertouch</source>
        <translation>Channel Aftertouch</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="154"/>
        <source>Pitch Wheel</source>
        <translation>Pitch Wheel</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="159"/>
        <source>Beat Clock: Start/Stop/Continue</source>
        <translation>Beat Clock: スタート/ストップ/コンティニュー</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="164"/>
        <source>Beat Clock: Beat</source>
        <translation>Beat Clock: ビート</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="185"/>
        <source>Note</source>
        <translation>Note</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="48"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="75"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
</context>
<context>
    <name>InputOutputManager</name>
    <message>
        <location filename="inputoutputmanager.cpp" line="85"/>
        <source>Add U&amp;niverse</source>
        <translation>Universe の追加</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="91"/>
        <source>&amp;Delete Universe</source>
        <oldsource>Universe</oldsource>
        <translation>Universe の削除</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="110"/>
        <source>Universe name:</source>
        <translation>Universe名:</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="120"/>
        <source>Passthrough</source>
        <translation>パススルー</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="229"/>
        <location filename="inputoutputmanager.cpp" line="404"/>
        <source>Universe %1</source>
        <translation>Universe %1</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="362"/>
        <location filename="inputoutputmanager.cpp" line="381"/>
        <source>Delete Universe</source>
        <translation>Universe の削除</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="363"/>
        <source>The universe you are trying to delete is patched. Are you sure you want to delete it?</source>
        <translation>この Universe は接続されています。本当に削除しますか？</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="382"/>
        <source>There are some fixtures using the universe you are trying to delete. Are you sure you want to delete it?</source>
        <translation>この Universe には使用中の機器があります。本当に削除しますか？</translation>
    </message>
</context>
<context>
    <name>InputOutputPatchEditor</name>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="33"/>
        <source>Input/Output patch editor</source>
        <translation>入力/出力 接続設定</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="55"/>
        <source>Mapping</source>
        <translation>マッピング</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="80"/>
        <source>Plugin</source>
        <translation>プラグイン</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="85"/>
        <location filename="inputoutputpatcheditor.ui" line="281"/>
        <source>Device</source>
        <translation>デバイス</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="90"/>
        <location filename="inputoutputpatcheditor.ui" line="286"/>
        <source>Input</source>
        <translation>入力</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="98"/>
        <location filename="inputoutputpatcheditor.ui" line="291"/>
        <source>Output</source>
        <translation>出力</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="106"/>
        <source>Feedback</source>
        <translation>MIDIフィードバック</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="124"/>
        <source>Configure the selected plugin</source>
        <translation>入出力設定</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="144"/>
        <source>Enable/Disable USB hotplugging (requires restart)</source>
        <translation>USBホットプラグインを有効化/無効化(設定変更後は再起動してください)</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="168"/>
        <location filename="inputoutputpatcheditor.ui" line="257"/>
        <source>Profile</source>
        <translation>プロファイル</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="174"/>
        <source>Create a new input profile</source>
        <translation>新規プロファイルを作成</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="194"/>
        <source>Delete the selected input profile</source>
        <translation>選択したプロファイルの削除</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="214"/>
        <source>Edit the selected input profile</source>
        <translation>選択したプロファイルの編集</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="262"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="271"/>
        <source>Audio</source>
        <translation>オーディオ</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="299"/>
        <source>Configuration</source>
        <translation>設定</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="305"/>
        <source>Sample Rate (Hz)</source>
        <translation>サンプルレート (Hz)</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="312"/>
        <source>Channels</source>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="319"/>
        <source>Level Monitor</source>
        <translation>レベルモニター</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="435"/>
        <location filename="inputoutputpatcheditor.cpp" line="454"/>
        <location filename="inputoutputpatcheditor.cpp" line="560"/>
        <source>Error</source>
        <translation>エラー</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="436"/>
        <location filename="inputoutputpatcheditor.cpp" line="455"/>
        <source>Output line already assigned</source>
        <translation>出力先はすでに選択されています</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="561"/>
        <source>An error occurred while trying to open the selected device line.
This can be caused either by a wrong system configuration or an unsupported input/output mode.
Please refer to the plugins documentation to troubleshoot this.</source>
        <translation>デバイスを使用することができませんでした、お使いのPCシステムの設定を確認してください</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="702"/>
        <location filename="inputoutputpatcheditor.cpp" line="836"/>
        <source>Existing Input Profile</source>
        <translation>上書き確認</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="703"/>
        <location filename="inputoutputpatcheditor.cpp" line="837"/>
        <source>An input profile at %1 already exists. Do you wish to overwrite it?</source>
        <translation>プロファイル &apos;%1&apos; は既に存在します。上書き保存しますか？</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="712"/>
        <location filename="inputoutputpatcheditor.cpp" line="846"/>
        <source>Save Input Profile</source>
        <translation>プロファイルを保存</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="713"/>
        <location filename="inputoutputpatcheditor.cpp" line="847"/>
        <source>Input Profiles (*.qxi)</source>
        <translation>外部入力用プロファイル (*.qxi)</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="726"/>
        <location filename="inputoutputpatcheditor.cpp" line="863"/>
        <source>Saving failed</source>
        <translation>保存に失敗しました</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="727"/>
        <source>Unable to save the profile to %1</source>
        <translation>プロファイルを &apos;%1&apos;.に保存できませんでした。</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="764"/>
        <source>Delete profile</source>
        <translation>プロファイルの削除</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="765"/>
        <source>Do you wish to permanently delete profile &quot;%1&quot;?</source>
        <translation>本当にプロファイル &quot;%1&quot; を削除しますか？</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="793"/>
        <source>File deletion failed</source>
        <translation>ファイルの削除に失敗しました</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="794"/>
        <source>Unable to delete file %1</source>
        <translation>&apos;%1&apos; を削除できません。</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="864"/>
        <source>Unable to save %1 to %2</source>
        <translation>&apos;%1&apos; を &apos;%2&apos; に保存できません。</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="884"/>
        <source>Default device</source>
        <translation>既定のデバイス</translation>
    </message>
</context>
<context>
    <name>InputProfileEditor</name>
    <message>
        <location filename="inputprofileeditor.ui" line="33"/>
        <source>Input Profile Editor</source>
        <translation>入力機器プロファイル設定</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="47"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="53"/>
        <source>Manufacturer</source>
        <translation>メーカー名</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="77"/>
        <source>The name of the company that made the device</source>
        <translation>メーカー名</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="60"/>
        <source>Model</source>
        <translation>機種名</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="87"/>
        <source>The device&apos;s model name</source>
        <translation>機種名</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="124"/>
        <source>Channels</source>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="297"/>
        <source>Channel</source>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="302"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="347"/>
        <source>Custom feedback</source>
        <translation>カスタムフィードバック</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="359"/>
        <source>Upper value</source>
        <translation>上限値</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="379"/>
        <source>Lower value</source>
        <translation>下限値</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="67"/>
        <location filename="inputprofileeditor.ui" line="307"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="94"/>
        <source>MIDI Global Settings</source>
        <translation>MIDI全般の設定</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="100"/>
        <source>When MIDI notes are used, send a Note Off when value is 0</source>
        <translation>MIDIのnoteがoffの時にvalue=0を送信する</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="130"/>
        <location filename="inputprofileeditor.ui" line="312"/>
        <source>Behaviour</source>
        <translation>モード</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="327"/>
        <source>Add a new channel description</source>
        <translation>チャンネルの説明を追加</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="212"/>
        <source>Remove the selected channels</source>
        <translation>選択したチャンネルを削除</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="255"/>
        <source>Edit the selected channel</source>
        <translation>選択したチャンネルを編集</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="232"/>
        <source>Automatically add channels to the list when you wiggle the device&apos;s controls</source>
        <translation>自動割り当てのために、設定したいチャンネルを動かしてみてください</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="136"/>
        <source>Movement</source>
        <translation>動き</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="150"/>
        <source>Absolute</source>
        <translation>絶対値</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="155"/>
        <source>Relative</source>
        <translation>相対値</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="189"/>
        <source>Generate an extra Press/Release when toggled</source>
        <translation>押下・押上を切り替える</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="163"/>
        <source>Sensitivity</source>
        <translation>感度調整</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="107"/>
        <source>File not writable</source>
        <translation>ファイル書き込み不可</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="108"/>
        <source>You do not have permission to write to the file %1. You might not be able to save your modifications to the profile.</source>
        <translation>ファイル %1 に書き込む権限がありません。</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="261"/>
        <source>Missing information</source>
        <translation>不明な情報</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="262"/>
        <source>Manufacturer and/or model name is missing.</source>
        <translation>メーカー名か機種名が見つかりません。</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="311"/>
        <location filename="inputprofileeditor.cpp" line="410"/>
        <source>Channel already exists</source>
        <translation>チャンネルが既に存在しています</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="312"/>
        <location filename="inputprofileeditor.cpp" line="411"/>
        <source>Channel %1 already exists</source>
        <translation>チャンネル %1 は既に存在しています。</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="333"/>
        <source>Delete channels</source>
        <translation>チャンネル削除</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="334"/>
        <source>Delete all %1 selected channels?</source>
        <translation>%1 の選択したチャンネルをすべて削除しますか？</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="451"/>
        <source>Channel wizard activated</source>
        <translation>チャンネルウィーザード</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="452"/>
        <source>You have enabled the input channel wizard. After clicking OK, wiggle your mapped input profile&apos;s controls. They should appear into the list. Click the wizard button again to stop channel auto-detection.

Note that the wizard cannot tell the difference between a knob and a slider so you will have to do the change manually.</source>
        <translation>チャンネル自動設定ウィザードです。&quot;OK&quot;を押した後、登録したいコントローラのチャンネルを動かすと画面のリスト上に追加されます。もう一度チャンネル自動設定ウィザードアイコンをクリックすると自動検出を終わります。
        ＊このウィザードではスライダーとノブの違いを検出することができません、種類は手動で設定する必要があります。</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="600"/>
        <source>Button %1</source>
        <translation>ボタン %1</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="640"/>
        <source>Slider %1</source>
        <translation>フェーダー %1</translation>
    </message>
</context>
<context>
    <name>InputSelectionWidget</name>
    <message>
        <location filename="inputselectionwidget.ui" line="60"/>
        <source>Key combination</source>
        <translation>キーボードショートカット</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="78"/>
        <source>Reset the keyboard shortcut key</source>
        <translation>キーボードショートカットをリセット</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="98"/>
        <source>Set a key combination for this widget</source>
        <translation>キーボードショートカットの設定</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="118"/>
        <source>Keyboard combination that toggles this widget</source>
        <translation>この部品を作動させるためのキーボードショートカット</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="137"/>
        <source>External Input</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="263"/>
        <source>When toggled, you can click an external button to assign it to this widget.</source>
        <translation>自動判別をオンにして、外部入力機器(MIDIコントローラーなど)のフェーダーやボタンを操作してください。</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="266"/>
        <source>Auto Detect</source>
        <translation>自動判別</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="155"/>
        <source>Input Universe</source>
        <translation>Input Universe</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="231"/>
        <source>Input Channel</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="162"/>
        <source>The input universe that sends data to this widget</source>
        <translation>外部入力でウィジェットを操作</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="240"/>
        <source>Custom Feedback</source>
        <translation>カスタムフィードバック</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="221"/>
        <source>The particular input channel within the input universe that sends data to this widget</source>
        <translation>外部入力でウィジェットを操作</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="288"/>
        <source>Choose an external input universe &amp; channel that this widget should listen to.</source>
        <translation>入力にしようするUniverseとチャンネルを選択してください</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="291"/>
        <source>Choose...</source>
        <translation>選択...</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="172"/>
        <source>Custom feedback</source>
        <translation>カスタムフィードバック</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="184"/>
        <source>Lower value</source>
        <translation>下限値</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="211"/>
        <source>Upper value</source>
        <translation>上限値</translation>
    </message>
</context>
<context>
    <name>Monitor</name>
    <message>
        <location filename="monitor/monitor.cpp" line="336"/>
        <source>Fixture Monitor</source>
        <translation>モニタ</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="377"/>
        <source>2D View</source>
        <translation>2Dビュー</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="384"/>
        <source>Font</source>
        <translation>フォント</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="393"/>
        <source>DMX Channels</source>
        <translation>DMXチャンネル番号</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="394"/>
        <source>Show absolute DMX channel numbers</source>
        <translation>DMXチャンネル番号を表示</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="404"/>
        <source>Relative Channels</source>
        <translation>機器ごと</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="405"/>
        <source>Show channel numbers relative to fixture</source>
        <translation>機器ごとのチャンネル番号を表示</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="421"/>
        <source>DMX Values</source>
        <translation>DMX値</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="422"/>
        <source>Show DMX values 0-255</source>
        <translation>DMX値(0-255)で表示</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="433"/>
        <source>Percent Values</source>
        <translation>パーセント</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="434"/>
        <source>Show percentage values 0-100%</source>
        <translation>パーセント表示 (0-100%)</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="447"/>
        <source>Universe</source>
        <oldsource>Universe:</oldsource>
        <translation>Universe</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="452"/>
        <source>All universes</source>
        <translation>すべてのUniverse</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="468"/>
        <location filename="monitor/monitor.cpp" line="549"/>
        <source>Close</source>
        <translation>閉じる</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="469"/>
        <location filename="monitor/monitor.cpp" line="550"/>
        <source>Close this window</source>
        <translation>ウィンドウを閉じる</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="488"/>
        <source>DMX View</source>
        <translation>DMX View</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="494"/>
        <source>Size</source>
        <oldsource>Size:</oldsource>
        <translation>サイズ</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="518"/>
        <source>Meters</source>
        <translation>メートル</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="519"/>
        <source>Feet</source>
        <translation>フィート</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="528"/>
        <source>Add fixture</source>
        <translation>機器の追加</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="530"/>
        <source>Remove fixture</source>
        <translation>機器の削除</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="535"/>
        <source>Set a background picture</source>
        <translation>背景に画像を設定</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="538"/>
        <source>Show/hide labels</source>
        <translation>ラベルの表示/非表示</translation>
    </message>
</context>
<context>
    <name>MonitorBackgroundSelection</name>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="33"/>
        <source>Background Picture Selection</source>
        <translation>背景に設定する画像を選択</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="43"/>
        <source>No background</source>
        <translation>背景を設定しない</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="52"/>
        <source>Common background</source>
        <translation>一括で背景を設定</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="88"/>
        <source>Custom background list</source>
        <translation>個別に背景を設定</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="98"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="103"/>
        <source>Background</source>
        <translation>背景</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.cpp" line="160"/>
        <location filename="monitor/monitorbackgroundselection.cpp" line="182"/>
        <source>Select background image</source>
        <translation>背景画像を選択</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.cpp" line="162"/>
        <location filename="monitor/monitorbackgroundselection.cpp" line="184"/>
        <source>Images</source>
        <translation>画像</translation>
    </message>
</context>
<context>
    <name>MonitorFixturePropertiesEditor</name>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="33"/>
        <source>Monitor Fixture Properties Editor</source>
        <translation>モニターのフィクスチャーの設定</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="49"/>
        <source>Gel color</source>
        <oldsource>Gel color:</oldsource>
        <translation>カラーフィルター</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="56"/>
        <source>Position and rotation</source>
        <translation>配置と回転</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="72"/>
        <source>Vertical</source>
        <oldsource>Vertical:</oldsource>
        <translation>垂直</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="79"/>
        <source>Horizontal</source>
        <oldsource>Horizontal:</oldsource>
        <translation>水平</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="96"/>
        <source>Rotation</source>
        <oldsource>Rotation:</oldsource>
        <translation>回転</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="118"/>
        <source>Set the color of the gel installed on the fixture</source>
        <translation>フィクスチャーのカラーフィルターを設定できます</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="131"/>
        <source>Reset the current color</source>
        <translation>カラーフィルターの設定をリセット</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="171"/>
        <source>Fixture name:</source>
        <translation>名前</translation>
    </message>
</context>
<context>
    <name>MultiTrackView</name>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="319"/>
        <source>Do you want to DELETE item:</source>
        <oldsource>Do you want to DELETE sequence:</oldsource>
        <translation>以下のシーケンスを削除しますか？</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="322"/>
        <source>Delete Functions</source>
        <translation>ファンクションの削除</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="354"/>
        <source>Delete Track</source>
        <translation>トラックの削除</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="342"/>
        <source>Do you want to DELETE track:</source>
        <translation>トラックを削除します:</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="345"/>
        <source>This operation will also DELETE:</source>
        <translation>この操作をすると、以下を削除します:</translation>
    </message>
</context>
<context>
    <name>PaletteGenerator</name>
    <message>
        <location filename="palettegenerator.cpp" line="99"/>
        <source>Primary colours</source>
        <oldsource>Primary colors</oldsource>
        <translation>基本色</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="100"/>
        <source>16 Colours</source>
        <oldsource>16 Colors</oldsource>
        <translation>16色</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="101"/>
        <source>Shutter macros</source>
        <translation>シャッターマクロ</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="102"/>
        <source>Gobo macros</source>
        <translation>ゴボマクロ</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="103"/>
        <source>Colour macros</source>
        <oldsource>Color macros</oldsource>
        <translation>カラーマクロ</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="104"/>
        <source>Animations</source>
        <translation>アニメーション</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="107"/>
        <source>Unknown</source>
        <translation>不明</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="255"/>
        <source>%1 - %2 (Even)</source>
        <translation>%1 - %2 (偶数)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="256"/>
        <source>%1 - %2 (Odd)</source>
        <translation>%1 - %2 (奇数)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Black</source>
        <translation>Black</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Dark Blue</source>
        <translation>Dark Blue</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Blue</source>
        <translation>Blue</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Dark Green</source>
        <translation>Dark Green</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Dark Cyan</source>
        <translation>Dark Cyan</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Green</source>
        <translation>Green</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Cyan</source>
        <translation>Cyan</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Dark Red</source>
        <translation>Dark Red</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Dark Magenta</source>
        <translation>Dark Magenta</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Dark Yellow</source>
        <translation>Dark Yellow</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Dark Gray</source>
        <translation>Dark Gray</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Light Gray</source>
        <translation>Light Gray</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>Red</source>
        <translation>Red</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>Magenta</source>
        <translation>Magenta</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>Yellow</source>
        <translation>Yellow</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>White</source>
        <translation>White</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="360"/>
        <source>%1 %2 - %3</source>
        <translation>%1 %2 - %3</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="364"/>
        <source>%1 %2 - %3 (Even)</source>
        <translation>%1 %2 - %3 (偶数)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="365"/>
        <source>%1 %2 - %3 (Odd)</source>
        <translation>%1 %2 - %3 (奇数)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="427"/>
        <source> - Even</source>
        <translation> - 偶数</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="428"/>
        <source> - Odd</source>
        <translation> - 奇数</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="443"/>
        <source> - RGB Group</source>
        <translation> - RGB グループ</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="449"/>
        <source>Animation %1</source>
        <translation>アニメーション %1</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="468"/>
        <source>%1 chaser - %2</source>
        <translation>%1 チェイス - %2</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="540"/>
        <source>Red scene</source>
        <translation>Red scene</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="541"/>
        <source>Green scene</source>
        <translation>Green scene</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="542"/>
        <source>Blue scene</source>
        <translation>Blue scene</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="543"/>
        <source>Cyan scene</source>
        <translation>Cyan scene</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="544"/>
        <source>Magenta scene</source>
        <translation>Magenta scene</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="545"/>
        <source>Yellow scene</source>
        <translation>Yellow scene</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="546"/>
        <source>White scene</source>
        <translation>White scene</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="553"/>
        <location filename="palettegenerator.cpp" line="555"/>
        <source>Scene</source>
        <translation>シーン</translation>
    </message>
</context>
<context>
    <name>PlaybackSlider</name>
    <message>
        <location filename="playbackslider.cpp" line="52"/>
        <source>Select</source>
        <translation>選択</translation>
    </message>
    <message>
        <location filename="playbackslider.cpp" line="84"/>
        <source>Flash</source>
        <translation>フラッシュ</translation>
    </message>
</context>
<context>
    <name>PositionTool</name>
    <message>
        <location filename="positiontool.ui" line="33"/>
        <source>PositonTool</source>
        <translation>位置調整</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="app.cpp" line="85"/>
        <source>Operate</source>
        <translation>本番モード</translation>
    </message>
    <message>
        <location filename="app.cpp" line="86"/>
        <source>Design</source>
        <translation>仕込みモード</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixture.cpp" line="277"/>
        <location filename="virtualconsole/vcxypadfixture.cpp" line="352"/>
        <source>Reversed</source>
        <translation>戻る</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframepageshortcut.cpp" line="49"/>
        <source>Page: %1</source>
        <translation>Page : %1</translation>
    </message>
</context>
<context>
    <name>RGBMatrixEditor</name>
    <message>
        <location filename="rgbmatrixeditor.ui" line="33"/>
        <source>RGB Matrix Editor</source>
        <translation>RGBマトリックスエディタ</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="508"/>
        <source>RGB matrix name</source>
        <translation>マトリックス名</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="521"/>
        <source>The name of this RGB matrix function</source>
        <translation>このRGBマトリックスの名前</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="552"/>
        <source>Save this matrix to a sequence</source>
        <translation>シーケンスに保存</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="572"/>
        <source>Toggle between circle and square preview</source>
        <translation>円形、四角を切り替え</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="618"/>
        <source>Fixture group</source>
        <translation>機器グループ</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="631"/>
        <source>The fixture group to use as the pixel matrix</source>
        <translation>ドットとして使用する機器グループ</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="56"/>
        <source>Pattern</source>
        <translation>パターン</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="98"/>
        <source>The RGB matrix pattern</source>
        <translation>RGBマトリックスパターン</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="173"/>
        <source>Animated Text</source>
        <translation>テキスト</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="194"/>
        <source>Text to display</source>
        <translation>表示するテキスト</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="201"/>
        <source>Choose the font</source>
        <translation>フォントを選択</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="152"/>
        <source>Properties</source>
        <translation>プロパティー</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="221"/>
        <source>Animation style</source>
        <translation>アニメーションスタイル</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="231"/>
        <source>Image</source>
        <translation>画像</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="279"/>
        <source>Offset</source>
        <translation>オフセット</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="291"/>
        <source>X</source>
        <translation>X</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="298"/>
        <source>Shift the pattern X pixels horizontally</source>
        <translation>X軸方向にずらす</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="311"/>
        <source>Y</source>
        <translation>Y</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="318"/>
        <source>Shift the pattern Y pixels vertically</source>
        <translation>Y軸方向にずらす</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="376"/>
        <source>Dimmer control</source>
        <translation>光量の自動化設定</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="373"/>
        <source>Set the dimmer channel of fixtures to 100%</source>
        <translation>チェックを入れるとファンクション再生時に自動的光量が100%になります。</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="71"/>
        <source>Matrix end color</source>
        <translation>第2色設定</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="87"/>
        <source>Reset the end color</source>
        <translation>リセット</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="105"/>
        <source>Matrix start color</source>
        <translation>第一色</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="118"/>
        <source>Blend mode</source>
        <translation>ブレンドモード</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="126"/>
        <source>Default (HTP)</source>
        <translation>デフォルト (HTP)</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="131"/>
        <source>Mask</source>
        <translation>マスク</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="136"/>
        <source>Additive</source>
        <translation>加算</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="141"/>
        <source>Subtractive</source>
        <translation>減算</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="361"/>
        <source>Other Controls</source>
        <translation>その他</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="452"/>
        <source>Run Order</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="464"/>
        <source>Run through over and over again</source>
        <translation>繰り返し</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="467"/>
        <source>Loop</source>
        <translation>繰り返し</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="477"/>
        <source>Run through once and stop</source>
        <translation>一方通行</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="480"/>
        <source>Single Shot</source>
        <translation>一方通行</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="487"/>
        <source>First run forwards, then backwards, again forwards, etc.</source>
        <translation>往復</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="490"/>
        <source>Ping Pong</source>
        <translation>往復</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="392"/>
        <source>Direction</source>
        <translation>順/逆</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="404"/>
        <source>Start from the first step</source>
        <translation>最初のステップから再生</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="407"/>
        <source>Forward</source>
        <translation>順再生</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="417"/>
        <source>Start from the last step</source>
        <translation>最後のステップから再生</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="420"/>
        <source>Backward</source>
        <translation>逆再生</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="532"/>
        <source>Show/Hide speed dial window</source>
        <translation>スピードダイヤルの表示/非表示</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="592"/>
        <source>See what the RGB Matrix does when it is run</source>
        <translation>プレビュー再生</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="273"/>
        <source>None</source>
        <translation>無し</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="489"/>
        <source>No fixture group to control</source>
        <translation>操作する機器グループがありません</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="766"/>
        <source>Select image</source>
        <translation>画像を選択</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="768"/>
        <source>Images</source>
        <translation>画像</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="1015"/>
        <source>Sequence</source>
        <translation>シーケンス</translation>
    </message>
</context>
<context>
    <name>SceneEditor</name>
    <message>
        <location filename="sceneeditor.ui" line="39"/>
        <source>Scene editor</source>
        <translation>シーンエディタ</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="64"/>
        <location filename="sceneeditor.ui" line="67"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="120"/>
        <source>Enable all channel groups</source>
        <translation>すべてのチャンネルグループを有効</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="86"/>
        <source>Disable all channel groups</source>
        <translation>全てのチャンネルグループを無効</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="162"/>
        <source>Fixtures used in this scene</source>
        <oldsource>Name of this scene</oldsource>
        <translation>このシーンで使う機器</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="209"/>
        <source>Channel groups used in this scene</source>
        <translation>このシーンで使うチャンネルグループ</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="103"/>
        <source>Add a new fixture to this scene</source>
        <translation>シーンで使う機器を追加</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="170"/>
        <source>Remove the selected fixture(s) from this scene</source>
        <translation>選択した機器を削除</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="241"/>
        <source>Enable all fixtures&apos; channels</source>
        <translation>すべての機器とチャンネルを有効</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="224"/>
        <source>Disable all fixtures&apos; channels</source>
        <translation>すべての機器とチャンネルを無効</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="189"/>
        <source>Enable all channels in current fixture</source>
        <translation>この機器のすべてのチャンネルを有効</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="191"/>
        <source>Disable all channels in current fixture</source>
        <translation>この機器のすべてのチャンネルを無効</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="193"/>
        <source>Copy current values to clipboard</source>
        <translation>現在のシーンをコピー</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="195"/>
        <source>Paste clipboard values to current fixture</source>
        <translation>クリップボードから貼り付け</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="197"/>
        <source>Copy current values to all fixtures</source>
        <translation>すべての機器に現在の値を適用</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="199"/>
        <source>Color tool for CMY/RGB-capable fixtures</source>
        <translation>色設定ツール(CMY/RGBミックスが可能な機器用)</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="201"/>
        <source>Position tool for moving heads/scanners</source>
        <translation>ムービングの位置調整</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="203"/>
        <source>Switch between tab view and all channels view</source>
        <translation>フィクスチャーごとにタブ表示/まとめて表示</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="205"/>
        <source>Toggle blind mode</source>
        <translation>ブラインドモード</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="207"/>
        <source>Show/Hide speed dial window</source>
        <translation>スピード設定</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="209"/>
        <source>Clone this scene and append as a new step to the selected chaser</source>
        <translation>このシーンを複製し、選択したチェイスにステップとして登録</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="211"/>
        <source>Go to next fixture tab</source>
        <translation>次の機器へ</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="215"/>
        <source>Go to previous fixture tab</source>
        <translation>前の機器へ</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="238"/>
        <source>None</source>
        <translation>無し</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="250"/>
        <source>Scene name:</source>
        <translation>シーン名:</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="925"/>
        <location filename="sceneeditor.cpp" line="926"/>
        <source>All fixtures</source>
        <translation>すべての機器</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1458"/>
        <location filename="sceneeditor.cpp" line="1459"/>
        <source>Channels Groups</source>
        <translation>チャンネルグループ</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1235"/>
        <location filename="sceneeditor.cpp" line="1236"/>
        <source>Generic</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1303"/>
        <source>Remove fixtures</source>
        <translation>機器を削除</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1304"/>
        <source>Do you want to remove the selected fixture(s)?</source>
        <translation>選択した機器を削除しますか？</translation>
    </message>
</context>
<context>
    <name>ScriptEditor</name>
    <message>
        <location filename="scripteditor.ui" line="33"/>
        <source>Script editor</source>
        <translation>スクリプトエディタ</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="172"/>
        <source>Test the execution of this script</source>
        <translation>スクリプトを実行してみる</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="192"/>
        <source>Script name</source>
        <translation>スクリプト名</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="52"/>
        <source>Add new command to cursor position</source>
        <translation>カーソルの位置に新しいコマンドを追加</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="82"/>
        <source>Cut selected text to clipboard</source>
        <translation>選択したテキストを切り取り</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="99"/>
        <source>Copy selected text to clipboard</source>
        <translation>選択したテキストをコピー</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="116"/>
        <source>Paste text from clipboard at cursor</source>
        <translation>選択したテキストを貼り付け</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="140"/>
        <source>Undo</source>
        <translation>戻す</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="211"/>
        <source>Check the syntax of this script</source>
        <translation>スクリプトの文法をチェック</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="92"/>
        <source>Start Function</source>
        <translation>ファンクション開始</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="96"/>
        <source>Stop Function</source>
        <translation>ファンクション停止</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="100"/>
        <source>Blackout</source>
        <translation>暗転</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="104"/>
        <source>Wait</source>
        <translation>ウェイト</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="108"/>
        <source>Wait Key</source>
        <translation>キー入力待ち</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="112"/>
        <source>Set HTP</source>
        <translation>Set HTP</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="116"/>
        <source>Set LTP</source>
        <translation>Set LTP</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="120"/>
        <source>Set Fixture</source>
        <translation>機器をセット</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="124"/>
        <source>System Command</source>
        <translation>システムコマンド</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="128"/>
        <source>Comment</source>
        <translation>コメント</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="132"/>
        <source>Random Number</source>
        <translation>乱数生成</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="136"/>
        <source>File Path</source>
        <translation>ファイルパス</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="163"/>
        <source>Open Executable File</source>
        <translation>外部ファイルを開く</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="168"/>
        <source>All Files (*.*)</source>
        <translation>すべてのファイル (*.*)</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="170"/>
        <source>All Files (*)</source>
        <translation>すべてのファイル (*)</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="273"/>
        <source>Blackout state</source>
        <translation>暗転の状態</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="300"/>
        <source>Enter the desired time</source>
        <translation>任意の数値を指定してください</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="384"/>
        <source>Invalid executable</source>
        <translation>実行</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="384"/>
        <source>Please select an executable file!</source>
        <translation>実行できるファイルを指定してください</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="390"/>
        <source>Enter the program arguments (leave empty if not required)</source>
        <translation>プログラムに引数を入力できます。(空白にすることもできます)</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="426"/>
        <source>Enter the range for the randomization</source>
        <translation>乱数生成の範囲を指定できます</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="433"/>
        <source>Minimum value</source>
        <translation>最大値</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="434"/>
        <source>Maximum value</source>
        <translation>最小値</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="474"/>
        <source>No syntax errors found in the script</source>
        <translation>文法エラーはありませんでした</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="481"/>
        <source>Syntax error at line %1:
%2

</source>
        <translation>文法エラー%1:
%2</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="484"/>
        <source>Script check results</source>
        <translation>文法チェックの結果</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="410"/>
        <source>Add Comment</source>
        <translation>コメントの追加</translation>
    </message>
</context>
<context>
    <name>SelectInputChannel</name>
    <message>
        <location filename="selectinputchannel.ui" line="33"/>
        <source>Select input channel</source>
        <translation>入力を選択してください</translation>
    </message>
    <message>
        <location filename="selectinputchannel.ui" line="43"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="selectinputchannel.ui" line="51"/>
        <source>Allow unpatched universes</source>
        <translation>接続されていないUniversを設定する</translation>
    </message>
    <message>
        <location filename="selectinputchannel.cpp" line="210"/>
        <source>&lt;Double click here to enter channel number manually&gt;</source>
        <translation>チャンネルを手動で入力してください</translation>
    </message>
</context>
<context>
    <name>ShowEditor</name>
    <message>
        <location filename="showmanager/showeditor.ui" line="33"/>
        <source>Show editor</source>
        <translation>タイムラインエディタ</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="45"/>
        <source>Show name</source>
        <translation>タイムライン名</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="52"/>
        <source>Name of the function being edited</source>
        <translation>タイムラインの名前</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="72"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="77"/>
        <source>Steps</source>
        <translation>ステップ</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="85"/>
        <source>Start Time</source>
        <translation>開始時間</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="93"/>
        <source>Duration</source>
        <translation>継続時間</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="104"/>
        <source>Add function(s) to the collection</source>
        <translation>コレクションに追加</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="124"/>
        <source>Remove the selected function</source>
        <translation>選択したファンクションの削除</translation>
    </message>
</context>
<context>
    <name>ShowItem</name>
    <message>
        <location filename="showmanager/showitem.cpp" line="53"/>
        <source>Align to cursor</source>
        <translation>割り当てる</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="56"/>
        <location filename="showmanager/showitem.cpp" line="85"/>
        <location filename="showmanager/showitem.cpp" line="261"/>
        <source>Lock item</source>
        <translation>ロックする</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="66"/>
        <source>Name: %1
Start time: %2
Duration: %3
%4</source>
        <translation>名前 : %1
開始時間 : %2
継続時間 : %3
%4</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="70"/>
        <source>Click to move this item along the timeline</source>
        <oldsource>Click to move this item across the timeline</oldsource>
        <translation type="unfinished">ドラッグでタイムライン上のウィジェットを移動できます</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="80"/>
        <location filename="showmanager/showitem.cpp" line="256"/>
        <source>Unlock item</source>
        <translation>ロックを解除</translation>
    </message>
</context>
<context>
    <name>ShowManager</name>
    <message>
        <location filename="showmanager/showmanager.cpp" line="200"/>
        <source>New s&amp;how</source>
        <translation>新しいタイムライン</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="212"/>
        <source>New s&amp;equence</source>
        <translation>新しいシーケンス</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="218"/>
        <source>New &amp;audio</source>
        <translation>新しいオーディオ</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="225"/>
        <source>New vi&amp;deo</source>
        <translation>新しいビデオ</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="232"/>
        <source>&amp;Copy</source>
        <translation>コピー</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="239"/>
        <source>&amp;Paste</source>
        <translation>ペースト</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="246"/>
        <source>&amp;Delete</source>
        <translation>削除</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="253"/>
        <source>Change Co&amp;lor</source>
        <translation>色の変更</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="274"/>
        <source>Snap to &amp;Grid</source>
        <translation>グリッドの表示</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="281"/>
        <source>St&amp;op</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="287"/>
        <source>&amp;Play</source>
        <translation>再生</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="348"/>
        <source>Time division:</source>
        <translation>横軸:</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="353"/>
        <source>Time</source>
        <translation>時間</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="569"/>
        <source>New Show</source>
        <translation>新しいタイムライン</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="570"/>
        <source>Show name setup</source>
        <translation>タイムライン名</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="571"/>
        <source>Show name:</source>
        <translation>タイムライン名 :</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="206"/>
        <source>Add a &amp;track or an existing function</source>
        <translation>ファンクションを新しいトラックに追加</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="260"/>
        <source>Lock item</source>
        <translation>ロック</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="267"/>
        <source>Item start time and duration</source>
        <translation>再生開始時間と再生継続時間</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="695"/>
        <location filename="showmanager/showmanager.cpp" line="813"/>
        <source> (Copy)</source>
        <translation> (コピー)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="766"/>
        <source>Track %1</source>
        <translation>トラック %1</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="795"/>
        <location filename="showmanager/showmanager.cpp" line="885"/>
        <source>New Sequence</source>
        <translation>新しいシーケンス</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="863"/>
        <location filename="showmanager/showmanager.cpp" line="940"/>
        <location filename="showmanager/showmanager.cpp" line="999"/>
        <source>Overlapping error</source>
        <translation>オーバーラップはできません</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="863"/>
        <location filename="showmanager/showmanager.cpp" line="940"/>
        <location filename="showmanager/showmanager.cpp" line="999"/>
        <source>Overlapping not allowed. Operation canceled.</source>
        <translation>オーバーラップはできません。新しいトラックを追加してください。</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="873"/>
        <source>Scene for %1 - Track %2</source>
        <translation>シーン: %1 - トラック: %2</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="898"/>
        <source>Open Audio File</source>
        <translation>オーディオファイルを開く</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="907"/>
        <source>Audio Files (%1)</source>
        <translation>オーディオファイル (%1)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="909"/>
        <location filename="showmanager/showmanager.cpp" line="968"/>
        <source>All Files (*.*)</source>
        <translation>すべてのファイル (*.*)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="911"/>
        <location filename="showmanager/showmanager.cpp" line="970"/>
        <source>All Files (*)</source>
        <translation>すべてのファイル (*)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="933"/>
        <source>Unsupported audio file</source>
        <translation>サポートしていないファイル</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="933"/>
        <source>This audio file cannot be played with QLC+. Sorry.</source>
        <translation>そのオーディオファイルはQLC+では再生できません。</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="957"/>
        <source>Open Video File</source>
        <translation>ビデオファイルを開く</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="966"/>
        <source>Video Files (%1)</source>
        <translation>ビデオファイル (%1)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="992"/>
        <source>Unsupported video file</source>
        <translation>サポートしていないファイル</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="992"/>
        <source>This video file cannot be played with QLC+. Sorry.</source>
        <translation>そのビデオファイルはQLC+では再生できません。</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1035"/>
        <location filename="showmanager/showmanager.cpp" line="1089"/>
        <source>Paste error</source>
        <translation>貼り付けエラー</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1035"/>
        <source>Overlapping paste not allowed. Operation canceled.</source>
        <translation>オーバーラップする貼り付けはできません。</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1089"/>
        <source>Trying to paste on an incompatible Scene. Operation canceled.</source>
        <translation>そのシーンには貼り付けできません。</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1414"/>
        <source>Track name setup</source>
        <translation>トラック名の編集</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1415"/>
        <source>Track name:</source>
        <translation>トラック名</translation>
    </message>
</context>
<context>
    <name>SimpleDesk</name>
    <message>
        <location filename="simpledesk.cpp" line="247"/>
        <source>Universe</source>
        <translation>Universe</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="234"/>
        <source>Next page</source>
        <translation>次のページ</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="226"/>
        <source>Current page</source>
        <translation>現在のページ</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="218"/>
        <source>Previous page</source>
        <translation>前のページ</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="209"/>
        <source>View mode</source>
        <translation>ビューモード</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="242"/>
        <source>Reset universe</source>
        <translation>Universe のリセット</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="287"/>
        <source>Playback</source>
        <translation>プレイバック</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="284"/>
        <location filename="simpledesk.cpp" line="294"/>
        <source>Cue Stack</source>
        <translation>キュースタック</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="304"/>
        <source>Previous cue</source>
        <translation>前のキュー</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="310"/>
        <source>Stop cue stack</source>
        <translation>キュースタックの停止</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="316"/>
        <source>Next cue</source>
        <translation>次のキュー</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="324"/>
        <source>Clone cue stack</source>
        <translation>キュースタックの複製</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="330"/>
        <source>Edit cue stack</source>
        <translation>キュースタックの編集</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="337"/>
        <source>Record cue</source>
        <translation>キューの記録</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="558"/>
        <source>Channel groups</source>
        <translation>チャンネルグループ</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1049"/>
        <source>Cue Stack - Playback %1</source>
        <translation>キュースタック - フェーダー %1</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1237"/>
        <source>No selection</source>
        <translation>選択無し</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1260"/>
        <source>Cue name</source>
        <translation>キュー名</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1267"/>
        <source>Multiple Cues</source>
        <translation>複数キュー</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1364"/>
        <source>Delete cue</source>
        <translation>キューの削除</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1418"/>
        <source>Clone Cue Stack</source>
        <translation>キュースタックの複製</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1418"/>
        <source>Clone To Playback#</source>
        <translation>複製先フェーダー:</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1484"/>
        <source>Cue %1</source>
        <translation>キュー %1</translation>
    </message>
</context>
<context>
    <name>SpeedDial</name>
    <message>
        <location filename="speeddial.cpp" line="144"/>
        <source>Hours</source>
        <translation>時</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="153"/>
        <source>Minutes</source>
        <translation>分</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="162"/>
        <source>Seconds</source>
        <translation>秒</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="171"/>
        <source>Milliseconds</source>
        <translation>ミリ秒</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="177"/>
        <source>Infinite</source>
        <translation>∞</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="129"/>
        <source>Tap</source>
        <translation>Tap</translation>
    </message>
</context>
<context>
    <name>SpeedDialWidget</name>
    <message>
        <location filename="speeddialwidget.cpp" line="61"/>
        <source>Fade In</source>
        <translation>フェードイン</translation>
    </message>
    <message>
        <location filename="speeddialwidget.cpp" line="67"/>
        <source>Fade Out</source>
        <translation>フェードアウト</translation>
    </message>
    <message>
        <location filename="speeddialwidget.cpp" line="73"/>
        <source>Hold</source>
        <translation>ホールド</translation>
    </message>
</context>
<context>
    <name>TimingsTool</name>
    <message>
        <location filename="showmanager/timingstool.cpp" line="50"/>
        <source>Start Time</source>
        <translation>開始時間</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="61"/>
        <source>Duration</source>
        <translation>再生継続時間</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="68"/>
        <source>Duration options</source>
        <translation>再生オプション</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="70"/>
        <source>Stretch the original function duration</source>
        <translation>再生時間を変更</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="71"/>
        <source>Loop function until duration is reached</source>
        <translation>再生時間内はループ再生</translation>
    </message>
</context>
<context>
    <name>TrackItem</name>
    <message>
        <location filename="showmanager/trackitem.cpp" line="55"/>
        <source>Move up</source>
        <translation>上へ移動</translation>
    </message>
    <message>
        <location filename="showmanager/trackitem.cpp" line="58"/>
        <source>Move down</source>
        <translation>下へ移動</translation>
    </message>
    <message>
        <location filename="showmanager/trackitem.cpp" line="62"/>
        <source>Change name</source>
        <translation>名前変更</translation>
    </message>
    <message>
        <location filename="showmanager/trackitem.cpp" line="66"/>
        <source>Delete</source>
        <translation>削除</translation>
    </message>
</context>
<context>
    <name>UniverseItemWidget</name>
    <message>
        <location filename="universeitemwidget.cpp" line="86"/>
        <source>Input:</source>
        <translation>外部入力 :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="87"/>
        <source>Profile:</source>
        <translation>プロファイル :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="88"/>
        <source>Output:</source>
        <translation>出力 :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="89"/>
        <source>Feedback:</source>
        <translation>MIDIフィードバック :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="112"/>
        <location filename="universeitemwidget.cpp" line="114"/>
        <location filename="universeitemwidget.cpp" line="116"/>
        <location filename="universeitemwidget.cpp" line="118"/>
        <source>None</source>
        <translation>無し</translation>
    </message>
</context>
<context>
    <name>VCButton</name>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="93"/>
        <source>Choose...</source>
        <translation>選択...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="97"/>
        <source>None</source>
        <translation>無し</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="135"/>
        <source>Button %1</source>
        <translation>ボタン %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="313"/>
        <source>Select button icon</source>
        <translation>ボタンアイコンの選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="314"/>
        <source>Images (%1)</source>
        <translation>画像 (%1)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="579"/>
        <source>Toggle Blackout</source>
        <translation>暗転</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="581"/>
        <source>Stop ALL functions!</source>
        <translation>全ファンクション停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="820"/>
        <source>Icon</source>
        <translation>アイコン</translation>
    </message>
</context>
<context>
    <name>VCButtonProperties</name>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="33"/>
        <source>Button properties</source>
        <translation>ボタンの詳細</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="219"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="225"/>
        <source>Button label</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="232"/>
        <source>Text to display on the button</source>
        <translation>ボタン上に表示するテキスト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="239"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="246"/>
        <source>The function that this button controls</source>
        <translation>このボタンで操作するファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="256"/>
        <source>Attach a function to this button</source>
        <translation>このボタンで操作するファンクションを設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="276"/>
        <source>Detach the button&apos;s function attachment</source>
        <translation>ファンクションを除外</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="68"/>
        <source>Toggle Blackout</source>
        <translation>暗転</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="83"/>
        <source>Stop All Functions</source>
        <translation>すべてのファンクション停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="90"/>
        <source>Fade time:</source>
        <translation>フェードタイム: </translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="145"/>
        <source>Adjust function intensity when it is running</source>
        <translation>ファンクション再生時の明るさを設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="148"/>
        <source>Adjust Function Intensity</source>
        <translation>ファンクション再生時の明るさ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="163"/>
        <source>Function&apos;s adjusted intensity percentage when run</source>
        <translation>ファンクション再生時の明るさ(パーセント)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="39"/>
        <source>On button press...</source>
        <translation>押された時の動作</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="45"/>
        <source>Toggle the assigned function on/off with this button</source>
        <translation>ボタンを1度押すとON、もう1度押すとOFF</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="48"/>
        <source>Toggle function on/off</source>
        <translation>ON/OFF切り替え</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="58"/>
        <source>Flash the assigned function with this button</source>
        <translation>ボタンを押している間だけ再生</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="61"/>
        <source>Flash function (only for scenes)</source>
        <translation>フラッシュ(シーンのみ対応)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.cpp" line="133"/>
        <source>No function</source>
        <translation>無し</translation>
    </message>
</context>
<context>
    <name>VCClockProperties</name>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="33"/>
        <source>Clock properties</source>
        <translation>時計の詳細</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="47"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="53"/>
        <source>Clock type</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="59"/>
        <source>Stopwatch</source>
        <translation>ストップウォッチ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="66"/>
        <source>h</source>
        <translation> h</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="76"/>
        <source>Countdown</source>
        <translation>カウントダウン</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="83"/>
        <source>m</source>
        <translation> min</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="93"/>
        <source>s</source>
        <translation> s</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="109"/>
        <source>Clock</source>
        <translation>現在時刻</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="122"/>
        <source>Schedule</source>
        <translation>スケジュール</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="188"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="193"/>
        <source>Time</source>
        <translation>時間</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="205"/>
        <source>Input</source>
        <translation>入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="214"/>
        <source>No input control available for Clock type</source>
        <translation>入力を確認してください</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.cpp" line="39"/>
        <source>Play/Pause control</source>
        <translation>再生/停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.cpp" line="48"/>
        <source>Reset control</source>
        <translation>リセット</translation>
    </message>
</context>
<context>
    <name>VCCueList</name>
    <message>
        <source>Blend</source>
        <translation type="vanished">ブレンド</translation>
    </message>
    <message>
        <source>Link</source>
        <translation type="vanished">連動</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="210"/>
        <source>Show/Hide crossfade sliders</source>
        <translation>クロスフェーダー表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="221"/>
        <location filename="virtualconsole/vccuelist.cpp" line="1036"/>
        <source>Play/Pause Cue list</source>
        <translation>キューリストの再生/一時停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="230"/>
        <location filename="virtualconsole/vccuelist.cpp" line="1037"/>
        <source>Stop Cue list</source>
        <translation>キューリストの停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="239"/>
        <source>Go to previous step in the list</source>
        <translation>前のステップへ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="248"/>
        <source>Go to next step in the list</source>
        <translation>次のステップへ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="257"/>
        <source>Cue list</source>
        <translation>キューリスト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1030"/>
        <source>Play/Stop Cue list</source>
        <translation>キューリストの再生/一時停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1031"/>
        <source>Pause Cue list</source>
        <translation>キューリストの停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Fade In</source>
        <translation>フェードイン</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Fade Out</source>
        <translation>フェードアウト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Duration</source>
        <translation>再生継続時間</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Notes</source>
        <translation>メモ</translation>
    </message>
</context>
<context>
    <name>VCCueListProperties</name>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="33"/>
        <source>Cue list properties</source>
        <translation>キューリストの詳細</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="39"/>
        <source>Cue list name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="56"/>
        <source>The name of the cue list widget</source>
        <translation>キューリストの名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="70"/>
        <source>Cue List</source>
        <oldsource>Cues</oldsource>
        <translation>キューリスト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="120"/>
        <source>Chaser</source>
        <translation>チェイス</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="76"/>
        <source>The chaser function to use as cue list</source>
        <translation>キューリストには、チェイスを使用します</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="86"/>
        <source>Choose the chaser function to use as the steps for the cue list</source>
        <translation>キューリストとして使うチェイスを選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="103"/>
        <source>Detach current chaser from the cue list</source>
        <translation>現在のチェイスをキューリストから除外</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="127"/>
        <location filename="virtualconsole/vccuelistproperties.ui" line="134"/>
        <source>Behavior of the Next/Previous buttons when the chaser is not active</source>
        <translation>チェイスが既に再生中ではない時の動作</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="141"/>
        <source>Run chaser from first/last step (default)</source>
        <translation>最初から最後のステップに向けて再生(デフォルト)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="146"/>
        <source>Run chaser from next/previous step</source>
        <translation>再生中の次のステップから再生を開始</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="151"/>
        <source>Select next/previous step</source>
        <translation>選択されているステップの次から再生開始</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="156"/>
        <source>Do nothing</source>
        <translation>設定しない</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="177"/>
        <source>Playback buttons layout</source>
        <translation>プレイバックボタンの設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="183"/>
        <source>Play/Pause + Stop</source>
        <translation>再生/一時停止 + 停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="193"/>
        <source>Play/Stop + Pause</source>
        <translation>再生/一時停止 + 停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="204"/>
        <source>Playback</source>
        <translation>プレイバック</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="230"/>
        <source>Next Cue</source>
        <translation>次のキュー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="276"/>
        <source>Side Fader</source>
        <oldsource>Side Faders</oldsource>
        <translation type="unfinished">スライダー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="253"/>
        <source>Previous Cue</source>
        <translation>前のキュー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="282"/>
        <source>Behaviour</source>
        <translation>動作</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="294"/>
        <source>None</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="304"/>
        <source>Crossfade</source>
        <translation>クロスフェード</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="314"/>
        <source>Steps</source>
        <translation>ステップ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="67"/>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="215"/>
        <source>Play/Pause control</source>
        <translation>再生/一時停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="76"/>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="216"/>
        <source>Stop control</source>
        <translation>ステップ</translation>
    </message>
    <message>
        <source>Left Fader</source>
        <translation type="vanished">Lスライダー</translation>
    </message>
    <message>
        <source>Right Fader</source>
        <translation type="vanished">Rスライダー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="118"/>
        <source>External Input</source>
        <translation type="unfinished">外部入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="220"/>
        <source>Play/Stop control</source>
        <translation>再生/一時停止 + 停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="221"/>
        <source>Pause control</source>
        <translation>一時停止</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="229"/>
        <source>No function</source>
        <translation>無し</translation>
    </message>
</context>
<context>
    <name>VCFrame</name>
    <message>
        <location filename="virtualconsole/vcframe.cpp" line="1449"/>
        <source>Add</source>
        <translation>追加</translation>
    </message>
</context>
<context>
    <name>VCFrameProperties</name>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="33"/>
        <source>Frame Properties</source>
        <translation>フレームの設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="41"/>
        <source>Frame name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="57"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="63"/>
        <source>Appearance</source>
        <translation>外見</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="75"/>
        <source>Accept child widgets</source>
        <translation>中に部品を作ることを許可</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="88"/>
        <source>Allow resizing</source>
        <translation>サイズ変更を許可</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="101"/>
        <source>Show header</source>
        <translation>ヘッダーを表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="114"/>
        <source>Show enable button</source>
        <translation>ボタンを表示する</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="210"/>
        <source>Page Name</source>
        <translation>ページ名</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.cpp" line="63"/>
        <source>External Input - Enable</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.cpp" line="76"/>
        <source>External Input - Previous Page</source>
        <translation>外部入力 - 前のページへ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.cpp" line="89"/>
        <source>External Input - Next Page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="141"/>
        <source>Pages</source>
        <translation>ページ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="147"/>
        <source>Enable</source>
        <translation>ページを使用</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="180"/>
        <source>Clone first page widgets</source>
        <translation>1ページ目のコピー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="156"/>
        <source>Pages circular scrolling</source>
        <translation>ページのスクロール</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="173"/>
        <source>Number of pages:</source>
        <translation>ページ数 :</translation>
    </message>
</context>
<context>
    <name>VCLabel</name>
    <message>
        <location filename="virtualconsole/vclabel.cpp" line="46"/>
        <source>Label</source>
        <translation>メモ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vclabel.cpp" line="79"/>
        <source>Rename Label</source>
        <translation>メモの書き換え</translation>
    </message>
    <message>
        <location filename="virtualconsole/vclabel.cpp" line="79"/>
        <source>Caption:</source>
        <translation>説明: </translation>
    </message>
</context>
<context>
    <name>VCMatrix</name>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="173"/>
        <source>Animation %1</source>
        <translation>アニメーション %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="634"/>
        <source>End Color Reset</source>
        <translation>第二色のリセット</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="673"/>
        <source>Start color Red component</source>
        <translation>第一色を赤に設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="675"/>
        <source>Start color Green component</source>
        <translation>第一色を緑に設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="677"/>
        <source>Start color Blue component</source>
        <translation>第一色を青に設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="689"/>
        <source>End color Red component</source>
        <translation>第二色を赤に設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="691"/>
        <source>End color Green component</source>
        <translation>第二色を緑に設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="693"/>
        <source>End color Blue component</source>
        <translation>第二色を青に設定</translation>
    </message>
</context>
<context>
    <name>VCMatrixPresetSelection</name>
    <message>
        <location filename="virtualconsole/vcmatrixpresetselection.ui" line="33"/>
        <source>Select an animation preset</source>
        <translation>アニメーションプリセットの選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixpresetselection.ui" line="51"/>
        <source>Pattern</source>
        <translation>パターン</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixpresetselection.ui" line="69"/>
        <source>Properties</source>
        <translation>プロパティー</translation>
    </message>
</context>
<context>
    <name>VCMatrixProperties</name>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="33"/>
        <source>Animation widget properties</source>
        <oldsource>RGB Matrix properties</oldsource>
        <translation>RGBマトリックスの設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="57"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="65"/>
        <source>The function that this widget controls</source>
        <oldsource>The function that this matrix controls</oldsource>
        <translation>このボタンで操作するファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="75"/>
        <source>Widget name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="82"/>
        <source>RGB Matrix Function</source>
        <oldsource>Matrix Function</oldsource>
        <translation>マトリックスのファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="89"/>
        <source>Detach the matrix function attachment</source>
        <oldsource>Detach the button&apos;s function attachment</oldsource>
        <translation>ファンクションを除外</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="109"/>
        <source>Attach a function to this widget</source>
        <oldsource>Attach a function to this button</oldsource>
        <translation>このボタンで操作するファンクションを設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="129"/>
        <source>Text to display on the widget</source>
        <oldsource>Text to display on the button</oldsource>
        <translation>ボタン上に表示するテキスト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="136"/>
        <source>Apply color and preset changes immediately</source>
        <translation>即座に色の変更を適用する</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="143"/>
        <source>Show Label</source>
        <translation>ラベルの表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="150"/>
        <source>Show Slider</source>
        <translation>スライダーの表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="157"/>
        <source>Show Start Color Button</source>
        <translation>第一色ボタンの表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="164"/>
        <source>Show End Color Button</source>
        <translation>第二色ボタンの表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="171"/>
        <source>Show Preset Combo</source>
        <translation>コンボプリセットを表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="180"/>
        <source>Slider External Input</source>
        <translation>スライダーの外部入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="186"/>
        <source>Input universe</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="193"/>
        <source>The input universe that sends data to this widget</source>
        <translation>外部入力をウィジェットに送信</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="203"/>
        <source>Input channel</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="210"/>
        <source>The particular input channel within the input universe that sends data to this widget</source>
        <translation>外部入力からの信号をウィジェットに追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="233"/>
        <source>Choose an external input universe &amp; channel that this widget should listen to</source>
        <translation>外部入力をウィジェットに設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="236"/>
        <source>Choose...</source>
        <translation>選択...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="243"/>
        <source>When toggled, you can move an external slider/knob to assign it to the animation widget slider.</source>
        <oldsource>When toggled, you can move an external slider/knob to assign it to this virtual console slider.</oldsource>
        <translation>自動判別をオンにして、外部入力機器(MIDIコントローラーなど)のフェーダーやボタンを操作してください。</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="246"/>
        <source>Auto Detect</source>
        <translation>自動判別</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="273"/>
        <source>Custom Controls</source>
        <translation>カスタムコントロール</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="370"/>
        <source> Add start color knobs</source>
        <translation>第一色ノブを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="281"/>
        <source> Add end color knobs</source>
        <translation>第二色ノブを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="337"/>
        <source> Add end color reset</source>
        <translation>第二色の削除を追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="348"/>
        <source> Remove</source>
        <translation>削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="312"/>
        <source> Add preset</source>
        <translation>プリセットを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="299"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="304"/>
        <source>Value</source>
        <translation>値</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="359"/>
        <source> Add start color</source>
        <translation>第一色を追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="381"/>
        <source> Add end color</source>
        <translation>第二色を追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="323"/>
        <source> Add text</source>
        <translation>テキストを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="152"/>
        <source>No function</source>
        <translation>ファンクションなし</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="228"/>
        <source>Start Color</source>
        <translation>第一色</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="234"/>
        <source>Start Color Knob</source>
        <translation>第一色ノブ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="240"/>
        <source>End Color</source>
        <translation>第二色</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="246"/>
        <source>End Color Knob</source>
        <translation>第二色ノブ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="252"/>
        <source>End Color Reset</source>
        <translation>第二色をリセット</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="257"/>
        <source>Animation</source>
        <translation>アニメーション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="279"/>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="411"/>
        <source>Text</source>
        <translation>テキスト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="410"/>
        <source>Enter a text</source>
        <translation>テキストを決定</translation>
    </message>
</context>
<context>
    <name>VCPropertiesEditor</name>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="33"/>
        <source>Virtual Console Settings</source>
        <oldsource>Virtual Console properties</oldsource>
        <translation>バーチャルコンソールの設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="62"/>
        <location filename="virtualconsole/vcproperties.ui" line="88"/>
        <source>Widget grid layout X resolution</source>
        <translation>コンソールの幅</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="761"/>
        <source>Choose...</source>
        <translation>選択...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="43"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="49"/>
        <source>Virtual Console Size</source>
        <translation>調光卓の大きさ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="55"/>
        <source>Width</source>
        <translation>幅</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="81"/>
        <source>Height</source>
        <translation>高さ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="124"/>
        <source>Widgets</source>
        <translation>部品</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="136"/>
        <source>Widgets default properties</source>
        <translation>部品のデフォルトを設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="512"/>
        <source>Button size</source>
        <oldsource>Button size:</oldsource>
        <translation>ボタンの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="243"/>
        <source>Solo frame size</source>
        <oldsource>Solo frame size:</oldsource>
        <translation>ソロフレームの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="222"/>
        <source>Slider size</source>
        <oldsource>Slider size:</oldsource>
        <translation>フェーダーの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="545"/>
        <source>Speed dial size</source>
        <oldsource>Speed dial size:</oldsource>
        <translation>スピードダイヤルの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="158"/>
        <source>XY Pad size</source>
        <oldsource>XY Pad size:</oldsource>
        <translation>XYパッドの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="519"/>
        <source>Cue List size</source>
        <oldsource>Cue List size:</oldsource>
        <translation>キューリストの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="328"/>
        <source>Frame size</source>
        <oldsource>Frame size:</oldsource>
        <translation>フレームの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="165"/>
        <source>Speed dial value</source>
        <oldsource>Speed dial value:</oldsource>
        <translation>スピードダイヤルの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="568"/>
        <source>Button status style</source>
        <oldsource>Button status style:</oldsource>
        <translation>ボタン状態の表示 :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="502"/>
        <source>LED</source>
        <translation>LED</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="250"/>
        <source>Border</source>
        <translation>Border</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="229"/>
        <source>Audio triggers size</source>
        <oldsource>Audio triggers size:</oldsource>
        <translation>オーディオトリガーの大きさ :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="361"/>
        <location filename="virtualconsole/vcproperties.ui" line="447"/>
        <location filename="virtualconsole/vcproperties.ui" line="585"/>
        <location filename="virtualconsole/vcproperties.ui" line="608"/>
        <source>px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="575"/>
        <source>Animation size</source>
        <oldsource>RGB Matrix size</oldsource>
        <translation>RGBマトリックスの大きさ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="625"/>
        <source>Grand Master</source>
        <translation>グランドマスター(GM)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="631"/>
        <source>Channels</source>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="637"/>
        <source>Apply Grand Master only to Intensity channels.</source>
        <translation>グランドマスターは明るさチャンネルにのみ適用</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="640"/>
        <source>Intensity</source>
        <translation>明るさのみ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="647"/>
        <source>Apply Grand Master to all channels.</source>
        <translation>グランドマスターをすべてのチャンネルに適用</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="650"/>
        <source>All channels</source>
        <translation>すべてのチャンネル</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="660"/>
        <source>Values</source>
        <translation>値</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="666"/>
        <source>Make Grand Master reduce levels by a percentage.</source>
        <translation>レベルを乗算で減少</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="669"/>
        <source>Reduce</source>
        <translation>乗算</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="676"/>
        <source>Make Grand Master limit the maximum channel values.</source>
        <translation>上限値にする</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="679"/>
        <source>Limit</source>
        <translation>上限</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="689"/>
        <source>External Input</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="695"/>
        <source>Input Universe</source>
        <translation>外部Universe</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="702"/>
        <source>Input universe for Grand Master slider.</source>
        <translation>外部入力をグランドマスターに対応させる</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="712"/>
        <source>Input Channel</source>
        <translation>入力チャンネル</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="719"/>
        <source>Input channel for Grand Master slider.</source>
        <translation>グランドマスター</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="742"/>
        <source>When toggled, you can move an external slider/knob to assign it to the Grand Master slider.</source>
        <translation>自動判別をオンにして、外部入力機器(MIDIコントローラーなど)のフェーダーやボタンを操作してください。</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="745"/>
        <source>Auto Detect</source>
        <translation>自動判別</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="758"/>
        <source>Choose an external input universe &amp; channel that the Grand Master slider should listen to.</source>
        <translation>外部入力をグランドマスターに関連づける</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="784"/>
        <source>Slider movement</source>
        <translation>上下</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="790"/>
        <source>Normal</source>
        <translation>標準</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="797"/>
        <source>Inverted</source>
        <translation>反転</translation>
    </message>
</context>
<context>
    <name>VCSlider</name>
    <message>
        <location filename="virtualconsole/vcslider.cpp" line="236"/>
        <source>Slider %1</source>
        <translation>フェーダー %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcslider.cpp" line="617"/>
        <source>Reset channels override</source>
        <translation>強制的にリセット</translation>
    </message>
</context>
<context>
    <name>VCSliderProperties</name>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="33"/>
        <source>Slider properties</source>
        <translation>フェーダー設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="46"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="61"/>
        <source>Name of the slider</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="179"/>
        <source>Value display style</source>
        <translation>数値表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="185"/>
        <source>Show exact DMX values</source>
        <translation>DMX値を表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="198"/>
        <source>Show value as percentage</source>
        <translation>パーセント表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="201"/>
        <source>Percentage</source>
        <translation>パーセント</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="70"/>
        <source>Slider movement</source>
        <translation>上下</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="76"/>
        <source>Normal</source>
        <translation>標準(上が100%)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="86"/>
        <source>Inverted</source>
        <translation>反転(下が100%)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="225"/>
        <source>Value range</source>
        <translation>上限/下限</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="231"/>
        <source>Low limit</source>
        <translation>下限</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="248"/>
        <source>High limit</source>
        <translation>上限</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="219"/>
        <source>Level</source>
        <translation>チャンネル</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="238"/>
        <source>Lowest DMX value that can be set with this slider</source>
        <translation>フェーダー値の下限</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="410"/>
        <source>Intensity</source>
        <translation>明るさ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="431"/>
        <source>Gobo/Effect/Macro</source>
        <oldsource>Gobo/Effect</oldsource>
        <translation>ゴボ/エフェクト/マクロ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="462"/>
        <source>Playback</source>
        <translation>プレイバック</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="468"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="474"/>
        <source>Function that is attached to the slider</source>
        <translation>このフェーダーで操作する機能</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="484"/>
        <source>Attach a function to the slider</source>
        <translation>このフェーダーで操作する機能の選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="504"/>
        <source>Detach the current function from the slider</source>
        <translation>解放</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="540"/>
        <source>Make the slider control a function</source>
        <translation>ファンクションフェダーの作成</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="543"/>
        <source>Switch to Playback Mode</source>
        <translation>プレイバックモードにする</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="188"/>
        <source>Actual</source>
        <translation>DMX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="54"/>
        <source>Widget name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="109"/>
        <source>Widget appearance</source>
        <translation>外見</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="115"/>
        <source>Slider</source>
        <translation>フェーダー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="132"/>
        <source>Knob</source>
        <translation>つまみ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="211"/>
        <source>Catch up with the external controller input value</source>
        <translation>外部コントローラからの操作を受け付ける</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="255"/>
        <source>Highest DMX value that can be set with this slider</source>
        <translation>フェーダー値の上限</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="284"/>
        <source>Set value range from the selected capability</source>
        <translation>機能に応じたDMX値を上限/下限にする</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="287"/>
        <source>From capability</source>
        <translation>機能から設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="313"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="318"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="323"/>
        <source>Range</source>
        <translation>範囲</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="333"/>
        <source>Select all channels</source>
        <translation>全てのチャンネルを選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="336"/>
        <source>All</source>
        <translation>全て選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="343"/>
        <source>Unselect everything</source>
        <translation>全て選択解除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="346"/>
        <location filename="virtualconsole/vcsliderproperties.ui" line="400"/>
        <source>None</source>
        <translation>全て解除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="353"/>
        <source>Invert selection</source>
        <translation>選択/非選択を逆転</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="356"/>
        <source>Invert</source>
        <translation>逆転</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="376"/>
        <source>Choose channels by channel group</source>
        <translation>チャンネルグループから選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="379"/>
        <source>By group...</source>
        <translation>グループから...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="441"/>
        <source>Monitor the selected channels and update the slider level</source>
        <translation>選択したチャンネルの変化をスライダーに表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="394"/>
        <source>Click &amp;&amp; Go</source>
        <translation>Click &amp;&amp; Go を利用</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="417"/>
        <source>RGB</source>
        <translation>RGB</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="424"/>
        <source>CMY</source>
        <translation>CMY</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="451"/>
        <source>Make the slider control the level of a set of channels</source>
        <translation>チャンネルフェダーを作成する</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="454"/>
        <source>Switch to Level Mode</source>
        <translation>チャンネルフェーダーにする</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="551"/>
        <source>Submaster</source>
        <translation>フレームのマスター</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="557"/>
        <source>Slider submaster mode is active</source>
        <translation>フレームのマスターとして動作中</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="580"/>
        <source>Make the slider act as a submaster</source>
        <translation>フレームのマスターにする</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="583"/>
        <source>Switch to Submaster Mode</source>
        <translation>フレームのマスターにする</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="178"/>
        <source>Override reset control</source>
        <translation>強制的にリセット</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="641"/>
        <source>Select channels by group</source>
        <translation>チャンネルグループから選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="642"/>
        <source>Select a channel group</source>
        <translation>チャンネルグループを選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="707"/>
        <source>No function</source>
        <translation>無し</translation>
    </message>
</context>
<context>
    <name>VCSoloFrameProperties</name>
    <message>
        <location filename="virtualconsole/vcsoloframeproperties.cpp" line="40"/>
        <source>Solo Frame properties</source>
        <translation>ソロフレームの設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsoloframeproperties.cpp" line="43"/>
        <source>Mix sliders in playback mode</source>
        <translation>プレイバックモードでクロスフェーダ</translation>
    </message>
</context>
<context>
    <name>VCSpeedDial</name>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="81"/>
        <source>Duration</source>
        <translation>継続時間</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="100"/>
        <source>Divide the current time by 2</source>
        <translation>2つに時間を分ける</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="123"/>
        <source>Multiply the current time by 2</source>
        <translation>時間を半分にする</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="131"/>
        <source>Reset the current factor to 1x</source>
        <translation>変更をリセット</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="144"/>
        <source>Apply</source>
        <translation>決定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="145"/>
        <source>Send the current value to the function now</source>
        <translation>決定した値を今すぐ設定</translation>
    </message>
</context>
<context>
    <name>VCSpeedDialProperties</name>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="33"/>
        <source>Speed Dial Properties</source>
        <translation>スピードダイヤルの詳細</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="43"/>
        <source>Speed Dial Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="50"/>
        <source>Title of the dial</source>
        <translation>スピードダイヤルの名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="61"/>
        <source>Functions</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="101"/>
        <source>Copy the factors of the selected function</source>
        <translation>選択したファンクションからコピー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="121"/>
        <source>Paste copied factors to all selected functions</source>
        <translation>選択したファンクションにペースト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="182"/>
        <source>Function</source>
        <translation>ファンクション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="187"/>
        <source>Fade In factor</source>
        <oldsource>Fade In *</oldsource>
        <translation>フェードイン</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="190"/>
        <source>Multiplier applied before time is sent as Fade In Time to the function.</source>
        <translation>フェードアウトインを定数倍</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="195"/>
        <source>Fade Out factor</source>
        <oldsource>Fade Out *</oldsource>
        <translation>フェードアウト時間</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="198"/>
        <source>Multiplier applied before time is sent as Fade Out Time to the function.</source>
        <translation>フェードアウトタイムを定数倍</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="203"/>
        <source>Duration factor (+tap)</source>
        <oldsource>Duration * (+tap)</oldsource>
        <translation>再生継続時間をタップシンクで調整</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="206"/>
        <source>Multiplier applied before time is sent as Duration to the function.</source>
        <translation>再生継続時間を定数倍する</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="67"/>
        <source>Add functions to be controlled</source>
        <translation>ファンクションの追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="84"/>
        <source>Remove selected functions</source>
        <translation>選択したファンクションの削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="215"/>
        <source>Input</source>
        <translation>外部入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="221"/>
        <source>Absolute Value</source>
        <translation>スピード値を直接入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="429"/>
        <source>Preset Name</source>
        <translation>プリセット名</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="233"/>
        <source>Range</source>
        <translation>範囲</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="246"/>
        <source>ms precision</source>
        <translation>ms&#x3000;パーセント</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="259"/>
        <source>Tap</source>
        <translation>タップ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="267"/>
        <source>Apply</source>
        <translation>決定</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="372"/>
        <source>Multiplier</source>
        <translation>定数倍</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="378"/>
        <source>Reset multiplier factor when the dial value changes</source>
        <translation>定数倍の変更をリセット</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="408"/>
        <source>Presets</source>
        <translation>プリセット</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="454"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="459"/>
        <source>Value</source>
        <translation>値</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="439"/>
        <source> Add preset</source>
        <translation> プリセットを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="467"/>
        <source> Remove</source>
        <translation> 削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="416"/>
        <source>Preset name</source>
        <translation>プリセット名</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="289"/>
        <source>Appearance</source>
        <translation>外見</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="295"/>
        <source>Show plus and minus buttons</source>
        <translation>＋ボタンを表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="344"/>
        <source>Show multiplier and divisor buttons</source>
        <translation>マルチボタンとディバイドボタンを表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="302"/>
        <source>Show the central dial</source>
        <translation>コントロールダイアルを表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="309"/>
        <source>Show the tap button</source>
        <translation>タップシンクを表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="351"/>
        <source>Show the apply button</source>
        <translation>決定ボタンを表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="316"/>
        <source>Show the hours field</source>
        <translation>時間を表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="323"/>
        <source>Show the minutes field</source>
        <translation>分を表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="330"/>
        <source>Show the seconds field</source>
        <translation>秒を表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="337"/>
        <source>Show the milliseconds field</source>
        <translation>ミリ秒を表示</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.cpp" line="115"/>
        <source>Multiply by 2 Input</source>
        <translation>2つの入力</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.cpp" line="124"/>
        <source>Divide by 2 Input</source>
        <translation>2つの入力を分ける</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.cpp" line="133"/>
        <source>Factor Reset Input</source>
        <translation>入力をリセット</translation>
    </message>
</context>
<context>
    <name>VCWidget</name>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="144"/>
        <source>Button</source>
        <translation>ボタン</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="145"/>
        <source>Slider</source>
        <translation>フェーダー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="146"/>
        <source>XYPad</source>
        <translation>XYパッド</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="147"/>
        <source>Frame</source>
        <translation>フレーム</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="148"/>
        <source>Solo frame</source>
        <translation>ソロフレーム</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="149"/>
        <source>Speed dial</source>
        <translation>スピードダイヤル</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="150"/>
        <source>Cue list</source>
        <translation>キューリスト</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="151"/>
        <source>Label</source>
        <translation>メモ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="152"/>
        <source>Audio Triggers</source>
        <translation>オーディオトリガー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="153"/>
        <source>Animation</source>
        <translation>アニメーション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="154"/>
        <source>Clock</source>
        <translation>現在時刻</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="157"/>
        <location filename="virtualconsole/vcwidget.cpp" line="159"/>
        <source>Unknown</source>
        <translation>不明</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="513"/>
        <source>This widget has no properties</source>
        <translation>このウィジェットに設定項目はありません</translation>
    </message>
</context>
<context>
    <name>VCWidgetSelection</name>
    <message>
        <location filename="virtualconsole/vcwidgetselection.ui" line="33"/>
        <source>Virtual Console widget selection</source>
        <translation>部品の選択</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidgetselection.ui" line="42"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidgetselection.ui" line="47"/>
        <source>Type</source>
        <translation>タイプ</translation>
    </message>
</context>
<context>
    <name>VCXYPadArea</name>
    <message>
        <location filename="virtualconsole/vcxypadarea.cpp" line="302"/>
        <source>Shift: fine, Ctrl:10x</source>
        <translation>Shift: 微調整, Ctrl: 10倍</translation>
    </message>
</context>
<context>
    <name>VCXYPadFixtureEditor</name>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="33"/>
        <source>XY Pad Fixture</source>
        <translation>XYパッド</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="39"/>
        <source>Horizontal X-Axis</source>
        <translation>水平/X軸</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="45"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="101"/>
        <source>Minimum</source>
        <translation>最小</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="52"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="69"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="108"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="125"/>
        <source>%</source>
        <translation> %</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="62"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="118"/>
        <source>Maximum</source>
        <translation>最大</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="85"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="141"/>
        <source>Reverse</source>
        <translation>反転</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="95"/>
        <source>Vertical Y-Axis</source>
        <translation>垂直/Y軸</translation>
    </message>
</context>
<context>
    <name>VCXYPadProperties</name>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="33"/>
        <source>XY Pad Properties</source>
        <translation>XYパッドの詳細</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="53"/>
        <source>General</source>
        <translation>全般</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="102"/>
        <source>XY Pad Name</source>
        <translation>XYパッドの名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="109"/>
        <source>The name of this XY Pad</source>
        <translation>このXYパッドの名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="143"/>
        <source>Fixtures</source>
        <translation>機器</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="149"/>
        <source>List of fixtures that are controlled by this pad</source>
        <translation>このXYパッドで操作する機器</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="174"/>
        <source>Fixture</source>
        <translation>機器</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="179"/>
        <source>X-Axis</source>
        <translation>X軸</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="184"/>
        <source>Y-Axis</source>
        <translation>Y軸</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="192"/>
        <source>Add fixture(s) to the pad</source>
        <translation>機器の追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="252"/>
        <source>Range Display Mode</source>
        <translation>範囲指定モード</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="258"/>
        <source>Degrees</source>
        <translation>角度</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="268"/>
        <source>Percentage</source>
        <translation>パーセント</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="278"/>
        <source>DMX</source>
        <translation>DMX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="302"/>
        <source>Presets</source>
        <translation>新しいアニメーション</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="323"/>
        <source>Name</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="333"/>
        <source>Add position</source>
        <translation>ポジションを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="344"/>
        <source>Add EFX</source>
        <translation>EFXを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="355"/>
        <source>Add Scene</source>
        <translation>シーンを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="366"/>
        <source>Add Fixture Group</source>
        <translation>フィクスチャーグループを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="377"/>
        <source>Remove</source>
        <translation>削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="388"/>
        <source>Move Up</source>
        <translation>移動</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="399"/>
        <source>Move Down</source>
        <translation>1つ下へ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="418"/>
        <source>Preset name</source>
        <translation>プリセット名</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="85"/>
        <source>Pan / Horizontal Axis</source>
        <translation>パン/X軸</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="98"/>
        <source>Tilt / Vertical Axis</source>
        <translation>チルト/Y軸</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="116"/>
        <source>Y-Axis slider movement</source>
        <translation>Y軸の上下</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="122"/>
        <source>Normal</source>
        <translation>標準</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="132"/>
        <source>Inverted</source>
        <translation>反転</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="212"/>
        <source>Remove selected fixture(s) from the pad</source>
        <translation>選択した機器の削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="232"/>
        <source>Edit the selected fixture&apos;s axis</source>
        <translation>機器の編集</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="111"/>
        <source>Width</source>
        <translation>幅</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="119"/>
        <source>Height</source>
        <translation>高さ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="370"/>
        <source>Remove fixtures</source>
        <translation>機器の削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="371"/>
        <source>Do you want to remove the selected fixtures?</source>
        <translation>選択した機器を削除しますか？</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="734"/>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="783"/>
        <source>Error</source>
        <translation>エラー</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="735"/>
        <source>The selected Scene does not include any Pan or Tilt channel.
Please select one with such channels.</source>
        <translation>選択したシーンにはPan,Tiltを操作するチャンネルが含まれていません、ポジションを操作するファンクションを選択してください</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="784"/>
        <source>Please select at least one fixture or head to create this type of preset!</source>
        <translation>選択したフィクスチャーにはPan,Tiltが定義されていません。ポジション操作が定義されているフィクスチャーを選択してください</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="791"/>
        <source>Fixture Group</source>
        <translation>機器グループ</translation>
    </message>
</context>
<context>
    <name>VideoEditor</name>
    <message>
        <location filename="videoeditor.ui" line="33"/>
        <source>Video Editor</source>
        <translation>ビデオエディタ</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="54"/>
        <source>Video name</source>
        <oldsource>Video name:</oldsource>
        <translation>動画名</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="162"/>
        <source>Play the video and see how it looks like</source>
        <translation>動画の再生を行います</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="189"/>
        <source>File name</source>
        <oldsource>File name:</oldsource>
        <translation>ファイル名 :</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="196"/>
        <source>Duration</source>
        <oldsource>Duration:</oldsource>
        <translation>継続時間 :</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="203"/>
        <source>Resolution</source>
        <oldsource>Resolution:</oldsource>
        <translation>解像度</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="217"/>
        <source>Audio codec</source>
        <oldsource>Audio codec:</oldsource>
        <translation>オーディオコーデック</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="210"/>
        <source>Video codec</source>
        <oldsource>Video codec:</oldsource>
        <translation>ビデオコーデック</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="103"/>
        <source>Set an arbitrary URL for this Video</source>
        <translation>任意の動画ファイルのURLを指定してください</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="133"/>
        <source>Name of the function being edited</source>
        <translation>ファンクション名を編集</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="231"/>
        <source>Output Screen</source>
        <oldsource>Output Screen:</oldsource>
        <translation>出力画面: </translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="238"/>
        <source>Video output</source>
        <translation>ビデオアウトプット</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="244"/>
        <source>Windowed</source>
        <translation>ウィンドウ</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="254"/>
        <source>Fullscreen</source>
        <translation>フルスクリーン</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="264"/>
        <source>Playback mode</source>
        <translation>再生モード</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="270"/>
        <source>Single shot</source>
        <translation>一方通行</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="280"/>
        <source>Loop</source>
        <translation>繰り返し</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="118"/>
        <source>Open Video File</source>
        <translation>ビデオファイルを開く</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="126"/>
        <source>Video Files (%1)</source>
        <translation>ビデオファイル (%1)</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="128"/>
        <source>All Files (*.*)</source>
        <translation>すべてのファイル (*.*)</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="130"/>
        <source>All Files (*)</source>
        <translation>すべてのファイル (*)</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="158"/>
        <source>Video source URL</source>
        <translation>動画のURL</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="159"/>
        <source>Enter a URL:</source>
        <translation>URLを入力: </translation>
    </message>
</context>
<context>
    <name>VideoItem</name>
    <message>
        <location filename="showmanager/videoitem.cpp" line="50"/>
        <source>Fullscreen</source>
        <translation>フルスクリーン</translation>
    </message>
    <message>
        <location filename="showmanager/videoitem.cpp" line="184"/>
        <source>Screen %1</source>
        <translation>スクリーン %1</translation>
    </message>
</context>
<context>
    <name>VirtualConsole</name>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="372"/>
        <source>Cut</source>
        <translation>カット</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="375"/>
        <source>Copy</source>
        <translation>コピー</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="378"/>
        <source>Paste</source>
        <translation>ペースト</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="382"/>
        <source>Delete</source>
        <translation>削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="408"/>
        <location filename="virtualconsole/virtualconsole.cpp" line="422"/>
        <location filename="virtualconsole/virtualconsole.cpp" line="435"/>
        <source>Default</source>
        <translation>デフォルト</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="432"/>
        <source>Font</source>
        <translation>フォント</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="445"/>
        <source>Sunken</source>
        <translation>後ろへ</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="448"/>
        <source>Raised</source>
        <translation>前面へ</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="451"/>
        <source>None</source>
        <translation>無し</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="479"/>
        <source>&amp;Add</source>
        <translation>追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="500"/>
        <source>&amp;Edit</source>
        <translation>編集</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="304"/>
        <source>New Button</source>
        <translation>ボタンを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="307"/>
        <source>New Button Matrix</source>
        <translation>ボタンをまとめて追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="310"/>
        <source>New Slider</source>
        <translation>フェーダーを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="316"/>
        <source>New Knob</source>
        <translation>つまみを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="319"/>
        <source>New Speed Dial</source>
        <translation>スピードダイヤルを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="322"/>
        <source>New XY pad</source>
        <translation>XYパッドを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="325"/>
        <source>New Cue list</source>
        <translation>キューリストを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="328"/>
        <source>New Frame</source>
        <translation>通常フレームを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="331"/>
        <source>New Solo frame</source>
        <translation>ソロフレームを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="334"/>
        <source>New Label</source>
        <translation>メモを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="337"/>
        <source>New Audio Triggers</source>
        <translation>オーディオトリガーを追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="340"/>
        <source>New Clock</source>
        <translation>時計を追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="365"/>
        <source>Virtual Console Settings</source>
        <translation>バーチャルコンソール設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="385"/>
        <source>Widget Properties</source>
        <translation>部品の詳細</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="388"/>
        <source>Rename Widget</source>
        <translation>部品の名前を変更</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="402"/>
        <source>Background Color</source>
        <translation>背景色</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="405"/>
        <source>Background Image</source>
        <translation>背景画像</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="419"/>
        <source>Font Colour</source>
        <translation>フォント色</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="462"/>
        <source>Bring to front</source>
        <translation>前面へ</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="465"/>
        <source>Send to back</source>
        <translation>背面へ</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="512"/>
        <source>&amp;Background</source>
        <translation>背景</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="520"/>
        <source>&amp;Foreground</source>
        <translation>表示の設定</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="527"/>
        <source>F&amp;ont</source>
        <translation>前</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="534"/>
        <source>F&amp;rame</source>
        <translation>フレーム</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="542"/>
        <source>Stacking &amp;order</source>
        <translation>並び順</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1271"/>
        <source>Images</source>
        <translation>画像</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="313"/>
        <source>New Slider Matrix</source>
        <translation>フェーダーをまとめて追加</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="343"/>
        <source>New Animation</source>
        <translation>新しいアニメーション</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="893"/>
        <source>Knob %1</source>
        <translation>ノブ %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1162"/>
        <source>Do you wish to delete the selected widgets?</source>
        <translation>選択した部品を削除しますか？</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1163"/>
        <source>Delete widgets</source>
        <translation>部品の削除</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1216"/>
        <source>Rename widgets</source>
        <translation>部品の名前を変更</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1216"/>
        <source>Caption:</source>
        <translation>注釈 :</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1269"/>
        <source>Select background image</source>
        <translation>背景画像を選択</translation>
    </message>
</context>
</TS>
