<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  vcbuttonproperties.ui

  Copyright (c) 2015 Massimo Call<PERSON>ari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>VCButtonProperties</class>
 <widget class="QDialog" name="VCButtonProperties">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>512</width>
    <height>541</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Button properties</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="4" column="0" colspan="2">
    <widget class="QGroupBox" name="groupBox_4">
     <property name="title">
      <string>On button press...</string>
     </property>
     <layout class="QVBoxLayout">
      <item>
       <widget class="QRadioButton" name="m_toggle">
        <property name="toolTip">
         <string>Toggle the assigned function on/off with this button</string>
        </property>
        <property name="text">
         <string>Toggle function on/off</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="m_flash">
        <property name="toolTip">
         <string>Flash the assigned function with this button</string>
        </property>
        <property name="text">
         <string>Flash function (only for scenes)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="m_blackout">
        <property name="text">
         <string>Toggle Blackout</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QRadioButton" name="m_stopAll">
          <property name="sizePolicy">
           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>Stop All Functions</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="m_safFadeLabel">
          <property name="text">
           <string>Fade time:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="m_fadeOutEdit">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string notr="true">.00</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QToolButton" name="m_speedDialButton">
          <property name="icon">
           <iconset resource="../qlcui.qrc">
            <normaloff>:/speed.png</normaloff>:/speed.png</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="6" column="0" colspan="2">
    <widget class="QDialogButtonBox" name="m_buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="5" column="0" colspan="2">
    <widget class="QGroupBox" name="m_intensityGroup">
     <property name="toolTip">
      <string>Adjust function intensity when it is running</string>
     </property>
     <property name="title">
      <string>Adjust Function Intensity</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QSlider" name="m_intensitySlider">
        <property name="sizePolicy">
         <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
          <horstretch>1</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="toolTip">
         <string>Function's adjusted intensity percentage when run</string>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>100</number>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="invertedAppearance">
         <bool>false</bool>
        </property>
        <property name="invertedControls">
         <bool>false</bool>
        </property>
        <property name="tickPosition">
         <enum>QSlider::TicksAbove</enum>
        </property>
        <property name="tickInterval">
         <number>10</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="m_intensityEdit">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="maximumSize">
         <size>
          <width>50</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="maxLength">
         <number>4</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QGroupBox" name="m_generalGroup">
     <property name="sizePolicy">
      <sizepolicy hsizetype="MinimumExpanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>General</string>
     </property>
     <layout class="QGridLayout" name="gridLayoutGeneral">
      <item row="0" column="0">
       <widget class="QLabel" name="m_nameLabel">
        <property name="text">
         <string>Button label</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1" colspan="3">
       <widget class="QLineEdit" name="m_nameEdit">
        <property name="toolTip">
         <string>Text to display on the button</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="m_functionLabel">
        <property name="text">
         <string>Function</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="m_functionEdit">
        <property name="toolTip">
         <string>The function that this button controls</string>
        </property>
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QToolButton" name="m_attachFunction">
        <property name="toolTip">
         <string>Attach a function to this button</string>
        </property>
        <property name="text">
         <string notr="true">...</string>
        </property>
        <property name="icon">
         <iconset resource="../qlcui.qrc">
          <normaloff>:/attach.png</normaloff>:/attach.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>32</width>
          <height>32</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QToolButton" name="m_detachFunction">
        <property name="toolTip">
         <string>Detach the button's function attachment</string>
        </property>
        <property name="text">
         <string notr="true">...</string>
        </property>
        <property name="icon">
         <iconset resource="../qlcui.qrc">
          <normaloff>:/detach.png</normaloff>:/detach.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>32</width>
          <height>32</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" colspan="2">
    <widget class="QFrame" name="frame">
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QVBoxLayout" name="m_extControlLayout"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../qlcui.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>VCButtonProperties</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>VCButtonProperties</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
