<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  vcxypadproperties.ui

  Copyright (c) 2015 Massimo Callegari

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>VCXYPadProperties</class>
 <widget class="QDialog" name="VCXYPadProperties">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>546</width>
    <height>472</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>XY Pad Properties</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="2" column="0">
    <widget class="QDialogButtonBox" name="m_buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QTabWidget" name="m_tab">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="general">
      <attribute name="title">
       <string>General</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_5">
       <item row="2" column="0" colspan="2">
        <widget class="QFrame" name="frame">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="m_extInputLayout"/>
          </item>
          <item>
           <layout class="QHBoxLayout" name="m_sizeInputLayout"/>
          </item>
         </layout>
        </widget>
       </item>
       <item row="3" column="0" colspan="2">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="m_nameLabel">
         <property name="text">
          <string>XY Pad Name</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="m_nameEdit">
         <property name="toolTip">
          <string>The name of this XY Pad</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="2">
        <widget class="QGroupBox" name="groupBox_3">
         <property name="title">
          <string>Y-Axis slider movement</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QRadioButton" name="m_YNormalRadio">
            <property name="text">
             <string>Normal</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_YInvertedRadio">
            <property name="text">
             <string>Inverted</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="fixtures">
      <attribute name="title">
       <string>Fixtures</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0" rowspan="4">
        <widget class="QTreeWidget" name="m_tree">
         <property name="toolTip">
          <string>List of fixtures that are controlled by this pad</string>
         </property>
         <property name="showDropIndicator" stdset="0">
          <bool>false</bool>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::ExtendedSelection</enum>
         </property>
         <property name="rootIsDecorated">
          <bool>false</bool>
         </property>
         <property name="itemsExpandable">
          <bool>false</bool>
         </property>
         <property name="allColumnsShowFocus">
          <bool>true</bool>
         </property>
         <property name="expandsOnDoubleClick">
          <bool>false</bool>
         </property>
         <column>
          <property name="text">
           <string>Fixture</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>X-Axis</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Y-Axis</string>
          </property>
         </column>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QToolButton" name="m_addButton">
         <property name="toolTip">
          <string>Add fixture(s) to the pad</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../qlcui.qrc">
           <normaloff>:/edit_add.png</normaloff>:/edit_add.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QToolButton" name="m_removeButton">
         <property name="toolTip">
          <string>Remove selected fixture(s) from the pad</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../qlcui.qrc">
           <normaloff>:/edit_remove.png</normaloff>:/edit_remove.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QToolButton" name="m_editButton">
         <property name="toolTip">
          <string>Edit the selected fixture's axis</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../qlcui.qrc">
           <normaloff>:/edit.png</normaloff>:/edit.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>Range Display Mode</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QRadioButton" name="m_degreesRadio">
            <property name="text">
             <string>Degrees</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_percentageRadio">
            <property name="text">
             <string>Percentage</string>
            </property>
            <property name="checked">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="m_dmxRadio">
            <property name="text">
             <string>DMX</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="3" column="1">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>142</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="presets">
      <attribute name="title">
       <string>Presets</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QTreeWidget" name="m_presetsTree">
           <property name="sizePolicy">
            <sizepolicy hsizetype="MinimumExpanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="rootIsDecorated">
            <bool>false</bool>
           </property>
           <property name="itemsExpandable">
            <bool>false</bool>
           </property>
           <column>
            <property name="text">
             <string>Name</string>
            </property>
           </column>
          </widget>
         </item>
         <item>
          <layout class="QVBoxLayout" name="m_presetLayout">
           <item>
            <widget class="QPushButton" name="m_addPositionButton">
             <property name="text">
              <string>Add position</string>
             </property>
             <property name="icon">
              <iconset resource="../qlcui.qrc">
               <normaloff>:/xypad.png</normaloff>:/xypad.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="m_addEfxButton">
             <property name="text">
              <string>Add EFX</string>
             </property>
             <property name="icon">
              <iconset resource="../qlcui.qrc">
               <normaloff>:/efx.png</normaloff>:/efx.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="m_addSceneButton">
             <property name="text">
              <string>Add Scene</string>
             </property>
             <property name="icon">
              <iconset resource="../qlcui.qrc">
               <normaloff>:/scene.png</normaloff>:/scene.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="m_addFxGroupButton">
             <property name="text">
              <string>Add Fixture Group</string>
             </property>
             <property name="icon">
              <iconset resource="../qlcui.qrc">
               <normaloff>:/group.png</normaloff>:/group.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="m_removePresetButton">
             <property name="text">
              <string>Remove</string>
             </property>
             <property name="icon">
              <iconset resource="../qlcui.qrc">
               <normaloff>:/edit_remove.png</normaloff>:/edit_remove.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="m_moveUpPresetButton">
             <property name="text">
              <string>Move Up</string>
             </property>
             <property name="icon">
              <iconset resource="../qlcui.qrc">
               <normaloff>:/up.png</normaloff>:/up.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="m_moveDownPresetButton">
             <property name="text">
              <string>Move Down</string>
             </property>
             <property name="icon">
              <iconset resource="../qlcui.qrc">
               <normaloff>:/down.png</normaloff>:/down.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QLabel" name="label">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>Preset name</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="m_presetNameEdit">
               <property name="sizePolicy">
                <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <widget class="QFrame" name="frame_3">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="m_presetInputLayout"/>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../qlcui.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>VCXYPadProperties</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>266</x>
     <y>390</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>VCXYPadProperties</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>334</x>
     <y>390</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_addButton</sender>
   <signal>clicked()</signal>
   <receiver>VCXYPadProperties</receiver>
   <slot>slotAddClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>400</x>
     <y>115</y>
    </hint>
    <hint type="destinationlabel">
     <x>438</x>
     <y>15</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_removeButton</sender>
   <signal>clicked()</signal>
   <receiver>VCXYPadProperties</receiver>
   <slot>slotRemoveClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>400</x>
     <y>155</y>
    </hint>
    <hint type="destinationlabel">
     <x>439</x>
     <y>87</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_editButton</sender>
   <signal>clicked()</signal>
   <receiver>VCXYPadProperties</receiver>
   <slot>slotEditClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>400</x>
     <y>195</y>
    </hint>
    <hint type="destinationlabel">
     <x>416</x>
     <y>159</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_tree</sender>
   <signal>currentItemChanged(QTreeWidgetItem*,QTreeWidgetItem*)</signal>
   <receiver>VCXYPadProperties</receiver>
   <slot>slotSelectionChanged(QTreeWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>161</x>
     <y>162</y>
    </hint>
    <hint type="destinationlabel">
     <x>3</x>
     <y>154</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>m_tree</sender>
   <signal>itemDoubleClicked(QTreeWidgetItem*,int)</signal>
   <receiver>VCXYPadProperties</receiver>
   <slot>slotEditClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>41</x>
     <y>147</y>
    </hint>
    <hint type="destinationlabel">
     <x>4</x>
     <y>73</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>slotAddClicked()</slot>
  <slot>slotRemoveClicked()</slot>
  <slot>slotEditClicked()</slot>
  <slot>slotSelectionChanged(QTreeWidgetItem*)</slot>
  <slot>slotItemDoubleClicked(QTreeWidgetItem*)</slot>
  <slot>slotPanChooseClicked()</slot>
  <slot>slotTiltChooseClicked()</slot>
  <slot>slotPanAutoDetectToggled(bool)</slot>
  <slot>slotTiltAutoDetectToggled(bool)</slot>
 </slots>
</ui>
