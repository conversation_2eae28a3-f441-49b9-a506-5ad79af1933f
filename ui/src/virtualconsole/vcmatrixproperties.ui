<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  vcmatrixproperties.ui

  Copyright (c) 2015 Massimo Call<PERSON>ari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>VCMatrixProperties</class>
 <widget class="QDialog" name="VCMatrixProperties">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>516</width>
    <height>489</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Animation widget properties</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../qlcui.qrc">
    <normaloff>:/animation.png</normaloff>:/animation.png</iconset>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="1" column="0">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>General</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="2" column="1">
          <widget class="QLineEdit" name="m_functionEdit">
           <property name="toolTip">
            <string>The function that this widget controls</string>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="m_nameLabel">
           <property name="text">
            <string>Widget name</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="m_functionLabel">
           <property name="text">
            <string>RGB Matrix Function</string>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QToolButton" name="m_detachFunction">
           <property name="toolTip">
            <string>Detach the matrix function attachment</string>
           </property>
           <property name="text">
            <string notr="true">...</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/detach.png</normaloff>:/detach.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QToolButton" name="m_attachFunction">
           <property name="toolTip">
            <string>Attach a function to this widget</string>
           </property>
           <property name="text">
            <string notr="true">...</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/attach.png</normaloff>:/attach.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="1" colspan="3">
          <widget class="QLineEdit" name="m_nameEdit">
           <property name="toolTip">
            <string>Text to display on the widget</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0" colspan="4">
          <widget class="QCheckBox" name="m_instantCheck">
           <property name="text">
            <string>Apply color and preset changes immediately</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0" colspan="4">
          <widget class="QCheckBox" name="m_labelCheck">
           <property name="text">
            <string>Show Label</string>
           </property>
          </widget>
         </item>
         <item row="5" column="0" colspan="4">
          <widget class="QCheckBox" name="m_sliderCheck">
           <property name="text">
            <string>Show Slider</string>
           </property>
          </widget>
         </item>
         <item row="6" column="0" colspan="4">
          <widget class="QCheckBox" name="m_startColorButtonCheck">
           <property name="text">
            <string>Show Start Color Button</string>
           </property>
          </widget>
         </item>
         <item row="7" column="0" colspan="4">
          <widget class="QCheckBox" name="m_endColorButtonCheck">
           <property name="text">
            <string>Show End Color Button</string>
           </property>
          </widget>
         </item>
         <item row="8" column="0" colspan="4">
          <widget class="QCheckBox" name="m_presetComboCheck">
           <property name="text">
            <string>Show Preset Combo</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QGroupBox" name="m_externalInputGroup">
         <property name="title">
          <string>Slider External Input</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="m_inputUniverseLabel">
            <property name="text">
             <string>Input universe</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1" colspan="2">
           <widget class="QLineEdit" name="m_inputUniverseEdit">
            <property name="toolTip">
             <string>The input universe that sends data to this widget</string>
            </property>
            <property name="readOnly">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="m_inputChannelLabel">
            <property name="text">
             <string>Input channel</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1" colspan="2">
           <widget class="QLineEdit" name="m_inputChannelEdit">
            <property name="toolTip">
             <string>The particular input channel within the input universe that sends data to this widget</string>
            </property>
            <property name="readOnly">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>298</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="2">
           <widget class="QPushButton" name="m_chooseInputButton">
            <property name="toolTip">
             <string>Choose an external input universe &amp; channel that this widget should listen to</string>
            </property>
            <property name="text">
             <string>Choose...</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QPushButton" name="m_autoDetectInputButton">
            <property name="toolTip">
             <string>When toggled, you can move an external slider/knob to assign it to the animation widget slider.</string>
            </property>
            <property name="text">
             <string>Auto Detect</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>Custom Controls</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QGridLayout" name="gridLayout_5">
         <item row="3" column="2">
          <widget class="QPushButton" name="m_addEndColorKnobsButton">
           <property name="text">
            <string> Add end color knobs</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/knob.png</normaloff>:/knob.png</iconset>
           </property>
          </widget>
         </item>
         <item row="0" column="1" rowspan="9">
          <widget class="QTreeWidget" name="m_controlsTree">
           <property name="rootIsDecorated">
            <bool>false</bool>
           </property>
           <property name="itemsExpandable">
            <bool>false</bool>
           </property>
           <column>
            <property name="text">
             <string>Type</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Value</string>
            </property>
           </column>
          </widget>
         </item>
         <item row="5" column="2">
          <widget class="QPushButton" name="m_addPresetButton">
           <property name="text">
            <string> Add preset</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/script.png</normaloff>:/script.png</iconset>
           </property>
          </widget>
         </item>
         <item row="6" column="2">
          <widget class="QPushButton" name="m_addTextButton">
           <property name="text">
            <string> Add text</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/fonts.png</normaloff>:/fonts.png</iconset>
           </property>
          </widget>
         </item>
         <item row="9" column="1" colspan="2">
          <layout class="QVBoxLayout" name="m_presetInputLayout"/>
         </item>
         <item row="4" column="2">
          <widget class="QPushButton" name="m_addEndColorResetButton">
           <property name="text">
            <string> Add end color reset</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/fileclose.png</normaloff>:/fileclose.png</iconset>
           </property>
          </widget>
         </item>
         <item row="7" column="2">
          <widget class="QPushButton" name="m_removeButton">
           <property name="text">
            <string> Remove</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/edit_remove.png</normaloff>:/edit_remove.png</iconset>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="m_addStartColorButton">
           <property name="text">
            <string> Add start color</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/color.png</normaloff>:/color.png</iconset>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QPushButton" name="m_addStartColorKnobsButton">
           <property name="text">
            <string> Add start color knobs</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/knob.png</normaloff>:/knob.png</iconset>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QPushButton" name="m_addEndColorButton">
           <property name="text">
            <string> Add end color</string>
           </property>
           <property name="icon">
            <iconset resource="../qlcui.qrc">
             <normaloff>:/color.png</normaloff>:/color.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../qlcui.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>VCMatrixProperties</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>VCMatrixProperties</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
