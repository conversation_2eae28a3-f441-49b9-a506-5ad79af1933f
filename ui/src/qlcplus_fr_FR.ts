<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="fr_FR">
<context>
    <name>AboutBox</name>
    <message>
        <location filename="aboutbox.ui" line="33"/>
        <source>About Q Light Controller Plus</source>
        <oldsource>About Q Light Controller</oldsource>
        <translation>À propos de Q Light Controller Plus</translation>
    </message>
    <message>
        <location filename="aboutbox.ui" line="88"/>
        <source>Contributors</source>
        <translation>Contributeurs</translation>
    </message>
    <message>
        <location filename="aboutbox.ui" line="135"/>
        <source>This application is licensed under the terms of the Apache 2.0 license.</source>
        <oldsource>This application is licensed under the terms of GNU GPL version 2.</oldsource>
        <translation>Cette application est sous licence Apache version 2.0.</translation>
    </message>
    <message>
        <location filename="aboutbox.cpp" line="42"/>
        <source>and contributors:</source>
        <translation>et contributeurs :</translation>
    </message>
    <message>
        <location filename="aboutbox.cpp" line="43"/>
        <source>Website: %1</source>
        <translation>Site internet : %1</translation>
    </message>
</context>
<context>
    <name>AddChannelsGroup</name>
    <message>
        <location filename="addchannelsgroup.ui" line="33"/>
        <source>Select Channels</source>
        <translation>Sélection des canaux</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="43"/>
        <source>Group Name</source>
        <translation>Nom du groupe</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="67"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="72"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="77"/>
        <source>Group</source>
        <translation>Groupe</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="87"/>
        <source>Apply changes to fixtures of the same type and mode</source>
        <oldsource>Apply changes to fixtures of the same type</oldsource>
        <translation>Appliquer à tous les appareils du même type et du même mode</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="94"/>
        <source>Collapse all</source>
        <translation>Tout plier</translation>
    </message>
    <message>
        <location filename="addchannelsgroup.ui" line="101"/>
        <source>Expand all</source>
        <translation>Tout déplier</translation>
    </message>
</context>
<context>
    <name>AddFixture</name>
    <message>
        <location filename="addfixture.ui" line="33"/>
        <source>Add fixture</source>
        <translation>Ajout d&apos;un appareil</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="183"/>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;ERROR: Address already used!&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;ERREUR: Cette addresse est déjà utilisée&#xa0;!&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="208"/>
        <source>Quick search</source>
        <oldsource>Quick search:</oldsource>
        <translation>Recherche rapide</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="200"/>
        <source>Fixture Model</source>
        <translation>Modèle de l&apos;appareil</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="45"/>
        <source>Fixture Properties</source>
        <translation>Propriétés de l&apos;appareil</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="51"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="58"/>
        <source>A friendly name for the new fixture</source>
        <translation>Un nom plus parlant pour le nouvel appareil</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="65"/>
        <source>Mode</source>
        <translation>Mode</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="72"/>
        <source>Selected fixture mode</source>
        <translation>Le mode de l&apos;appareil sélectionné</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="103"/>
        <source>Address</source>
        <translation>Adresse</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="112"/>
        <source>The starting address of the (first) added fixture</source>
        <translation>L&apos;adresse de départ du nouvel appareil (le premier)</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="125"/>
        <source>Address Tool</source>
        <translation>Outil d&apos;adressage</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="138"/>
        <source>Channels</source>
        <translation>Canaux</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="148"/>
        <source>Number of channels in the selected fixture</source>
        <translation>Le nombre de canaux de l&apos;appareil sélectionné</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="170"/>
        <source>List of channels in the selected fixture mode</source>
        <translation>La liste des canaux de l&apos;appareil dans le mode sélectionné</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="86"/>
        <source>Universe</source>
        <translation>Univers</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="221"/>
        <source>Multiple Fixtures</source>
        <translation>Nombre d&apos;appareil</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="227"/>
        <source>Quantity</source>
        <translation>Quantité</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="240"/>
        <source>Number of fixtures to add</source>
        <translation>Le nombre d&apos;appareils à ajouter</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="253"/>
        <source>Address gap</source>
        <translation>Saut d&apos;adresses</translation>
    </message>
    <message>
        <location filename="addfixture.ui" line="260"/>
        <source>Number of empty channels to leave between added fixtures</source>
        <translation>Le nombre de canaux vides à laisser entre chaque &apos;appareil</translation>
    </message>
    <message>
        <location filename="addfixture.cpp" line="104"/>
        <source>Fixtures found: %1</source>
        <translation>Appareils trouvés : %1</translation>
    </message>
    <message>
        <location filename="addfixture.cpp" line="625"/>
        <source>Dimmers</source>
        <translation>Gradateurs</translation>
    </message>
</context>
<context>
    <name>AddRGBPanel</name>
    <message>
        <location filename="addrgbpanel.ui" line="33"/>
        <source>Add RGB Panel</source>
        <translation>Ajout d&apos;un panneau RVB</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="43"/>
        <source>Panel properties</source>
        <translation>Propriétés du panneau</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="57"/>
        <source>Universe</source>
        <oldsource>Universe:</oldsource>
        <translation>Univers</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="64"/>
        <source>RGB Panel</source>
        <translation>Panneau RVB</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="71"/>
        <source>Name</source>
        <oldsource>Name:</oldsource>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="81"/>
        <source>Address</source>
        <oldsource>Address:</oldsource>
        <translation>Adresse</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="98"/>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;ERROR: Address already used!&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;ERREUR: Cette addresse est déjà utilisée&#xa0;!&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="198"/>
        <source>Size</source>
        <translation>Taille</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="212"/>
        <source>Columns</source>
        <oldsource>Columns:</oldsource>
        <translation>Colonnes</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="219"/>
        <source>Rows</source>
        <oldsource>Rows:</oldsource>
        <translation>Lignes</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="226"/>
        <source>Total pixels</source>
        <oldsource>Total pixels:</oldsource>
        <translation>Pixels totaux</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="299"/>
        <source>Direction</source>
        <translation>Direction</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="305"/>
        <source>Horizontal</source>
        <translation>Horizontale</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="315"/>
        <source>Vertical</source>
        <translation>Verticale</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="327"/>
        <source>Orientation</source>
        <translation>Orientation</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="333"/>
        <source>Top-Right</source>
        <translation>Supérieur droit</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="340"/>
        <source>Top-Left</source>
        <translation>Supérieur gauche</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="350"/>
        <source>Bottom-Left</source>
        <translation>Inférieur gauche</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="357"/>
        <source>Bottom-Right</source>
        <translation>Inférieur droit</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="120"/>
        <source>Physical</source>
        <translation>Données physiques</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="137"/>
        <source>Width</source>
        <oldsource>Width:</oldsource>
        <translation>Largeur</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="144"/>
        <location filename="addrgbpanel.ui" line="167"/>
        <source>mm</source>
        <translation> mm</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="160"/>
        <source>Height</source>
        <oldsource>Height:</oldsource>
        <translation>Hauteur</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="105"/>
        <source>Components</source>
        <translation>Composantes</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="273"/>
        <source>Displacement</source>
        <translation>Déplacement</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="279"/>
        <source>Snake</source>
        <translation>Serpent</translation>
    </message>
    <message>
        <location filename="addrgbpanel.ui" line="289"/>
        <source>Zig Zag</source>
        <translation>Zigzag</translation>
    </message>
</context>
<context>
    <name>AddVCButtonMatrix</name>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="33"/>
        <source>Add Button Matrix</source>
        <translation>Ajout d&apos;une matrice de boutons</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="40"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="45"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="53"/>
        <source>Add functions to be attached to the buttons in the matrix</source>
        <translation>Ajouter des fonctions à attacher aux boutons de la matrice</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="73"/>
        <source>Remove selected functions from the list of functions to attach</source>
        <translation>Enlever les fonctions sélectionnées de la liste des fonctions à attacher</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="106"/>
        <source>Dimensions</source>
        <translation>Dimensions</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="112"/>
        <source>Horizontal button count</source>
        <translation>Boutons horizontaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="119"/>
        <source>Number of buttons per horizontal row</source>
        <translation>Le nombre de boutons par ligne</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="132"/>
        <source>Button size</source>
        <translation>Taille des boutons</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="139"/>
        <source>Created buttons&apos; size</source>
        <translation>La taille des boutons à créer</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="142"/>
        <source> px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="155"/>
        <source>Vertical button count</source>
        <translation>Boutons verticaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="162"/>
        <source>Number of buttons per vertical column</source>
        <translation>Le nombre de boutons par colonne</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="172"/>
        <source>Allocation</source>
        <translation>Allocations</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="179"/>
        <source>Functions / Buttons</source>
        <translation>Le nombre total de fonctions attachées à un bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="192"/>
        <source>Frame</source>
        <translation>Cadre</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="198"/>
        <source>Place the buttons inside a normal frame</source>
        <translation>Placer les boutons dans un cadre normal</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="201"/>
        <source>Normal</source>
        <translation>Normal</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="208"/>
        <source>Place the buttons inside a frame that ensures that only one of them is pressed at a time</source>
        <translation>Placer les boutons dans un cadre permettant l&apos;activation d&apos;un seul bouton à la fois</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcbuttonmatrix.ui" line="211"/>
        <source>Solo</source>
        <translation>Solo</translation>
    </message>
</context>
<context>
    <name>AddVCSliderMatrix</name>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="33"/>
        <source>Add Slider Matrix</source>
        <translation>Ajout d&apos;une matrice de faders</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="39"/>
        <source>Sliders</source>
        <translation>Faders</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="45"/>
        <source>Amount</source>
        <translation>Quantité</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="52"/>
        <source>Number of sliders to create</source>
        <translation>Le nombre de faders à créer</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="62"/>
        <source>Height</source>
        <translation>Hauteur</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="69"/>
        <source>Vertical height of each slider</source>
        <translation>La hauteur de chaque fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="72"/>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="95"/>
        <source>px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="virtualconsole/addvcslidermatrix.ui" line="88"/>
        <source>Width</source>
        <translation>Largeur</translation>
    </message>
</context>
<context>
    <name>AddressTool</name>
    <message>
        <location filename="addresstool.ui" line="33"/>
        <source>Address Tool</source>
        <translation>Outil d&apos;adressage</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="41"/>
        <source>Reverse vertically</source>
        <translation>Miroir vertical</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="54"/>
        <source>Colour</source>
        <oldsource>Color</oldsource>
        <translation>Couleur</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="70"/>
        <source>Address</source>
        <translation>Adresse</translation>
    </message>
    <message>
        <location filename="addresstool.ui" line="117"/>
        <source>Reverse Horizontally</source>
        <translation>Miroir horizontal</translation>
    </message>
</context>
<context>
    <name>App</name>
    <message>
        <location filename="app.cpp" line="371"/>
        <source>Cannot exit in Operate mode</source>
        <translation>Impossible de quitter en mode Production</translation>
    </message>
    <message>
        <location filename="app.cpp" line="372"/>
        <source>You must switch back to Design mode to close the application.</source>
        <translation>Vous devez basculer vers le mode Création pour quitter l&apos;application.</translation>
    </message>
    <message>
        <location filename="app.cpp" line="380"/>
        <source>Close</source>
        <translation>Quitter</translation>
    </message>
    <message>
        <location filename="app.cpp" line="380"/>
        <source>Do you wish to save the current workspace before closing the application?</source>
        <translation>Voulez-vous enregistrer le projet en cours avant de quitter l&apos;application&#xa0;?</translation>
    </message>
    <message>
        <location filename="app.cpp" line="437"/>
        <source>Starting Q Light Controller Plus</source>
        <oldsource>Starting Q Light Controller</oldsource>
        <translation>Démarrage de Q Light Controller Plus</translation>
    </message>
    <message>
        <location filename="app.cpp" line="523"/>
        <source> - New Workspace</source>
        <translation> - Nouveau projet</translation>
    </message>
    <message>
        <location filename="app.cpp" line="591"/>
        <source>Switch to Design Mode</source>
        <translation>Basculer vers le mode Création</translation>
    </message>
    <message>
        <location filename="app.cpp" line="592"/>
        <source>There are still running functions.
Really stop them and switch back to Design mode?</source>
        <translation>Des fonctions sont encore en cours d&apos;exécution.
Voulez-vous vraiment les arrêter et basculer vers le mode Création&#xa0;?</translation>
    </message>
    <message>
        <location filename="app.cpp" line="627"/>
        <source>Design</source>
        <translation>Création</translation>
    </message>
    <message>
        <location filename="app.cpp" line="628"/>
        <source>Switch to design mode</source>
        <translation>Basculer vers le mode Création</translation>
    </message>
    <message>
        <location filename="app.cpp" line="639"/>
        <source>Operate</source>
        <translation>Production</translation>
    </message>
    <message>
        <location filename="app.cpp" line="640"/>
        <location filename="app.cpp" line="668"/>
        <source>Switch to operate mode</source>
        <translation>Basculer vers le mode Production</translation>
    </message>
    <message>
        <location filename="app.cpp" line="651"/>
        <source>&amp;New</source>
        <translation>&amp;Nouveau</translation>
    </message>
    <message>
        <location filename="app.cpp" line="652"/>
        <source>CTRL+N</source>
        <comment>File|New</comment>
        <translation></translation>
    </message>
    <message>
        <location filename="app.cpp" line="655"/>
        <source>&amp;Open</source>
        <translation>&amp;Ouvrir</translation>
    </message>
    <message>
        <location filename="app.cpp" line="656"/>
        <source>CTRL+O</source>
        <comment>File|Open</comment>
        <translation></translation>
    </message>
    <message>
        <location filename="app.cpp" line="659"/>
        <source>&amp;Save</source>
        <translation>Enregi&amp;strer</translation>
    </message>
    <message>
        <location filename="app.cpp" line="660"/>
        <source>CTRL+S</source>
        <comment>File|Save</comment>
        <translation></translation>
    </message>
    <message>
        <location filename="app.cpp" line="663"/>
        <source>Save &amp;As...</source>
        <translation>Enregistrer sous (&amp;A) ...</translation>
    </message>
    <message>
        <location filename="app.cpp" line="667"/>
        <source>&amp;Operate</source>
        <translation>&amp;Produire</translation>
    </message>
    <message>
        <location filename="app.cpp" line="672"/>
        <source>&amp;Monitor</source>
        <translation>&amp;Moniteur</translation>
    </message>
    <message>
        <location filename="app.cpp" line="679"/>
        <source>Toggle &amp;Blackout</source>
        <translation>&amp;Blackout</translation>
    </message>
    <message>
        <location filename="app.cpp" line="669"/>
        <source>CTRL+F12</source>
        <comment>Control|Toggle operate/design mode</comment>
        <translation></translation>
    </message>
    <message>
        <location filename="app.cpp" line="673"/>
        <source>CTRL+M</source>
        <comment>Control|Monitor</comment>
        <translation></translation>
    </message>
    <message>
        <location filename="app.cpp" line="684"/>
        <source>Live edit a function</source>
        <translation>Éditer une fonction en direct</translation>
    </message>
    <message>
        <location filename="app.cpp" line="724"/>
        <source>Toggle Full Screen</source>
        <translation>Plein écran</translation>
    </message>
    <message>
        <location filename="app.cpp" line="726"/>
        <source>CTRL+F11</source>
        <comment>Control|Toggle Full Screen</comment>
        <translation></translation>
    </message>
    <message>
        <location filename="app.cpp" line="730"/>
        <source>&amp;Index</source>
        <translation>A&amp;ide</translation>
    </message>
    <message>
        <location filename="app.cpp" line="731"/>
        <source>SHIFT+F1</source>
        <comment>Help|Index</comment>
        <translation></translation>
    </message>
    <message>
        <location filename="app.cpp" line="734"/>
        <source>&amp;About QLC+</source>
        <oldsource>&amp;About QLC</oldsource>
        <translation>&amp;À propos de QLC+</translation>
    </message>
    <message>
        <location filename="app.cpp" line="282"/>
        <source>Fixtures</source>
        <translation>Appareils</translation>
    </message>
    <message>
        <location filename="app.cpp" line="284"/>
        <source>Functions</source>
        <translation>Fonctions</translation>
    </message>
    <message>
        <location filename="app.cpp" line="286"/>
        <source>Shows</source>
        <translation>Shows</translation>
    </message>
    <message>
        <location filename="app.cpp" line="288"/>
        <source>Virtual Console</source>
        <translation>Console virtuelle</translation>
    </message>
    <message>
        <location filename="app.cpp" line="290"/>
        <source>Simple Desk</source>
        <translation>Pupitre traditionnel</translation>
    </message>
    <message>
        <location filename="app.cpp" line="292"/>
        <source>Inputs/Outputs</source>
        <translation>Entrées/Sorties</translation>
    </message>
    <message>
        <location filename="app.cpp" line="394"/>
        <source>Close the application?</source>
        <translation>Quitter QLCPlus&#xa0;?</translation>
    </message>
    <message>
        <location filename="app.cpp" line="395"/>
        <source>Do you wish to close the application?</source>
        <translation>Voulez-vous quitter l&apos;application&#xa0;?</translation>
    </message>
    <message>
        <location filename="app.cpp" line="574"/>
        <source>Exit</source>
        <translation>Quitter</translation>
    </message>
    <message>
        <location filename="app.cpp" line="676"/>
        <source>Address Tool</source>
        <translation>Outil d&apos;adressage</translation>
    </message>
    <message>
        <location filename="app.cpp" line="688"/>
        <source>Toggle Virtual Console Live edit</source>
        <translation>Basculer l&apos;édition live de la console virtuelle</translation>
    </message>
    <message>
        <location filename="app.cpp" line="693"/>
        <source>Dump DMX values to a function</source>
        <translation>Capturer les valeurs DMX vers une fonction</translation>
    </message>
    <message>
        <location filename="app.cpp" line="694"/>
        <source>CTRL+D</source>
        <comment>Control|Dump DMX</comment>
        <translation>CTRL+D</translation>
    </message>
    <message>
        <location filename="app.cpp" line="697"/>
        <source>Stop ALL functions!</source>
        <translation>Arrêter TOUTES les fonctions&#xa0;!</translation>
    </message>
    <message>
        <location filename="app.cpp" line="702"/>
        <source>Fade 1 second and stop</source>
        <translation>Fondu d&apos;1 s en sortie</translation>
    </message>
    <message>
        <location filename="app.cpp" line="707"/>
        <source>Fade 5 seconds and stop</source>
        <translation>Fondu de 5 s en sortie</translation>
    </message>
    <message>
        <location filename="app.cpp" line="712"/>
        <source>Fade 10 second and stop</source>
        <translation>Fondu de 10 s en sortie</translation>
    </message>
    <message>
        <location filename="app.cpp" line="717"/>
        <source>Fade 30 second and stop</source>
        <translation>Fondu de 30 s en sortie</translation>
    </message>
    <message>
        <location filename="app.cpp" line="739"/>
        <source>Quit QLC+</source>
        <translation>Quitter QLC+</translation>
    </message>
    <message>
        <location filename="app.cpp" line="747"/>
        <source>Workspace</source>
        <translation>Projet</translation>
    </message>
    <message>
        <location filename="app.cpp" line="805"/>
        <source>Unable to read from file</source>
        <translation>Impossible de lire le fichier</translation>
    </message>
    <message>
        <location filename="app.cpp" line="808"/>
        <source>Unable to write to file</source>
        <translation>Impossible d&apos;écrire dans le fichier</translation>
    </message>
    <message>
        <location filename="app.cpp" line="811"/>
        <source>A fatal error occurred</source>
        <translatorcomment>...la vilaine!</translatorcomment>
        <translation>Une erreur fatale est survenue</translation>
    </message>
    <message>
        <location filename="app.cpp" line="814"/>
        <source>Unable to access resource</source>
        <translation>Impossible d&apos;accéder à la ressource</translation>
    </message>
    <message>
        <location filename="app.cpp" line="817"/>
        <source>Unable to open file for reading or writing</source>
        <translation>Impossible d&apos;ouvrir le fichier en lectue ou écriture</translation>
    </message>
    <message>
        <location filename="app.cpp" line="820"/>
        <source>Operation was aborted</source>
        <translation>L&apos;opération a été abandonnée</translation>
    </message>
    <message>
        <location filename="app.cpp" line="823"/>
        <source>Operation timed out</source>
        <translation>L&apos;opération a pris trop de temps</translation>
    </message>
    <message>
        <location filename="app.cpp" line="827"/>
        <source>An unspecified error has occurred. Nice.</source>
        <translation>Une erreur indeterminée est survenue, sympa.</translation>
    </message>
    <message>
        <location filename="app.cpp" line="831"/>
        <source>File error</source>
        <translation>Erreur de fichier</translation>
    </message>
    <message>
        <location filename="app.cpp" line="925"/>
        <location filename="app.cpp" line="941"/>
        <location filename="app.cpp" line="1257"/>
        <source>Do you wish to save the current workspace?
Changes will be lost if you don&apos;t save them.</source>
        <translatorcomment>Tiens tiens...perspicace!</translatorcomment>
        <translation>Voulez-vous enregistrer le projet actuel&#xa0;?
Les changements seront perdus si vous ne les sauvegardez pas.</translation>
    </message>
    <message>
        <location filename="app.cpp" line="927"/>
        <source>New Workspace</source>
        <translation>Nouveau projet</translation>
    </message>
    <message>
        <location filename="app.cpp" line="943"/>
        <location filename="app.cpp" line="951"/>
        <location filename="app.cpp" line="1259"/>
        <source>Open Workspace</source>
        <translation>Ouvrir un projet</translation>
    </message>
    <message>
        <location filename="app.cpp" line="959"/>
        <location filename="app.cpp" line="1042"/>
        <source>Workspaces (*%1)</source>
        <translation>Projets (*%1)</translation>
    </message>
    <message>
        <location filename="app.cpp" line="961"/>
        <location filename="app.cpp" line="1044"/>
        <source>All Files (*.*)</source>
        <translation>Tous les fichiers (*.*)</translation>
    </message>
    <message>
        <location filename="app.cpp" line="963"/>
        <location filename="app.cpp" line="1046"/>
        <source>All Files (*)</source>
        <translation>Tous les fichiers (*)</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1036"/>
        <source>Save Workspace As</source>
        <translation>Enregistrer le projet sous</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1250"/>
        <source>Error</source>
        <translation>Erreur</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1251"/>
        <source>File not found!
The selected file has been moved or deleted.</source>
        <translation>Fichier introuvable&#xa0;
Celui-ci a dû être déplacé ou effacé.</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1427"/>
        <source>Warning</source>
        <translation>Attention</translation>
    </message>
    <message>
        <location filename="app.cpp" line="1428"/>
        <source>Some errors occurred while loading the project:</source>
        <translation>Des erreurs sont survenues lors du chargement du projet :</translation>
    </message>
</context>
<context>
    <name>AssignHotKey</name>
    <message>
        <location filename="assignhotkey.ui" line="33"/>
        <source>Assign a key combination to button</source>
        <translation>Assigner un raccourci clavier au bouton</translation>
    </message>
    <message>
        <location filename="assignhotkey.ui" line="42"/>
        <source>Key combination</source>
        <translation>Raccourci clavier</translation>
    </message>
    <message>
        <location filename="assignhotkey.ui" line="62"/>
        <source>Close automatically on key press</source>
        <translation>Fermer la fenêtre automatiquement sur appui touche</translation>
    </message>
    <message>
        <location filename="assignhotkey.cpp" line="50"/>
        <source>Assign Key</source>
        <translation>Assigner une touche</translation>
    </message>
    <message>
        <location filename="assignhotkey.cpp" line="51"/>
        <source>Hit the key combination that you wish to assign. You may hit either a single key or a combination using %1, %2, and %3.</source>
        <translation>Presser le raccourci clavier que vous voulez assigner. Vous pouvez presser une seule touche ou une combinaison utilisant %1, %2 et %3.</translation>
    </message>
</context>
<context>
    <name>AudioEditor</name>
    <message>
        <location filename="audioeditor.ui" line="33"/>
        <source>Audio editor</source>
        <translation>Éditeur audio</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="41"/>
        <source>Playback mode</source>
        <translation>Mode de lecture</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="47"/>
        <source>Single shot</source>
        <translation>Unique</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="57"/>
        <source>Loop</source>
        <translation>Boucle</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="209"/>
        <source>Bitrate</source>
        <oldsource>Bitrate:</oldsource>
        <translation>Débit</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="226"/>
        <source>Play the audio file</source>
        <translation>Jouer le fichier audio</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="300"/>
        <source>Duration</source>
        <oldsource>Duration:</oldsource>
        <translation>Durée</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="279"/>
        <source>File name</source>
        <translation>Nom du fichier</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="113"/>
        <source>Audio name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="293"/>
        <source>Sample rate</source>
        <oldsource>Sample rate:</oldsource>
        <translation>Échantillonnage</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="173"/>
        <source>Name of the function being edited</source>
        <translation>Le nom de la fonction en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="127"/>
        <source>Audio device</source>
        <translation>Périphérique audio</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="74"/>
        <source>Channels</source>
        <oldsource>Channels:</oldsource>
        <translation>Canaux</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="246"/>
        <source>Show/Hide speed dial window</source>
        <translation>Afficher/Masquer la fenêtre de réglage des vitesses</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="314"/>
        <source>Fade in</source>
        <oldsource>Fade in:</oldsource>
        <translation>Fondu en ouverture</translation>
    </message>
    <message>
        <location filename="audioeditor.ui" line="134"/>
        <source>Fade out</source>
        <oldsource>Fade out:</oldsource>
        <translation>Fondu en fermeture</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="84"/>
        <source>Default device</source>
        <translation>Périphérique par défaut</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="140"/>
        <source>Open Audio File</source>
        <translation>Ouvrir un fichier audio</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="148"/>
        <source>Audio Files (%1)</source>
        <translation>Fichiers audio (%1)</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="150"/>
        <source>All Files (*.*)</source>
        <translation>Tous les fichiers (*.*)</translation>
    </message>
    <message>
        <location filename="audioeditor.cpp" line="152"/>
        <source>All Files (*)</source>
        <translation>Tous les fichiers (*)</translation>
    </message>
</context>
<context>
    <name>AudioItem</name>
    <message>
        <location filename="showmanager/audioitem.cpp" line="55"/>
        <location filename="showmanager/audioitem.cpp" line="196"/>
        <source>Preview Left Channel</source>
        <translation>Prévisualiser le canal gauche</translation>
    </message>
    <message>
        <location filename="showmanager/audioitem.cpp" line="59"/>
        <source>Preview Right Channel</source>
        <translation>Prévisualiser le canal droit</translation>
    </message>
    <message>
        <location filename="showmanager/audioitem.cpp" line="63"/>
        <source>Preview Stereo Channels</source>
        <translation>Prévisualiser les deux canaux (stéréo)</translation>
    </message>
    <message>
        <location filename="showmanager/audioitem.cpp" line="192"/>
        <source>Preview Mono</source>
        <translation>Prévisualiser le canal (mono)</translation>
    </message>
</context>
<context>
    <name>AudioTriggersConfiguration</name>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="33"/>
        <source>Audio Triggers Configuration</source>
        <translation>Configuration du déclencheur audio</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="116"/>
        <source>Number of spectrum bars:</source>
        <translation>Nombre de bandes :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="59"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="43"/>
        <source>Triggers</source>
        <translation>Déclencheurs</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="51"/>
        <source>Widget name</source>
        <translation>Nom du widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="64"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="69"/>
        <source>Assign</source>
        <translation>Assignation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="74"/>
        <source>Info</source>
        <translation>Infos</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="79"/>
        <source>Disable threshold</source>
        <translation>Seuil de désactivation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="84"/>
        <source>Enable threshold</source>
        <translation>Seuil d&apos;activation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="89"/>
        <source>Divisor</source>
        <translation>Diviseur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.ui" line="132"/>
        <source>Input</source>
        <translation>Entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="115"/>
        <source>None</source>
        <translation>Aucun</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="116"/>
        <source>DMX</source>
        <translation>DMX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="117"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="118"/>
        <source>VC Widget</source>
        <translation>Widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="131"/>
        <source>%1 channels</source>
        <translation>%1 canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="147"/>
        <source>No function</source>
        <translation>Aucune fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="165"/>
        <source>No widget</source>
        <translation>Aucun widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="171"/>
        <source>Not assigned</source>
        <translation>Non assigné</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="225"/>
        <source>Volume Bar</source>
        <translation>Barre de volume</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcaudiotriggersproperties.cpp" line="234"/>
        <source>#%1 (%2Hz - %3Hz)</source>
        <translation></translation>
    </message>
</context>
<context>
    <name>ChannelModifierEditor</name>
    <message>
        <location filename="channelmodifiereditor.ui" line="33"/>
        <source>Channel Modifier Editor</source>
        <translation>Éditeur de modificateur de canal</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="50"/>
        <source>Modified DMX value</source>
        <translation>Valeur DMX modifiée</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="63"/>
        <source>Original DMX value</source>
        <translation>Valeur DMX originale</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="81"/>
        <source>Remove the selected handler</source>
        <translatorcomment>???</translatorcomment>
        <translation>Supprimer le point sélectionné</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="98"/>
        <source>Overwrite the current template</source>
        <translation>Écraser le modèle actuel</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="115"/>
        <source>Add a new handler</source>
        <translatorcomment>???</translatorcomment>
        <translation>Ajouter un nouveau point</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="149"/>
        <source>Templates</source>
        <translation>Modèles</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="161"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="168"/>
        <source>New Template</source>
        <translation>Nouveau modèle</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.ui" line="181"/>
        <source>Unset Modifier</source>
        <translation>Désactiver le modificateur</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.cpp" line="183"/>
        <source>Error</source>
        <translation>Erreur</translation>
    </message>
    <message>
        <location filename="channelmodifiereditor.cpp" line="184"/>
        <source>You are trying to overwrite a system template! Please choose another name and the template will be saved in your channel modifier&apos;s user folder.</source>
        <translation>Vous tentez d&apos;écraser un modèle système&#xa0;! Merci de choisir un nom différent et le modèle sera enregistré dans votre dossier personnel.</translation>
    </message>
</context>
<context>
    <name>ChannelsSelection</name>
    <message>
        <location filename="channelsselection.ui" line="33"/>
        <source>Channels selection</source>
        <oldsource>Channels Fade Configuration</oldsource>
        <translation>Sélection des canaux</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="47"/>
        <location filename="channelsselection.cpp" line="50"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="52"/>
        <location filename="channelsselection.cpp" line="50"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="62"/>
        <source>Apply changes to fixtures of the same type</source>
        <translation>Appliquer à tous les appareils du même type</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="69"/>
        <source>Collapse all</source>
        <translation>Tout plier</translation>
    </message>
    <message>
        <location filename="channelsselection.ui" line="76"/>
        <source>Expand all</source>
        <translation>Tout déplier</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="54"/>
        <source>Selected</source>
        <translation>Sélectionné</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="58"/>
        <source>Channel properties configuration</source>
        <translation>Configuration des propriétés du canal</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="60"/>
        <source>Can fade</source>
        <translation>Graduable</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="60"/>
        <source>Behaviour</source>
        <translation>Comportement</translation>
    </message>
    <message>
        <location filename="channelsselection.cpp" line="60"/>
        <source>Modifier</source>
        <translation>Modificateur</translation>
    </message>
</context>
<context>
    <name>ChaserEditor</name>
    <message>
        <location filename="chasereditor.ui" line="33"/>
        <source>Chaser editor</source>
        <translation>Éditeur de chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="142"/>
        <source>Chaser name</source>
        <translation>Nom du chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="155"/>
        <source>Name of the chaser being edited</source>
        <translation>Le nom du chaser en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="79"/>
        <source>Step</source>
        <translation>Pas</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="84"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="89"/>
        <location filename="chasereditor.cpp" line="983"/>
        <source>Fade In</source>
        <translation>Montée</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="99"/>
        <location filename="chasereditor.cpp" line="984"/>
        <source>Fade Out</source>
        <translation>Descente</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="104"/>
        <source>Duration</source>
        <translation>Durée du pas</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="741"/>
        <source>Add step(s) to the current position</source>
        <translation>Ajouter un ou plusieurs pas à la position actuelle</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="595"/>
        <source>Show/Hide speed dial window</source>
        <translation>Afficher/Masquer la fenêtre de réglage des vitesses</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="94"/>
        <location filename="chasereditor.cpp" line="985"/>
        <source>Hold</source>
        <translation>Maintien</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="109"/>
        <source>Notes</source>
        <translation>Notes</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="228"/>
        <source>Switch direction at both ends</source>
        <translation>Alterner l&apos;ordre de lecture dans les deux directions</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="238"/>
        <source>Execute steps in random order</source>
        <translation>Lire les pas dans un ordre aléatoire</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="241"/>
        <source>Random</source>
        <translation>Aléatoire</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="326"/>
        <source>Fade In Speed</source>
        <translation>Vitesse de montée</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="353"/>
        <source>All steps have common fade in speed set by the chaser</source>
        <translation>Tous les pas ont une vitesse de montée commune définie par le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="356"/>
        <location filename="chasereditor.ui" line="431"/>
        <location filename="chasereditor.ui" line="509"/>
        <source>Common</source>
        <translation>Commune</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="369"/>
        <source>Each step has its own fade in speed set by the chaser</source>
        <translation>Chaque pas a sa propre vitesse de montée définie par le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="372"/>
        <location filename="chasereditor.ui" line="447"/>
        <location filename="chasereditor.ui" line="528"/>
        <source>Per Step</source>
        <translation>Par pas</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="385"/>
        <source>Use each function&apos;s own fade in speed</source>
        <translation>Chaque pas utilise la vitesse de montée de sa fontion</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="388"/>
        <location filename="chasereditor.ui" line="463"/>
        <source>Default</source>
        <translation>Défaut</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="401"/>
        <source>Fade Out Speed</source>
        <translation>Vitesse de descente</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="428"/>
        <source>All steps have common fade out speed set by the chaser</source>
        <translation>Tous les pas ont une vitesse de descente commune définie par le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="444"/>
        <source>Each step has its own fade out speed set by the chaser</source>
        <translation>Chaque pas a sa propre vitesse de descente définie par le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="460"/>
        <source>Use each function&apos;s own fade out speed</source>
        <translation>Chaque pas utilise la vitesse de descente de sa fontion</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="476"/>
        <source>Step Duration</source>
        <translation>Durée du pas</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="506"/>
        <source>All steps have common step duration set by the chaser</source>
        <translation>Tous les pas ont une durée commune définie par le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="525"/>
        <source>Each step has its own duration set by the chaser</source>
        <translation>Chaque pas a sa propre durée définie par le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="622"/>
        <source>See what the chaser does when it is run</source>
        <translation>Prévisualiser le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="639"/>
        <source>Stop the chaser if running</source>
        <translation>Arrêter le chaser</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="673"/>
        <source>Skip to the next step</source>
        <translation>Passer au pas suivant</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="782"/>
        <source>Shuffle steps</source>
        <translation>Pas au hasard</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="656"/>
        <source>Skip to the previous step</source>
        <translation>Passer au pas précédent</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="117"/>
        <source>Remove the selected step</source>
        <translation>Enlever le pas sélectionné</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="758"/>
        <source>Raise the selected step once</source>
        <translation>Monter le pas sélectionné</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="556"/>
        <source>Lower the selected step once</source>
        <translation>Descendre le pas sélectionné</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="166"/>
        <source>Run Order</source>
        <translation>Ordre de lecture</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="193"/>
        <source>Run through over and over again</source>
        <translation>Répéter indéfiniment</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="196"/>
        <source>Loop</source>
        <translation>Boucle</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="212"/>
        <source>Run through once and stop</source>
        <translation>Lire une seule fois</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="215"/>
        <source>Single Shot</source>
        <translation>Unique</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="231"/>
        <source>Ping Pong</source>
        <translation>Ping pong</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="251"/>
        <source>Direction</source>
        <translation>Direction</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="278"/>
        <source>Start from the first step</source>
        <translation>Commencer à partir du premier pas</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="281"/>
        <source>Forward</source>
        <translation>Avant</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="297"/>
        <source>Start from the last step</source>
        <translation>Commencer à partir du dernier pas</translation>
    </message>
    <message>
        <location filename="chasereditor.ui" line="300"/>
        <source>Backward</source>
        <translation>Arrière</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="75"/>
        <source>Cut</source>
        <translation>Couper</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="80"/>
        <source>Copy</source>
        <translation>Copier</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="85"/>
        <source>Paste</source>
        <translation>Coller</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="754"/>
        <source>Paste error</source>
        <translation>Erreur de collage</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="754"/>
        <source>Trying to paste on an incompatible Scene. Operation canceled.</source>
        <translation>La scène vers laquelle coller est incompatible. Opération annulée.</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="986"/>
        <source>Common Fade In</source>
        <translation>Montée commune</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="987"/>
        <source>Common Fade Out</source>
        <translation>Descente commune</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="988"/>
        <source>Common Hold</source>
        <translation>Maintien commun</translation>
    </message>
    <message>
        <location filename="chasereditor.cpp" line="1004"/>
        <source>Multiple Steps</source>
        <translation>Pas multiples</translation>
    </message>
</context>
<context>
    <name>CollectionEditor</name>
    <message>
        <location filename="collectioneditor.ui" line="33"/>
        <source>Collection editor</source>
        <translation>Éditeur de collection</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="39"/>
        <source>Move the selected functions down</source>
        <translation>Déplacer les fonctions sélectionnées vers le bas</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="56"/>
        <source>Move the selected functions up</source>
        <translation>Déplacer les fonctions sélectionnées vers le haut</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="79"/>
        <source>Collection name</source>
        <translation>Nom de la collection</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="86"/>
        <source>Name of the function being edited</source>
        <translation>Le nom de la collection en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="146"/>
        <source>See what the Collection does when it is run</source>
        <translation>Prévisualiser la collection</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="188"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="93"/>
        <source>Add function(s) to the collection</source>
        <translation>Ajouter une ou plusieurs fonctions à la collection</translation>
    </message>
    <message>
        <location filename="collectioneditor.ui" line="113"/>
        <source>Remove the selected function</source>
        <translation>Enlever la fonction sélectionnée</translation>
    </message>
</context>
<context>
    <name>ConsoleChannel</name>
    <message>
        <location filename="consolechannel.cpp" line="163"/>
        <source>Intensity</source>
        <translation>Intensité</translation>
    </message>
    <message>
        <location filename="consolechannel.cpp" line="313"/>
        <source>Reset this channel</source>
        <translation>Réinitialiser ce canal</translation>
    </message>
</context>
<context>
    <name>CreateFixtureGroup</name>
    <message>
        <location filename="createfixturegroup.ui" line="33"/>
        <source>Create Fixture Group</source>
        <translation>Création d&apos;un groupe d&apos;appareils</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="39"/>
        <source>Group name</source>
        <translation>Nom du groupe</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="51"/>
        <source>Initial size</source>
        <translation>Agencement des appareils</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="57"/>
        <source>Width</source>
        <translation>Largeur</translation>
    </message>
    <message>
        <location filename="createfixturegroup.ui" line="74"/>
        <source>Height</source>
        <translation>Hauteur</translation>
    </message>
</context>
<context>
    <name>CueStackModel</name>
    <message>
        <location filename="cuestackmodel.cpp" line="144"/>
        <source>Number</source>
        <translation>Numéro</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="146"/>
        <source>Fade In</source>
        <translation>Montée</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="148"/>
        <source>Fade Out</source>
        <translation>Descente</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="150"/>
        <source>Duration</source>
        <translation>Durée</translation>
    </message>
    <message>
        <location filename="cuestackmodel.cpp" line="152"/>
        <source>Cue</source>
        <translation>Mémoire</translation>
    </message>
</context>
<context>
    <name>DmxDumpFactory</name>
    <message>
        <location filename="dmxdumpfactory.ui" line="33"/>
        <source>Dump DMX values</source>
        <translation>Capture de valeurs DMX</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="43"/>
        <source>Dump only non-zero values</source>
        <translation>Valeurs différentes de 0 uniquement</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="94"/>
        <source>Dump options</source>
        <translation>Options de capture</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="177"/>
        <source>Dump selected channels</source>
        <oldsource>Dump selected DMX values</oldsource>
        <translation>Capturer les canaux sélectionnés</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="121"/>
        <source>Add to:</source>
        <translation>Ajouter à :</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="72"/>
        <source>Select an existing Scene to overwrite</source>
        <translation>Choisir une scène à écraser</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="104"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="128"/>
        <source>Chaser</source>
        <translation>Chaser</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="138"/>
        <source>Button</source>
        <translation>Bouton</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="145"/>
        <source>Slider</source>
        <translation>Fader</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.ui" line="62"/>
        <source>Scene name:</source>
        <translation>Nom de la scène :</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.cpp" line="74"/>
        <source>Dump all channels (%1 Universes, %2 Fixtures, %3 Channels)</source>
        <oldsource>Dump all DMX values (%1 Universes, %2 Fixtures, %3 Channels)</oldsource>
        <translation>Capturer tous les canaux (%1 univers, %2 appareils, %3 canaux)</translation>
    </message>
    <message>
        <location filename="dmxdumpfactory.cpp" line="77"/>
        <source>New Scene From Live %1</source>
        <translation>Nouvelle scène live %1</translation>
    </message>
</context>
<context>
    <name>DocBrowser</name>
    <message>
        <location filename="docbrowser.cpp" line="105"/>
        <source>%1 - Document Browser</source>
        <translation>%1 - Explorateur de document</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="127"/>
        <source>Backward</source>
        <translation>Arrière</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="128"/>
        <source>Forward</source>
        <translation>Avant</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="129"/>
        <source>Index</source>
        <translation>Aide</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="130"/>
        <source>About Qt</source>
        <translation>À propos de Qt</translation>
    </message>
    <message>
        <location filename="docbrowser.cpp" line="131"/>
        <source>Close this window</source>
        <translation>Fermer cette fenêtre</translation>
    </message>
</context>
<context>
    <name>EFXEditor</name>
    <message>
        <location filename="efxeditor.ui" line="33"/>
        <source>EFX Editor</source>
        <translation>Éditeur EFX</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="46"/>
        <source>General</source>
        <translatorcomment>...mon Caporal!</translatorcomment>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="715"/>
        <source>EFX name</source>
        <translation>Nom de l&apos;EFX</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="722"/>
        <source>The name of the function being edited</source>
        <translation>Le nom de la fonction en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="182"/>
        <source>Step</source>
        <translation>Pas</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="187"/>
        <source>Fixture</source>
        <translation>Appareil</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="197"/>
        <source>Reverse</source>
        <translation>Inversé</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="101"/>
        <source>Fixture order</source>
        <translation>Mouvement des appareils</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="107"/>
        <source>All fixtures move in parallel</source>
        <translatorcomment>&quot;bouger&quot; n&apos;est peut-être pas le meilleur verbe</translatorcomment>
        <translation>Tous les appareils bougent en parallèle</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="110"/>
        <source>Parallel</source>
        <translation>Parallèle</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="120"/>
        <source>The pattern propagates to each fixture in a sequential order</source>
        <translation>Le motif se transmet à chaque appareil dans un ordre séquentiel</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="123"/>
        <source>Serial</source>
        <translation>Série</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="224"/>
        <source>Movement</source>
        <translation>Mouvement</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="202"/>
        <location filename="efxeditor.ui" line="303"/>
        <source>Start Offset</source>
        <translation>Décalage</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="130"/>
        <source>Each fixture starts moving immediately with an offset</source>
        <translatorcomment>&quot;bouger&quot; n&apos;est peut-être pas le meilleur verbe</translatorcomment>
        <translation>Chaque appareil commence à bouger avec un décalage</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="133"/>
        <source>Asymmetric</source>
        <translation>Asymétrique</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="252"/>
        <source>Pattern</source>
        <translation>Motif</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="264"/>
        <source>Pattern for moving the mirror/head</source>
        <translation>Le motif utilisé pour le mouvement de la tête/du mirroir</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="274"/>
        <source>Parameters</source>
        <translation>Paramètres</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="296"/>
        <source>Width</source>
        <translation>Largeur</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="501"/>
        <source>Value width of the pattern</source>
        <translation>La largeur du motif</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="467"/>
        <source>Height</source>
        <translation>Hauteur</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="514"/>
        <source>Value height of the pattern</source>
        <translation>La hauteur du motif</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="481"/>
        <source>X offset</source>
        <translation>Décalage en X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="534"/>
        <source>Pattern&apos;s center point on the X axis</source>
        <translation>La position du centre du motif sur l&apos;axe des abscisses (X)</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="474"/>
        <source>Y offset</source>
        <translation>Décalage en Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="488"/>
        <source>Pattern&apos;s center point on the Y axis</source>
        <translation>La position du centre du motif sur l&apos;axe des ordonnées (Y)</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="527"/>
        <source>Rotation</source>
        <translation>Rotation</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="323"/>
        <source>Rotation of the pattern&apos;s starting point</source>
        <translation>L&apos;angle de rotation du motif</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="326"/>
        <location filename="efxeditor.ui" line="424"/>
        <source>°</source>
        <translation>°</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="316"/>
        <source>X frequency</source>
        <translation>Fréquence en X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="404"/>
        <source>Lissajous pattern&apos;s X frequency</source>
        <translation>La fréquence en X du motif Lissajous</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="394"/>
        <source>Y frequency</source>
        <translation>Fréquence en Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="378"/>
        <source>Lissajous pattern&apos;s Y frequency</source>
        <translation>La fréquence en Y du motif Lissajous</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="368"/>
        <source>X phase</source>
        <translation>Phase en X</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="339"/>
        <source>Lissajous pattern&apos;s X phase</source>
        <translation>La phase en X du motif Lissajous</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="437"/>
        <source>Y phase</source>
        <translation>Phase en Y</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="669"/>
        <source>Show/Hide speed dial window</source>
        <translation>Afficher/Masquer la fenêtre de réglage des vitesses</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="454"/>
        <source>Lissajous pattern&apos;s Y phase</source>
        <translation>La phase en Y du motif Lissajous</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="192"/>
        <source>Mode</source>
        <translation>Mode</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="417"/>
        <source>Relative</source>
        <translation>Relatif</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="547"/>
        <source>Color Background</source>
        <translation>Couleur de fond</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="557"/>
        <source>Direction</source>
        <translation>Direction</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="563"/>
        <source>Run the pattern forwards</source>
        <translation>Lire le motif en avant</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="566"/>
        <source>Forward</source>
        <translation>Avant</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="576"/>
        <source>Run the pattern backwards</source>
        <translation>Lire le motif en arrière</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="579"/>
        <source>Backward</source>
        <translation>Arrière</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="605"/>
        <source>Run order</source>
        <translation>Ordre de lecture</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="611"/>
        <source>Run through over and over again</source>
        <translation>Répéter indéfiniment</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="614"/>
        <source>Loop</source>
        <translation>Boucle</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="624"/>
        <source>Run through once and stop</source>
        <translation>Lire une seule fois</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="627"/>
        <source>Single shot</source>
        <translation>Unique</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="634"/>
        <source>First run forwards, then backwards, again forwards, etc...</source>
        <translation>Alterner l&apos;ordre de lecture dans les deux directions</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="637"/>
        <source>Ping pong</source>
        <translation>Ping pong</translation>
    </message>
    <message>
        <location filename="efxeditor.ui" line="689"/>
        <source>See what the EFX does when it is run</source>
        <translation>Prévisualiser l&apos;EFX</translation>
    </message>
    <message>
        <location filename="efxeditor.cpp" line="736"/>
        <source>Remove fixtures</source>
        <translation>Enlever les appareils</translation>
    </message>
    <message>
        <location filename="efxeditor.cpp" line="737"/>
        <source>Do you want to remove the selected fixture(s)?</source>
        <translation>Voulez-vous enlever le(s) appareil(s) sélectionné(s)&#xa0;?</translation>
    </message>
</context>
<context>
    <name>FixtureGroupEditor</name>
    <message>
        <location filename="fixturegroupeditor.ui" line="33"/>
        <source>Fixture Group</source>
        <translation>Groupe d&apos;appareils</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="183"/>
        <source>Fixture group name</source>
        <translation>Nom du groupe</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="58"/>
        <source>Remove selected fixture/head</source>
        <translation>Enlever l&apos;appareil sélectionné</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="88"/>
        <source>Width</source>
        <translation>Largeur</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="95"/>
        <location filename="fixturegroupeditor.ui" line="128"/>
        <source>px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="108"/>
        <source>Height</source>
        <translation>Hauteur</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="141"/>
        <source>Add/replace fixtures to current row, starting from selected cell</source>
        <translation>Ajouter/Remplacer des appareils horizontalement depuis la position actuelle</translation>
    </message>
    <message>
        <location filename="fixturegroupeditor.ui" line="158"/>
        <source>Add/replace fixtures to current column, starting from selected cell</source>
        <translation>Ajouter/Remplacer des appareils verticalement depuis la position actuelle</translation>
    </message>
</context>
<context>
    <name>FixtureManager</name>
    <message>
        <location filename="fixturemanager.cpp" line="366"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="362"/>
        <source>Fixtures Groups</source>
        <translation>Groupes d&apos;appareils</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="366"/>
        <source>Channels</source>
        <translation>Canaux</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="378"/>
        <source>Channels Groups</source>
        <translation>Groupes de canaux</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="611"/>
        <source>&lt;H1&gt;Multiple fixtures selected&lt;/H1&gt;&lt;P&gt;Click &lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt; to remove the selected fixtures.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;Plusieurs appareils sélectionnés&lt;/H1&gt;&lt;P&gt;Cliquez sur &lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt; pour supprimer les appareils sélectionnés.&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="637"/>
        <source>&lt;H1&gt;Multiple fixtures selected&lt;/H1&gt;&lt;P&gt;Fixture list modification is not permitted in operate mode.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;Plusieurs appareils sélectionnés&lt;/H1&gt;&lt;P&gt;La modification de la liste de appareils est impossible en mode Production.&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="646"/>
        <source>&lt;H1&gt;No fixtures&lt;/H1&gt;&lt;P&gt;Click &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; to add fixtures.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;Aucun appareil&lt;/H1&gt;&lt;P&gt;Cliquez sur &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; pour ajouter des appareils.&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="652"/>
        <source>&lt;H1&gt;Nothing selected&lt;/H1&gt;&lt;P&gt;Select a fixture from the list or click &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; to add fixtures.&lt;/P&gt;</source>
        <translation>&lt;H1&gt;Aucune sélection&lt;/H1&gt;&lt;P&gt;Sélectionnez un appareil à partir de la liste ou cliquez sur &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; pour ajouter des appareils.&lt;/P&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="705"/>
        <source>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;Multiple groups selected&lt;/H1&gt;&lt;P&gt;Click &lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt; to remove the selected groups.&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</source>
        <translation>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;Plusieurs groupes sélectionnés&lt;/H1&gt;&lt;P&gt;Cliquez sur &lt;IMG SRC=&quot;:/edit_remove.png&quot;&gt; pour supprimer ces groupes.&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="713"/>
        <source>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;Nothing selected&lt;/H1&gt;&lt;P&gt;Select a channel group from the list or click &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; to add a new channels group.&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</source>
        <translation>&lt;HTML&gt;&lt;BODY&gt;&lt;H1&gt;Aucune sélection&lt;/H1&gt;&lt;P&gt;Sélectionnez un groupe de canaux à partir de la liste ou cliquez sur &lt;IMG SRC=&quot;:/edit_add.png&quot;&gt; pour ajouter un groupe de canaux.&lt;/P&gt;&lt;/BODY&gt;&lt;/HTML&gt;</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="738"/>
        <source>Add group...</source>
        <translation>Ajouter un groupe...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="744"/>
        <location filename="fixturemanager.cpp" line="850"/>
        <source>Add fixture...</source>
        <translation>Ajouter un appareil...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="855"/>
        <source>Add RGB panel...</source>
        <translation>Ajouter un panneau RVB...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="860"/>
        <source>Delete items</source>
        <translation>Supprimer la sélection</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="865"/>
        <source>Properties...</source>
        <translation>Propriétés...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="870"/>
        <source>Channels Fade Configuration...</source>
        <translation>Configuration des fondus des canaux...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="876"/>
        <source>Add fixture to group...</source>
        <translation>Ajouter la sélection au groupe...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="879"/>
        <source>Remove fixture from group</source>
        <translation>Enlever la sélection du groupe</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="883"/>
        <source>New Group...</source>
        <translation>Nouveau groupe...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="886"/>
        <source>Move group up...</source>
        <translation>Monter le groupe...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="892"/>
        <source>Move group down...</source>
        <translation>Descendre le groupe...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="898"/>
        <source>Import fixtures...</source>
        <translation>Importer des appareils...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="903"/>
        <source>Export fixtures...</source>
        <translation>Exporter les appareils...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="909"/>
        <source>Remap fixtures...</source>
        <translation>Remapper les appareils...</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1167"/>
        <source>%1 - Row %2</source>
        <translation>%1 - Ligne %2</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1242"/>
        <source>Do you want to delete the selected items?</source>
        <translation>Voulez-vous supprimer les éléments sélectionnés&#xa0;?</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1297"/>
        <source>Delete Channels Group</source>
        <translation>Supprimer le groupe de canaux</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="975"/>
        <location filename="fixturemanager.cpp" line="1416"/>
        <source>Error</source>
        <translation>Erreur</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="587"/>
        <source>This group contains all fixtures of</source>
        <translation>Ce groupe contient tous les appareils de</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="591"/>
        <location filename="fixturemanager.cpp" line="632"/>
        <source>Total estimated weight</source>
        <translation>Poids total estimé</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="592"/>
        <location filename="fixturemanager.cpp" line="633"/>
        <source>Maximum estimated power consumption</source>
        <translation>Consommation de puissance maximum estimée</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="976"/>
        <location filename="fixturemanager.cpp" line="1417"/>
        <source>Please enter a valid address</source>
        <translation>Veuillez entrer une adresse valide</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1493"/>
        <source>Ungroup fixtures?</source>
        <translation>Dégrouper les appareils&#xa0;?</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1494"/>
        <source>Do you want to ungroup the selected fixtures?</source>
        <translation>Voulez-vous dégrouper les appareils sélectionnés&#xa0;?</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1609"/>
        <source>Import Fixtures List</source>
        <oldsource>Import Fixture Definition</oldsource>
        <translation>Importer une liste d&apos;appareils</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1614"/>
        <source>Export Fixtures List As</source>
        <oldsource>Export Fixture Definition As</oldsource>
        <translation>Exporter la liste d&apos;appareils</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1620"/>
        <source>Fixtures List (*%1)</source>
        <oldsource>Fixture Definitions (*%1)</oldsource>
        <translation>Liste d&apos;appareils (*%1)</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1622"/>
        <source>All Files (*.*)</source>
        <translation>Tous les fichiers (*.*)</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1624"/>
        <source>All Files (*)</source>
        <translation>Tous les fichiers (*)</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="942"/>
        <source>Fixture manager</source>
        <translation>Gestionnaire d&apos;appareils</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1018"/>
        <source>Generic Dimmer</source>
        <translation>Gradateur générique</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1241"/>
        <source>Delete Fixtures</source>
        <translation>Supprimer des appareils</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1298"/>
        <source>Do you want to delete the selected groups?</source>
        <translation>Voulez-vous supprimer les groupes sélectionnés&#xa0;?</translation>
    </message>
    <message>
        <location filename="fixturemanager.cpp" line="1358"/>
        <source>Change fixture properties</source>
        <translation>Éditer les propriétés de l&apos;appareil</translation>
    </message>
</context>
<context>
    <name>FixtureRemap</name>
    <message>
        <location filename="fixtureremap.ui" line="33"/>
        <source>Fixtures Remap</source>
        <translation>Remappage des appareils</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="47"/>
        <source>Add target fixture...</source>
        <translation>Ajouter un appareil cible...</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="64"/>
        <source>Remove target fixture...</source>
        <translation>Enlever l&apos;appareil cible...</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="88"/>
        <source>Clone and auto-remap the selected source fixture</source>
        <translation>Dupliquer et remapper automatiquement l&apos;appareil source sélectionné</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="112"/>
        <source>Connect selections...</source>
        <translation>Connecter la sélection...</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="129"/>
        <source>Disconnect selections...</source>
        <translation>Déconnecter la sélection...</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="284"/>
        <source>Destination project name</source>
        <translation>Nom du projet de destination</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="180"/>
        <source>Remapped Fixtures</source>
        <translation>Appareils remappés</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="185"/>
        <location filename="fixtureremap.ui" line="217"/>
        <source>Address</source>
        <translation>Adresse</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="212"/>
        <source>Source Fixtures</source>
        <translation>Appareils sources</translation>
    </message>
    <message>
        <location filename="fixtureremap.ui" line="272"/>
        <source>Remap fixture names</source>
        <translation>Reprendre le nom des appareils sources</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="131"/>
        <location filename="fixtureremap.cpp" line="133"/>
        <source> (remapped)</source>
        <translation> (remappé)</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="226"/>
        <source>Generic Dimmer</source>
        <translation>Gradateur générique</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="297"/>
        <source>Delete Fixtures</source>
        <translation>Supprimer un élément</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="298"/>
        <source>Do you want to delete the selected items?</source>
        <translation>Voulez-vous supprimer l&apos;élément cible sélectionné&#xa0;?</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="346"/>
        <source>Invalid operation</source>
        <translation>Opération invalide</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="347"/>
        <source>You are trying to clone a fixture on an address already in use. Please fix the target list first.</source>
        <translation>L&apos;adresse de l&apos;appareil à dupliquer est déjà utilisée. Veuillez d&apos;abord modifier la liste des appareils cibles.</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="409"/>
        <location filename="fixtureremap.cpp" line="425"/>
        <location filename="fixtureremap.cpp" line="448"/>
        <location filename="fixtureremap.cpp" line="538"/>
        <source>Invalid selection</source>
        <translation>Sélection invalide</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="410"/>
        <location filename="fixtureremap.cpp" line="426"/>
        <location filename="fixtureremap.cpp" line="539"/>
        <source>Please select a source and a target fixture or channel to perform this operation.</source>
        <translation>Veuillez d&apos;abord sélectionner un appareil ou un canal dans la liste des sources et des cibles.</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="449"/>
        <source>To perform a fixture remap, please select fixtures on both lists.</source>
        <translation>Veuillez sélectionner soit un appareil soit un canal dans les deux listes.</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="666"/>
        <source>This might take a while...</source>
        <translation>Ceci peut prendre un moment...</translation>
    </message>
    <message>
        <location filename="fixtureremap.cpp" line="666"/>
        <source>Cancel</source>
        <translation>Annuler</translation>
    </message>
</context>
<context>
    <name>FixtureSelection</name>
    <message>
        <location filename="fixtureselection.ui" line="33"/>
        <source>Select fixture</source>
        <translation>Sélectionner un appareil</translation>
    </message>
    <message>
        <location filename="fixtureselection.cpp" line="74"/>
        <source>No fixtures available</source>
        <translation>Aucun appareil disponible</translation>
    </message>
    <message>
        <location filename="fixtureselection.cpp" line="76"/>
        <source>Go to the Fixture Manager and add some fixtures first.</source>
        <translation>Veuillez commencez par ajouter des appareils à partir du gestionnaire d&apos;appareils.</translation>
    </message>
</context>
<context>
    <name>FixtureTreeWidget</name>
    <message>
        <location filename="fixturetreewidget.cpp" line="66"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="71"/>
        <source>Universe</source>
        <translation>Univers</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="76"/>
        <source>Address</source>
        <translation>Adresse</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="81"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="86"/>
        <source>Heads</source>
        <translation>Têtes</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="91"/>
        <source>Manufacturer</source>
        <translation>Fabricant</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="96"/>
        <source>Model</source>
        <translation>Modèle</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="214"/>
        <location filename="fixturetreewidget.cpp" line="222"/>
        <source>Generic</source>
        <translation>Générique</translation>
    </message>
    <message>
        <location filename="fixturetreewidget.cpp" line="234"/>
        <source>Head</source>
        <translation>Tête</translation>
    </message>
</context>
<context>
    <name>FunctionLiveEditDialog</name>
    <message>
        <location filename="functionliveeditdialog.cpp" line="46"/>
        <source>Function Live Edit</source>
        <translation>Edition d&apos;une fonction en direct</translation>
    </message>
</context>
<context>
    <name>FunctionManager</name>
    <message>
        <location filename="functionmanager.cpp" line="206"/>
        <source>New &amp;scene</source>
        <translation>Nouvelle &amp;scène</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="212"/>
        <source>New c&amp;haser</source>
        <translation>Nouveau c&amp;haser</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="218"/>
        <source>New se&amp;quence</source>
        <translation>Nouvelle sé&amp;quence</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="230"/>
        <source>New c&amp;ollection</source>
        <translation>Nouvelle c&amp;ollection</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="224"/>
        <source>New E&amp;FX</source>
        <translation>Nouvel E&amp;FX</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="236"/>
        <source>New &amp;RGB Matrix</source>
        <translation>Nouvelle matrice &amp;RVB</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="242"/>
        <source>New scrip&amp;t</source>
        <translation>Nouveau scrip&amp;t</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="333"/>
        <source>New Scene</source>
        <translation>Nouvelle scène</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="346"/>
        <source>New Chaser</source>
        <translation>Nouveau chaser</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="369"/>
        <source>New Sequence</source>
        <translation>Nouvelle séquence</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="279"/>
        <source>&amp;Clone</source>
        <translation>&amp;Dupliquer</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="248"/>
        <source>New au&amp;dio</source>
        <translation>Nouveau son (&amp;D)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="255"/>
        <source>New vid&amp;eo</source>
        <translation>Nouvelle vid&amp;éo</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="261"/>
        <source>New fo&amp;lder</source>
        <translation>Nouveau dossier (&amp;L)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="267"/>
        <source>Select Startup Function</source>
        <translation>Sélectionner la fonction de démarrage</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="272"/>
        <source>Function &amp;Wizard</source>
        <translation>Assistant de fonction (&amp;W)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="285"/>
        <source>&amp;Delete</source>
        <translation>Supprimer (&amp;D)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="291"/>
        <source>Select &amp;all</source>
        <translation>Tout sélectionner (&amp;A)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="383"/>
        <source>New Collection</source>
        <translation>Nouvelle collection</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="396"/>
        <source>New EFX</source>
        <translation>Nouvel EFX</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="409"/>
        <source>New RGB Matrix</source>
        <translation>Nouvelle matrice RVB</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="422"/>
        <source>New Script</source>
        <translation>Nouveau script</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="432"/>
        <source>Open Audio File</source>
        <translation>Ouvrir un fichier audio</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="441"/>
        <source>Audio Files (%1)</source>
        <translation>Fichiers audio (%1)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="443"/>
        <location filename="functionmanager.cpp" line="497"/>
        <source>All Files (*.*)</source>
        <translation>Tous les fichiers (*.*)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="445"/>
        <location filename="functionmanager.cpp" line="499"/>
        <source>All Files (*)</source>
        <translation>Tous les fichiers (*)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="465"/>
        <source>Unsupported audio file</source>
        <translation>Fichier audio non pris en charge</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="465"/>
        <source>This audio file cannot be played with QLC+. Sorry.</source>
        <translation>Ce fichier audio ne peut pas être lu par QLC+, désolé.</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="486"/>
        <source>Open Video File</source>
        <translation>Ouvrir un fichier vidéo</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="495"/>
        <source>Video Files (%1)</source>
        <translation>Fichiers vidéo (%1)</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="519"/>
        <source>Unsupported video file</source>
        <translation>Fichier vidéo non pris en charge</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="519"/>
        <source>This video file cannot be played with QLC+. Sorry.</source>
        <translation>Ce fichier vidéo ne peut pas être lu par QLC+, désolé.</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="593"/>
        <source>Do you want to DELETE folder:</source>
        <oldsource>Do you want to DELETE foler:</oldsource>
        <translation>Voulez-vous SUPPRIMER le dossier :</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="595"/>
        <source>Do you want to DELETE functions:</source>
        <translation>Voulez-vous SUPPRIMER ces fonctions :</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="607"/>
        <source>(This will also DELETE: </source>
        <translation>(Cela SUPPRIMERA également : </translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="619"/>
        <source>Delete Functions</source>
        <translation>Supprimer les fonctions</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="725"/>
        <source>Function</source>
        <translation>Fonctions</translation>
    </message>
    <message>
        <location filename="functionmanager.cpp" line="862"/>
        <source> (Copy)</source>
        <translation> (copie)</translation>
    </message>
</context>
<context>
    <name>FunctionSelection</name>
    <message>
        <location filename="functionselection.ui" line="33"/>
        <source>Select Function</source>
        <translation>Sélection de fonction</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="41"/>
        <source>All functions</source>
        <translation>Toutes les fonctions</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="51"/>
        <source>Running functions</source>
        <translation>Fonctions actives</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="93"/>
        <source>Filter</source>
        <translation>Filtre</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="122"/>
        <source>EFX</source>
        <translation>EFX</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="129"/>
        <source>Display collections in the list</source>
        <translation>Afficher les collections dans la liste</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="139"/>
        <source>Display scripts in the list</source>
        <translation>Afficher les scripts dans la liste</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="142"/>
        <source>Scripts</source>
        <translation>Scripts</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="180"/>
        <source>Sequences</source>
        <translation>Séquences</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="99"/>
        <source>Display chasers in the list</source>
        <translation>Afficher les chasers dans la liste</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="170"/>
        <source>Display RGB Matrices in the list</source>
        <oldsource>Display RGB Matrixes in the list</oldsource>
        <translation>Afficher les matrices RVB dans la liste</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="173"/>
        <source>RGB matrices</source>
        <oldsource>RGB matrixes</oldsource>
        <translation>Matrices RVB</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="119"/>
        <source>Display EFX&apos;s in the list</source>
        <translation>Afficher les EFX dans la liste</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="109"/>
        <source>Display scenes in the list</source>
        <translation>Afficher les scènes dans la liste</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="112"/>
        <source>Scenes</source>
        <translation>Scènes</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="163"/>
        <source>Shows</source>
        <translation>Shows</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="149"/>
        <source>Audio</source>
        <translation>Sons</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="156"/>
        <source>Video</source>
        <translation>Vidéos</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="102"/>
        <source>Chasers</source>
        <translation>Chasers</translation>
    </message>
    <message>
        <location filename="functionselection.ui" line="132"/>
        <source>Collections</source>
        <translation>Collections</translation>
    </message>
    <message>
        <location filename="functionselection.cpp" line="78"/>
        <source>Functions</source>
        <translation>Fonctions</translation>
    </message>
    <message>
        <location filename="functionselection.cpp" line="315"/>
        <source>&lt;No function&gt;</source>
        <translation>&lt;Aucune fonction&gt;</translation>
    </message>
    <message>
        <location filename="functionselection.cpp" line="324"/>
        <source>&lt;Create a new track&gt;</source>
        <translation>&lt;Créer une nouvelle piste&gt;</translation>
    </message>
</context>
<context>
    <name>FunctionWizard</name>
    <message>
        <location filename="functionwizard.ui" line="33"/>
        <source>Function Wizard</source>
        <translation>Assistant de fonction</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="57"/>
        <source>Next</source>
        <translation>Suivant</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="71"/>
        <source>OK</source>
        <translation>OK</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="85"/>
        <source>Cancel</source>
        <translation>Annuler</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="105"/>
        <source>Introduction</source>
        <translation>Introduction</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="114"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Welcome to the QLC+ wizard!&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;This is a guided procedure that will allow you to start using QLC+ in a few minutes.&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;It basically consists in three simple steps:&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;1- add fixtures&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;2- select capabilities to create functions&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;3- add widgets to the Virtual Console&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;To move from a step to another press the &amp;quot;Next&amp;quot; button&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Bienvenue dans l&apos;assistant QLC+&#xa0;!&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;Cette procédure guidée va vous permettre de commencer à utiliser QLC+ en quelques minutes.&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;Elle est composée de 3 étapes :&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;1. Ajouter un ou plusieurs appareils ;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;2. Choisir les fonctions à créer parmis celles supportées ;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;3. Ajouter des widgets à la console virtuelle.&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;Pour passer à l&apos;étape suivante, cliquez sur le bouton &amp;quot;Suivant&amp;quot;.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="207"/>
        <source>Fixtures that will be included in automatic function creation</source>
        <translation>La liste des appareils qui seront inclus dans la création automatique de fonctions</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="254"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Add the fixtures for which you want to create functions and widgets&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Ajoutez les appareils pour lesquels vous voulez créer des fonctions&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="266"/>
        <source>Functions</source>
        <translation>Fonctions</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="273"/>
        <source>Results</source>
        <translation>Résultats</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="282"/>
        <source>Available</source>
        <translation>Disponible</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="287"/>
        <source>Odd/Even</source>
        <translation>Pair/Impair</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="307"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Based on the fixtures you added, I can create the functions listed on the left. Just select what you need and see the results on the right!&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Avec ces appareils, je peux créer les fonctions listées à gauche. Choisissez celles que vous désirez, elles apparaîtront à droite&#xa0;!&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="319"/>
        <source>Virtual Console</source>
        <translation>Console virtuelle</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="337"/>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Based on the functions you selected, I can create the following widgets on your Virtual Console. Just check which ones you need.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Sans Serif&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;img src=&quot;:/wizard.png&quot; width=&quot;24&quot; /&gt; Avec ces fonctions, je peux créer les widgets suivants. Choisissez ceux que vous désirez, ils seront ajoutés dans votre console virtuelle.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="349"/>
        <source>Widgets</source>
        <translation>Widgets</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="148"/>
        <location filename="functionwizard.ui" line="223"/>
        <source>Fixtures</source>
        <translation>Appareils</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="228"/>
        <source>Supported capabilities</source>
        <translation>Fonctions supportées</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="187"/>
        <source>Select fixtures that will be included in the automatically created functions</source>
        <translation>Ajouter des appareils à la liste</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="190"/>
        <source>Add</source>
        <translation>Ajouter</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="154"/>
        <source>Don&apos;t include selected fixtures in the created functions</source>
        <translation>Enlever les appareils sélectionnés de la liste</translation>
    </message>
    <message>
        <location filename="functionwizard.ui" line="157"/>
        <source>Remove</source>
        <translation>Enlever</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="176"/>
        <source>%1 group</source>
        <translation>Groupe %1</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="193"/>
        <source>Error</source>
        <translation>Erreur</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="193"/>
        <source>%1 has no capability supported by this wizard.</source>
        <translation>%1 n&apos;a pas de fonction supportée par cet assistant.</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="467"/>
        <source>Presets solo frame</source>
        <translation>Cadre de préréglages solos</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="520"/>
        <source>Click &amp; Go RGB</source>
        <translation>Accès rapide RVB</translation>
    </message>
    <message>
        <location filename="functionwizard.cpp" line="524"/>
        <source>Click &amp; Go Macro</source>
        <translation>Accès rapide Macro</translation>
    </message>
</context>
<context>
    <name>GrandMasterSlider</name>
    <message>
        <location filename="grandmasterslider.cpp" line="90"/>
        <source>GM</source>
        <translation>GM</translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="145"/>
        <source>Grand Master &lt;B&gt;limits&lt;/B&gt; the maximum value of</source>
        <translation>Le Grand Master &lt;B&gt;limite&lt;/B&gt; la valeur maximum</translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="148"/>
        <source>Grand Master &lt;B&gt;reduces&lt;/B&gt; the current value of</source>
        <translation>Le Grand Master &lt;B&gt;reduit&lt;/B&gt; la valeur</translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="157"/>
        <source>intensity channels</source>
        <translation>des canaux d&apos;intensité</translation>
    </message>
    <message>
        <location filename="grandmasterslider.cpp" line="160"/>
        <source>all channels</source>
        <translation>de tous les canaux</translation>
    </message>
</context>
<context>
    <name>InputChannelEditor</name>
    <message>
        <location filename="inputchanneleditor.ui" line="33"/>
        <source>Input Channel Editor</source>
        <translation>Éditeur de canal d&apos;entrée</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="39"/>
        <source>Input Channel</source>
        <translation>Canal d&apos;entrée</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="55"/>
        <source>Number</source>
        <translation>Numéro</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="88"/>
        <source>Midi</source>
        <translation>MIDI</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="94"/>
        <source>Channel</source>
        <translation>Canal</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="111"/>
        <source>Message</source>
        <translation>Message</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="121"/>
        <source>Parameter</source>
        <translation>Paramètre</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="129"/>
        <source>Control Change</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="134"/>
        <source>Note On/Off</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="139"/>
        <source>Note Aftertouch</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="144"/>
        <source>Program Change</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="149"/>
        <source>Channel Aftertouch</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="154"/>
        <source>Pitch Wheel</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="159"/>
        <source>Beat Clock: Start/Stop/Continue</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="164"/>
        <source>Beat Clock: Beat</source>
        <translation></translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="185"/>
        <source>Note</source>
        <translation>Note</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="48"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="inputchanneleditor.ui" line="75"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
</context>
<context>
    <name>InputOutputManager</name>
    <message>
        <location filename="inputoutputmanager.cpp" line="85"/>
        <source>Add U&amp;niverse</source>
        <translation>Ajouter un u&amp;nivers</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="91"/>
        <source>&amp;Delete Universe</source>
        <oldsource>Universe</oldsource>
        <translation>Supprimer l&apos;univers (&amp;D)</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="110"/>
        <source>Universe name:</source>
        <translation>Nom de l&apos;univers :</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="120"/>
        <source>Passthrough</source>
        <translation>Traversé</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="229"/>
        <location filename="inputoutputmanager.cpp" line="404"/>
        <source>Universe %1</source>
        <translation>Univers %1</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="362"/>
        <location filename="inputoutputmanager.cpp" line="381"/>
        <source>Delete Universe</source>
        <translation>Supprimer l&apos;univers</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="363"/>
        <source>The universe you are trying to delete is patched. Are you sure you want to delete it?</source>
        <translation>Cet univers est actuellement patché. Êtes-vous sûr(e) de vouloir le supprimer&#xa0;?</translation>
    </message>
    <message>
        <location filename="inputoutputmanager.cpp" line="382"/>
        <source>There are some fixtures using the universe you are trying to delete. Are you sure you want to delete it?</source>
        <translation>Des appareils utilisent actuellement cet univers. Êtes-vous sûr(e) de vouloir le supprimer&#xa0;?</translation>
    </message>
</context>
<context>
    <name>InputOutputPatchEditor</name>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="33"/>
        <source>Input/Output patch editor</source>
        <translation>Editeur du patch entrée/sortie</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="55"/>
        <source>Mapping</source>
        <translation>Mapping</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="80"/>
        <source>Plugin</source>
        <translation>Plugin</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="85"/>
        <location filename="inputoutputpatcheditor.ui" line="281"/>
        <source>Device</source>
        <translation>Périphérique</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="90"/>
        <location filename="inputoutputpatcheditor.ui" line="286"/>
        <source>Input</source>
        <translation>Entrée</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="98"/>
        <location filename="inputoutputpatcheditor.ui" line="291"/>
        <source>Output</source>
        <translation>Sortie</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="106"/>
        <source>Feedback</source>
        <translation>Retour d&apos;info</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="124"/>
        <source>Configure the selected plugin</source>
        <translation>Configurer le greffon sélectionné</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="144"/>
        <source>Enable/Disable USB hotplugging (requires restart)</source>
        <translation>Activer/désactiver le branchement USB à chaud (nécessite un redémarrage)</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="168"/>
        <location filename="inputoutputpatcheditor.ui" line="257"/>
        <source>Profile</source>
        <translation>Profil</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="174"/>
        <source>Create a new input profile</source>
        <translation>Créer un nouveau profil d&apos;entrée</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="194"/>
        <source>Delete the selected input profile</source>
        <translation>Supprmer le profil d&apos;entrée sélectionné</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="214"/>
        <source>Edit the selected input profile</source>
        <translation>Éditer le profil d&apos;entrée sélectionné</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="262"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="271"/>
        <source>Audio</source>
        <translation>Son</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="299"/>
        <source>Configuration</source>
        <translation>Configuration</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="305"/>
        <source>Sample Rate (Hz)</source>
        <translation>Échantillonnage (Hz)</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="312"/>
        <source>Channels</source>
        <translation>Canaux</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.ui" line="319"/>
        <source>Level Monitor</source>
        <translation>Moniteur de niveau</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="435"/>
        <location filename="inputoutputpatcheditor.cpp" line="454"/>
        <location filename="inputoutputpatcheditor.cpp" line="560"/>
        <source>Error</source>
        <translation>Erreur</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="436"/>
        <location filename="inputoutputpatcheditor.cpp" line="455"/>
        <source>Output line already assigned</source>
        <translation>La ligne de sortie est déjà assignée</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="561"/>
        <source>An error occurred while trying to open the selected device line.
This can be caused either by a wrong system configuration or an unsupported input/output mode.
Please refer to the plugins documentation to troubleshoot this.</source>
        <translation>Une erreur est survenue lors de l&apos;ouverture de la ligne de périphérique sélectionnée.
Cela peut être dû à une mauvaise configuration système ou un mode d&apos;entrée/sortie non supporté.
Veuillez vous référer à la documentation du plugin.</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="702"/>
        <location filename="inputoutputpatcheditor.cpp" line="836"/>
        <source>Existing Input Profile</source>
        <translation>Profil d&apos;entrée existant</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="703"/>
        <location filename="inputoutputpatcheditor.cpp" line="837"/>
        <source>An input profile at %1 already exists. Do you wish to overwrite it?</source>
        <translation>Un profil d&apos;entrée existe déjà en &apos;%1&apos;. Voulez-vous l&apos;écraser&#xa0;?</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="712"/>
        <location filename="inputoutputpatcheditor.cpp" line="846"/>
        <source>Save Input Profile</source>
        <translation>Enregistrer le profil d&apos;entrée</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="713"/>
        <location filename="inputoutputpatcheditor.cpp" line="847"/>
        <source>Input Profiles (*.qxi)</source>
        <translation>Profils d&apos;entrée (*.qxi)</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="726"/>
        <location filename="inputoutputpatcheditor.cpp" line="863"/>
        <source>Saving failed</source>
        <translation>L&apos;enregistrement a échoué</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="727"/>
        <source>Unable to save the profile to %1</source>
        <translation>Impossible d&apos;enregistrer le profil sous %1</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="764"/>
        <source>Delete profile</source>
        <translation>Supprimer le profil</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="765"/>
        <source>Do you wish to permanently delete profile &quot;%1&quot;?</source>
        <translation>Voulez-vous supprimer définitivement le profil &quot;%1&quot;&#xa0;?</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="793"/>
        <source>File deletion failed</source>
        <translation>L&apos;effacement du fichier a échoué</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="794"/>
        <source>Unable to delete file %1</source>
        <translation>Impossible de supprimer le fichier %1</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="864"/>
        <source>Unable to save %1 to %2</source>
        <translation>Impossible d&apos;enregistrer %1 sous %2</translation>
    </message>
    <message>
        <location filename="inputoutputpatcheditor.cpp" line="884"/>
        <source>Default device</source>
        <translation>Périphérique par défaut</translation>
    </message>
</context>
<context>
    <name>InputProfileEditor</name>
    <message>
        <location filename="inputprofileeditor.ui" line="33"/>
        <source>Input Profile Editor</source>
        <translation>Éditeur de profil d&apos;entrée</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="47"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="53"/>
        <source>Manufacturer</source>
        <translation>Fabricant</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="77"/>
        <source>The name of the company that made the device</source>
        <translation>Le nom de la société qui a fabriqué le périphérique</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="60"/>
        <source>Model</source>
        <translation>Modèle</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="87"/>
        <source>The device&apos;s model name</source>
        <translation>Le nom du modèle du périphérique</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="124"/>
        <source>Channels</source>
        <translation>Canaux</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="297"/>
        <source>Channel</source>
        <translation>Canal</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="302"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="347"/>
        <source>Custom feedback</source>
        <translation>Retour d&apos;info personnalisé</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="359"/>
        <source>Upper value</source>
        <translation>Valeur supérieure</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="379"/>
        <source>Lower value</source>
        <translation>Valeur inférieure</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="67"/>
        <location filename="inputprofileeditor.ui" line="307"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="94"/>
        <source>MIDI Global Settings</source>
        <translation>Paramètres MIDI globaux</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="100"/>
        <source>When MIDI notes are used, send a Note Off when value is 0</source>
        <translation>Lors de l&apos;utilisation de notes MIDI, envoyer Note Off quand la valeur est de 0</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="130"/>
        <location filename="inputprofileeditor.ui" line="312"/>
        <source>Behaviour</source>
        <translation>Comportement</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="327"/>
        <source>Add a new channel description</source>
        <translation>Ajouter un nouveau canal</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="212"/>
        <source>Remove the selected channels</source>
        <translation>Enlever les canaux sélectionnés</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="255"/>
        <source>Edit the selected channel</source>
        <translation>Éditer le canal sélectionné</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="232"/>
        <source>Automatically add channels to the list when you wiggle the device&apos;s controls</source>
        <translation>Ajouter automatiquement le canal à la liste quand un contrôle du périphérique est sollicité</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="136"/>
        <source>Movement</source>
        <translation>Mouvement</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="150"/>
        <source>Absolute</source>
        <translation>Absolu</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="155"/>
        <source>Relative</source>
        <translation>Relatif</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="189"/>
        <source>Generate an extra Press/Release when toggled</source>
        <translation>Générer une pression/relâche supplémentaire quand basculé</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.ui" line="163"/>
        <source>Sensitivity</source>
        <translation>Sensibilité</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="107"/>
        <source>File not writable</source>
        <translation>Fichier inaccessible en écriture</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="108"/>
        <source>You do not have permission to write to the file %1. You might not be able to save your modifications to the profile.</source>
        <translation>Vous n&apos;avez pas les permissions pour écrire le fichier &apos;%1&apos;. Vous risquez de ne pas pouvoir sauvegarder vos modifications du profil.</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="261"/>
        <source>Missing information</source>
        <translation>Information manquante</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="262"/>
        <source>Manufacturer and/or model name is missing.</source>
        <translation>Le nom du fabricant et/ou du modèle est manquant.</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="311"/>
        <location filename="inputprofileeditor.cpp" line="410"/>
        <source>Channel already exists</source>
        <translation>Canal existant</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="312"/>
        <location filename="inputprofileeditor.cpp" line="411"/>
        <source>Channel %1 already exists</source>
        <translation>Le canal %1 existe déjà</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="333"/>
        <source>Delete channels</source>
        <translation>Supprimer les canaux</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="334"/>
        <source>Delete all %1 selected channels?</source>
        <translation>Supprimer les %1 canaux sélectionnés&#xa0;?</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="451"/>
        <source>Channel wizard activated</source>
        <translation>Assistant de canal activé</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="452"/>
        <source>You have enabled the input channel wizard. After clicking OK, wiggle your mapped input profile&apos;s controls. They should appear into the list. Click the wizard button again to stop channel auto-detection.

Note that the wizard cannot tell the difference between a knob and a slider so you will have to do the change manually.</source>
        <translation>Vous avez activé l&apos;assistant de canal d&apos;entrée. Après avoir cliqué sur OK, manipulez les contrôles de votre périphérique d&apos;entrée à mapper. Ils devraient apparaître dans la liste. Cliquez de nouveau sur le bouton de l&apos;assistant pour arrêter l&apos;auto-détection de canal.

Notez que l&apos;assistant ne peut pas différencier un bouton rotatif d&apos;un fader, vous devrez faire ce changement manuellement.</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="600"/>
        <source>Button %1</source>
        <translation>Bouton %1</translation>
    </message>
    <message>
        <location filename="inputprofileeditor.cpp" line="640"/>
        <source>Slider %1</source>
        <translation>Fader %1</translation>
    </message>
</context>
<context>
    <name>InputSelectionWidget</name>
    <message>
        <location filename="inputselectionwidget.ui" line="60"/>
        <source>Key combination</source>
        <translation>Raccourci clavier</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="78"/>
        <source>Reset the keyboard shortcut key</source>
        <translation>Effacer le raccourci clavier</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="98"/>
        <source>Set a key combination for this widget</source>
        <translation>Définir un raccourci clavier pour contrôler ce widget</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="118"/>
        <source>Keyboard combination that toggles this widget</source>
        <translation>Le raccourci clavier qui contrôle ce widget</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="137"/>
        <source>External Input</source>
        <translation>Entrée externe</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="263"/>
        <source>When toggled, you can click an external button to assign it to this widget.</source>
        <translation>Quand activée, pressez un bouton externe afin de l&apos;assigner à ce widget.</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="266"/>
        <source>Auto Detect</source>
        <translation>Auto-détection</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="155"/>
        <source>Input Universe</source>
        <translation>Univers d&apos;entrée</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="231"/>
        <source>Input Channel</source>
        <translation>Canal d&apos;entrée</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="162"/>
        <source>The input universe that sends data to this widget</source>
        <translation>L&apos;univers d&apos;entrée qui contrôle ce widget</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="240"/>
        <source>Custom Feedback</source>
        <translation>Retour d&apos;info personnalisé</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="221"/>
        <source>The particular input channel within the input universe that sends data to this widget</source>
        <translation>Le canal de l&apos;univers d&apos;entrée qui contrôle ce widget</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="288"/>
        <source>Choose an external input universe &amp; channel that this widget should listen to.</source>
        <translation>Choisir l&apos;univers d&apos;entrée et son canal qui contrôlera ce widget.</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="291"/>
        <source>Choose...</source>
        <translation>Choisir...</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="172"/>
        <source>Custom feedback</source>
        <translation>Retour d&apos;info personnalisé</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="184"/>
        <source>Lower value</source>
        <translation>Valeur inférieure</translation>
    </message>
    <message>
        <location filename="inputselectionwidget.ui" line="211"/>
        <source>Upper value</source>
        <translation>Valeur suppérieure</translation>
    </message>
</context>
<context>
    <name>Monitor</name>
    <message>
        <location filename="monitor/monitor.cpp" line="336"/>
        <source>Fixture Monitor</source>
        <translation>Moniteur d&apos;appareils</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="377"/>
        <source>2D View</source>
        <translation>Vue 2D</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="384"/>
        <source>Font</source>
        <translation>Police</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="393"/>
        <source>DMX Channels</source>
        <translation>Canaux DMX</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="394"/>
        <source>Show absolute DMX channel numbers</source>
        <translation>Montrer le numéro absolu des canaux DMX</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="404"/>
        <source>Relative Channels</source>
        <translation>Canaux relatifs</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="405"/>
        <source>Show channel numbers relative to fixture</source>
        <translation>Montrer le numéro des canaux relatif à leur appareil</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="421"/>
        <source>DMX Values</source>
        <translation>Valeurs DMX</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="422"/>
        <source>Show DMX values 0-255</source>
        <translation>Montrer les valeurs DMX (0-255)</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="433"/>
        <source>Percent Values</source>
        <translation>Valeurs en %</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="434"/>
        <source>Show percentage values 0-100%</source>
        <translation>Montrer les valeurs en pourcentage (0-100%)</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="447"/>
        <source>Universe</source>
        <oldsource>Universe:</oldsource>
        <translation>Univers</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="452"/>
        <source>All universes</source>
        <translation>Tous les univers</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="468"/>
        <location filename="monitor/monitor.cpp" line="549"/>
        <source>Close</source>
        <translation>Quitter</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="469"/>
        <location filename="monitor/monitor.cpp" line="550"/>
        <source>Close this window</source>
        <translation>Fermer cette fenêtre</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="488"/>
        <source>DMX View</source>
        <translation>Vue DMX</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="494"/>
        <source>Size</source>
        <oldsource>Size:</oldsource>
        <translation>Taille</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="518"/>
        <source>Meters</source>
        <translation>mètres</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="519"/>
        <source>Feet</source>
        <translation>pieds</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="528"/>
        <source>Add fixture</source>
        <translation>Ajouter un appareil</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="530"/>
        <source>Remove fixture</source>
        <translation>Enlever l&apos;appareil</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="535"/>
        <source>Set a background picture</source>
        <translation>Définir une image d&apos;arrière-plan</translation>
    </message>
    <message>
        <location filename="monitor/monitor.cpp" line="538"/>
        <source>Show/hide labels</source>
        <translation>Afficher/Masquer les étiquettes</translation>
    </message>
</context>
<context>
    <name>MonitorBackgroundSelection</name>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="33"/>
        <source>Background Picture Selection</source>
        <translation>Sélection d&apos;une image d&apos;arrière-plan</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="43"/>
        <source>No background</source>
        <translation>Aucun arrière-plan</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="52"/>
        <source>Common background</source>
        <translation>Arrière-plan commun</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="88"/>
        <source>Custom background list</source>
        <translation>Liste d&apos;arrière-plan personnalisée</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="98"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.ui" line="103"/>
        <source>Background</source>
        <translation>Arrière-plan</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.cpp" line="160"/>
        <location filename="monitor/monitorbackgroundselection.cpp" line="182"/>
        <source>Select background image</source>
        <translation>Choisir une image d&apos;arrière-plan</translation>
    </message>
    <message>
        <location filename="monitor/monitorbackgroundselection.cpp" line="162"/>
        <location filename="monitor/monitorbackgroundselection.cpp" line="184"/>
        <source>Images</source>
        <translation>Images</translation>
    </message>
</context>
<context>
    <name>MonitorFixturePropertiesEditor</name>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="33"/>
        <source>Monitor Fixture Properties Editor</source>
        <translation>Éditeur des propriétés de l&apos;appareil</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="49"/>
        <source>Gel color</source>
        <oldsource>Gel color:</oldsource>
        <translation>Couleur du filtre</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="56"/>
        <source>Position and rotation</source>
        <translation>Position et rotation</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="72"/>
        <source>Vertical</source>
        <oldsource>Vertical:</oldsource>
        <translation>Verticale</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="79"/>
        <source>Horizontal</source>
        <oldsource>Horizontal:</oldsource>
        <translation>Horizontale</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="96"/>
        <source>Rotation</source>
        <oldsource>Rotation:</oldsource>
        <translation>Rotation</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="118"/>
        <source>Set the color of the gel installed on the fixture</source>
        <translation>Définir la couleur du filtre installé sur l&apos;appareil</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="131"/>
        <source>Reset the current color</source>
        <translation>Réinitialiser la couleur actuelle</translation>
    </message>
    <message>
        <location filename="monitor/monitorfixturepropertieseditor.ui" line="171"/>
        <source>Fixture name:</source>
        <translation>Nom de l&apos;appareil :</translation>
    </message>
</context>
<context>
    <name>MultiTrackView</name>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="319"/>
        <source>Do you want to DELETE item:</source>
        <oldsource>Do you want to DELETE sequence:</oldsource>
        <translation>Voulez-vous SUPPRIMER l&apos;élément :</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="322"/>
        <source>Delete Functions</source>
        <translation>Supprimer les fonctions</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="354"/>
        <source>Delete Track</source>
        <translation>Supprimer la piste</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="342"/>
        <source>Do you want to DELETE track:</source>
        <translation>Voulez-vous SUPPRIMER la piste :</translation>
    </message>
    <message>
        <location filename="showmanager/multitrackview.cpp" line="345"/>
        <source>This operation will also DELETE:</source>
        <translation>Cela SUPPRIMERA également :</translation>
    </message>
</context>
<context>
    <name>PaletteGenerator</name>
    <message>
        <location filename="palettegenerator.cpp" line="99"/>
        <source>Primary colours</source>
        <oldsource>Primary colors</oldsource>
        <translation>Couleurs primaires</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="100"/>
        <source>16 Colours</source>
        <oldsource>16 Colors</oldsource>
        <translation>16 couleurs</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="101"/>
        <source>Shutter macros</source>
        <translation>Macros d&apos;obturateur</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="102"/>
        <source>Gobo macros</source>
        <translation>Macros de gobo</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="103"/>
        <source>Colour macros</source>
        <oldsource>Color macros</oldsource>
        <translation>Macros de couleur</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="104"/>
        <source>Animations</source>
        <translation>Animations</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="107"/>
        <source>Unknown</source>
        <translation>Inconnu</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="255"/>
        <source>%1 - %2 (Even)</source>
        <translation>%1 - %2 (pair)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="256"/>
        <source>%1 - %2 (Odd)</source>
        <translation>%1 - %2 (impair)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Black</source>
        <translation>Noir</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Dark Blue</source>
        <translation>Bleu foncé</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Blue</source>
        <translation>Bleu</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="280"/>
        <source>Dark Green</source>
        <translation>Vert foncé</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Dark Cyan</source>
        <translation>Cyan foncé</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Green</source>
        <translation>Vert</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Cyan</source>
        <translation>Cyan</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="281"/>
        <source>Dark Red</source>
        <translation>Rouge foncé</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Dark Magenta</source>
        <translation>Magenta foncé</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Dark Yellow</source>
        <translation>Jaune foncé</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Dark Gray</source>
        <translation>Gris foncé</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="282"/>
        <source>Light Gray</source>
        <translation>Gris clair</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>Red</source>
        <translation>Rouge</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>Magenta</source>
        <translation>Magenta</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>Yellow</source>
        <translation>Jaune</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="283"/>
        <source>White</source>
        <translation>Blanc</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="360"/>
        <source>%1 %2 - %3</source>
        <translation></translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="364"/>
        <source>%1 %2 - %3 (Even)</source>
        <translation>%1 %2 - %3 (pair)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="365"/>
        <source>%1 %2 - %3 (Odd)</source>
        <translation>%1 %2 - %3 (impair)</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="427"/>
        <source> - Even</source>
        <translation> - Pair</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="428"/>
        <source> - Odd</source>
        <translation> - Impair</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="443"/>
        <source> - RGB Group</source>
        <translation> - Groupe RVB</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="449"/>
        <source>Animation %1</source>
        <translation>Animation %1</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="468"/>
        <source>%1 chaser - %2</source>
        <translation>%1 chaser - %2</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="540"/>
        <source>Red scene</source>
        <translation>Scène rouge</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="541"/>
        <source>Green scene</source>
        <translation>Scène verte</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="542"/>
        <source>Blue scene</source>
        <translation>Scène bleue</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="543"/>
        <source>Cyan scene</source>
        <translation>Scène cyan</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="544"/>
        <source>Magenta scene</source>
        <translation>Scène magenta</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="545"/>
        <source>Yellow scene</source>
        <translation>Scène jaune</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="546"/>
        <source>White scene</source>
        <translation>Scène blanche</translation>
    </message>
    <message>
        <location filename="palettegenerator.cpp" line="553"/>
        <location filename="palettegenerator.cpp" line="555"/>
        <source>Scene</source>
        <translation>Scène</translation>
    </message>
</context>
<context>
    <name>PlaybackSlider</name>
    <message>
        <location filename="playbackslider.cpp" line="52"/>
        <source>Select</source>
        <translation>Sélectionner</translation>
    </message>
    <message>
        <location filename="playbackslider.cpp" line="84"/>
        <source>Flash</source>
        <translation>Flash</translation>
    </message>
</context>
<context>
    <name>PositionTool</name>
    <message>
        <location filename="positiontool.ui" line="33"/>
        <source>PositonTool</source>
        <translation>Outil de positionnement</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="app.cpp" line="85"/>
        <source>Operate</source>
        <translation>Production</translation>
    </message>
    <message>
        <location filename="app.cpp" line="86"/>
        <source>Design</source>
        <translation>Création</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixture.cpp" line="277"/>
        <location filename="virtualconsole/vcxypadfixture.cpp" line="352"/>
        <source>Reversed</source>
        <translation>Inversé</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframepageshortcut.cpp" line="49"/>
        <source>Page: %1</source>
        <translation>Page : %1</translation>
    </message>
</context>
<context>
    <name>RGBMatrixEditor</name>
    <message>
        <location filename="rgbmatrixeditor.ui" line="33"/>
        <source>RGB Matrix Editor</source>
        <translation>Éditeur de matrice RVB</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="508"/>
        <source>RGB matrix name</source>
        <translation>Nom de la matrice RVB</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="521"/>
        <source>The name of this RGB matrix function</source>
        <translation>Le nom de la matrice RVB en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="552"/>
        <source>Save this matrix to a sequence</source>
        <translation>Enregistrer cette matrice vers une séquence</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="572"/>
        <source>Toggle between circle and square preview</source>
        <translation>Basculer entre un aperçu en cercles ou en carrés</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="618"/>
        <source>Fixture group</source>
        <translation>Groupe d&apos;appareils</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="631"/>
        <source>The fixture group to use as the pixel matrix</source>
        <translation>Le groupe d&apos;appareils à utiliser comme matrice</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="56"/>
        <source>Pattern</source>
        <translation>Motif</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="98"/>
        <source>The RGB matrix pattern</source>
        <translation>Le motif de la matrice RVB</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="173"/>
        <source>Animated Text</source>
        <translation>Texte animé</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="194"/>
        <source>Text to display</source>
        <translation>Le texte à afficher</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="201"/>
        <source>Choose the font</source>
        <translation>Choisir la police</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="152"/>
        <source>Properties</source>
        <translation>Propriétés</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="221"/>
        <source>Animation style</source>
        <translation>Le style de l&apos;animation</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="231"/>
        <source>Image</source>
        <translation>Image</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="279"/>
        <source>Offset</source>
        <translation>Décalage</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="291"/>
        <source>X</source>
        <translation>X</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="298"/>
        <source>Shift the pattern X pixels horizontally</source>
        <translation>Le décalage horizontal du motif</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="311"/>
        <source>Y</source>
        <translation>Y</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="318"/>
        <source>Shift the pattern Y pixels vertically</source>
        <translation>Le décalage vertical du motif</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="376"/>
        <source>Dimmer control</source>
        <translation>Contrôle du gradateur</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="373"/>
        <source>Set the dimmer channel of fixtures to 100%</source>
        <translation>Passe le canal gradateur des appareils à 100%</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="71"/>
        <source>Matrix end color</source>
        <translation>Couleur de fin de la matrice</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="87"/>
        <source>Reset the end color</source>
        <translation>Réinitialiser la couleur de fin</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="105"/>
        <source>Matrix start color</source>
        <translation>Couleur de départ de la matrice</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="118"/>
        <source>Blend mode</source>
        <translation>Mode de mélange</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="126"/>
        <source>Default (HTP)</source>
        <translation>Par défaut (HTP)</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="131"/>
        <source>Mask</source>
        <translation>Masque</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="136"/>
        <source>Additive</source>
        <translation>Additif</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="141"/>
        <source>Subtractive</source>
        <translation>Soustractif</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="361"/>
        <source>Other Controls</source>
        <translation>Divers</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="452"/>
        <source>Run Order</source>
        <translation>Ordre de lecture</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="464"/>
        <source>Run through over and over again</source>
        <translation>Répéter indéfiniment</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="467"/>
        <source>Loop</source>
        <translation>Boucle</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="477"/>
        <source>Run through once and stop</source>
        <translation>Lire une seule fois</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="480"/>
        <source>Single Shot</source>
        <translation>Unique</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="487"/>
        <source>First run forwards, then backwards, again forwards, etc.</source>
        <translation>Alterner l&apos;ordre de lecture dans les deux directions</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="490"/>
        <source>Ping Pong</source>
        <translation>Ping pong</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="392"/>
        <source>Direction</source>
        <translation>Direction</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="404"/>
        <source>Start from the first step</source>
        <translation>Commencer à partir du premier pas</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="407"/>
        <source>Forward</source>
        <translation>Avant</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="417"/>
        <source>Start from the last step</source>
        <translation>Commencer à partir du dernier pas</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="420"/>
        <source>Backward</source>
        <translation>Arrière</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="532"/>
        <source>Show/Hide speed dial window</source>
        <translation>Afficher/Masquer la fenêtre de réglage des vitesses</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.ui" line="592"/>
        <source>See what the RGB Matrix does when it is run</source>
        <translation>Prévisualiser la matrice RVB</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="273"/>
        <source>None</source>
        <translation>Aucun</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="489"/>
        <source>No fixture group to control</source>
        <translation>Aucun groupe d&apos;appareils à controller</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="766"/>
        <source>Select image</source>
        <translation>Sélectionner une image</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="768"/>
        <source>Images</source>
        <translation>Images</translation>
    </message>
    <message>
        <location filename="rgbmatrixeditor.cpp" line="1015"/>
        <source>Sequence</source>
        <translation>Séquence</translation>
    </message>
</context>
<context>
    <name>SceneEditor</name>
    <message>
        <location filename="sceneeditor.ui" line="39"/>
        <source>Scene editor</source>
        <translation>Éditeur de scène</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="64"/>
        <location filename="sceneeditor.ui" line="67"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="120"/>
        <source>Enable all channel groups</source>
        <translation>Activer tous les groupes de canaux</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="86"/>
        <source>Disable all channel groups</source>
        <translation>Désactiver tous les groupes de canaux</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="162"/>
        <source>Fixtures used in this scene</source>
        <oldsource>Name of this scene</oldsource>
        <translation>Les appareils utilisés dans cette scène</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="209"/>
        <source>Channel groups used in this scene</source>
        <translation>Les groupes de canaux utilisés dans cette scène</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="103"/>
        <source>Add a new fixture to this scene</source>
        <translation>Ajouter un nouvel appareil dans cette scène</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="170"/>
        <source>Remove the selected fixture(s) from this scene</source>
        <translation>Enlever le(s) appareil(s) sélectionné(s) de cette scène</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="241"/>
        <source>Enable all fixtures&apos; channels</source>
        <translation>Activer tous les canaux des appareils</translation>
    </message>
    <message>
        <location filename="sceneeditor.ui" line="224"/>
        <source>Disable all fixtures&apos; channels</source>
        <translation>Désactiver tous les canaux des appareils</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="189"/>
        <source>Enable all channels in current fixture</source>
        <translation>Activer tous les canaux des appareils actuel</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="191"/>
        <source>Disable all channels in current fixture</source>
        <translation>Désactiver tous les canaux de l&apos;appareil actuel</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="193"/>
        <source>Copy current values to clipboard</source>
        <translation>Copier les valeurs actuelles dans le presse-papier</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="195"/>
        <source>Paste clipboard values to current fixture</source>
        <translation>Coller les valeurs du presse-papier vers cet appareil</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="197"/>
        <source>Copy current values to all fixtures</source>
        <translation>Copier les valeurs actuelles vers tous les appareils</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="199"/>
        <source>Color tool for CMY/RGB-capable fixtures</source>
        <translation>Outil de couleur pour les appareils à composantes CMJ/RVB</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="201"/>
        <source>Position tool for moving heads/scanners</source>
        <translation>Outil de positionnement pour les lyres/scanners</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="203"/>
        <source>Switch between tab view and all channels view</source>
        <translation>Basculer entre la vue par onglets et la vue de tous les canaux</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="205"/>
        <source>Toggle blind mode</source>
        <translation>Basculer le mode aveugle</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="207"/>
        <source>Show/Hide speed dial window</source>
        <translation>Afficher/Masquer la fenêtre de réglage des vitesses</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="209"/>
        <source>Clone this scene and append as a new step to the selected chaser</source>
        <translation>Cloner cette scène et l&apos;ajouter comme nouveau pas dans le chaser sélectionné</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="211"/>
        <source>Go to next fixture tab</source>
        <translation>Aller à l&apos;appareil suivant</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="215"/>
        <source>Go to previous fixture tab</source>
        <translation>Aller à l&apos;appareil précédent</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="238"/>
        <source>None</source>
        <translation>Aucun</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="250"/>
        <source>Scene name:</source>
        <translation>Nom de la scène :</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="925"/>
        <location filename="sceneeditor.cpp" line="926"/>
        <source>All fixtures</source>
        <translation>Tous les appareils</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1458"/>
        <location filename="sceneeditor.cpp" line="1459"/>
        <source>Channels Groups</source>
        <translation>Groupes de canaux</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1235"/>
        <location filename="sceneeditor.cpp" line="1236"/>
        <source>Generic</source>
        <translation>Générique</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1303"/>
        <source>Remove fixtures</source>
        <translation>Enlever les appareils</translation>
    </message>
    <message>
        <location filename="sceneeditor.cpp" line="1304"/>
        <source>Do you want to remove the selected fixture(s)?</source>
        <translation>Voulez-vous enlever le(s) appareil(s) sélectionné(s)&#xa0;?</translation>
    </message>
</context>
<context>
    <name>ScriptEditor</name>
    <message>
        <location filename="scripteditor.ui" line="33"/>
        <source>Script editor</source>
        <translation>Editeur de script</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="172"/>
        <source>Test the execution of this script</source>
        <translation>Tester l&apos;exécution du script</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="192"/>
        <source>Script name</source>
        <translation>Nom du script</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="52"/>
        <source>Add new command to cursor position</source>
        <translation>Ajouter une nouvelle commande à la position actuelle</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="82"/>
        <source>Cut selected text to clipboard</source>
        <translation>Couper le texte sélectionné vers le presse-papier</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="99"/>
        <source>Copy selected text to clipboard</source>
        <translation>Copier le texte sélectionné dans le presse-papier</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="116"/>
        <source>Paste text from clipboard at cursor</source>
        <translation>Coller le texte du presse-papier à la position actuelle</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="140"/>
        <source>Undo</source>
        <translation>Annuler</translation>
    </message>
    <message>
        <location filename="scripteditor.ui" line="211"/>
        <source>Check the syntax of this script</source>
        <translation>Vérifier la syntaxe du script</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="92"/>
        <source>Start Function</source>
        <translation>Démarrer une fonction</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="96"/>
        <source>Stop Function</source>
        <translation>Arrêter une fonction</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="100"/>
        <source>Blackout</source>
        <translation>Black-out</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="104"/>
        <source>Wait</source>
        <translation>Attendre</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="108"/>
        <source>Wait Key</source>
        <translation>Attendre une touche</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="112"/>
        <source>Set HTP</source>
        <translation>Passer en HTP</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="116"/>
        <source>Set LTP</source>
        <translation>Passer en LTP</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="120"/>
        <source>Set Fixture</source>
        <translation>Modifier un appareil</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="124"/>
        <source>System Command</source>
        <translation>Commande système</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="128"/>
        <source>Comment</source>
        <translation>Commentaire</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="132"/>
        <source>Random Number</source>
        <translation>Nombre aléatoire</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="136"/>
        <source>File Path</source>
        <translation>Chemin du fichier</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="163"/>
        <source>Open Executable File</source>
        <translation>Ouvrir un fichier exécutable</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="168"/>
        <source>All Files (*.*)</source>
        <translation>Tous les fichiers (*.*)</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="170"/>
        <source>All Files (*)</source>
        <translation>Tous les fichiers (*)</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="273"/>
        <source>Blackout state</source>
        <translation>État du black-out</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="300"/>
        <source>Enter the desired time</source>
        <translation>Renseignez le temps désiré</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="384"/>
        <source>Invalid executable</source>
        <translation>Exécutable invalide</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="384"/>
        <source>Please select an executable file!</source>
        <translation>Veuillez sélectioner un fichier exécutable&#xa0;!</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="390"/>
        <source>Enter the program arguments (leave empty if not required)</source>
        <translation>Entrer les paramètres du programme (laisser vide si non requis)</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="426"/>
        <source>Enter the range for the randomization</source>
        <translation>Entrer l&apos;intervalle du nombre aléatoire</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="433"/>
        <source>Minimum value</source>
        <translation>Valeur minimale</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="434"/>
        <source>Maximum value</source>
        <translation>valeur maximale</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="474"/>
        <source>No syntax errors found in the script</source>
        <translation>Aucune erreur de syntaxe trouvée dans le script</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="481"/>
        <source>Syntax error at line %1:
%2

</source>
        <translation>Erreur de syntaxe à la ligne %1 :
%2

</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="484"/>
        <source>Script check results</source>
        <translation>Résultats de la vérification du script</translation>
    </message>
    <message>
        <location filename="scripteditor.cpp" line="410"/>
        <source>Add Comment</source>
        <translation>Ajouter un commentaire</translation>
    </message>
</context>
<context>
    <name>SelectInputChannel</name>
    <message>
        <location filename="selectinputchannel.ui" line="33"/>
        <source>Select input channel</source>
        <translation>Sélectionner le canal d&apos;entrée</translation>
    </message>
    <message>
        <location filename="selectinputchannel.ui" line="43"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="selectinputchannel.ui" line="51"/>
        <source>Allow unpatched universes</source>
        <translation>Autoriser les univers non patchés</translation>
    </message>
    <message>
        <location filename="selectinputchannel.cpp" line="210"/>
        <source>&lt;Double click here to enter channel number manually&gt;</source>
        <translation>&lt;Double-cliquer ici pour entrer le numéro du canal manuellement&gt;</translation>
    </message>
</context>
<context>
    <name>ShowEditor</name>
    <message>
        <location filename="showmanager/showeditor.ui" line="33"/>
        <source>Show editor</source>
        <translation>Editeur de show</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="45"/>
        <source>Show name</source>
        <translation>Nom du show</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="52"/>
        <source>Name of the function being edited</source>
        <translation>Le nom du show en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="72"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="77"/>
        <source>Steps</source>
        <translation>Pas</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="85"/>
        <source>Start Time</source>
        <translation>Début</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="93"/>
        <source>Duration</source>
        <translation>Durée</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="104"/>
        <source>Add function(s) to the collection</source>
        <translation>Ajouter des fonctions à la collection</translation>
    </message>
    <message>
        <location filename="showmanager/showeditor.ui" line="124"/>
        <source>Remove the selected function</source>
        <translation>Enlever la fonction sélectionnée</translation>
    </message>
</context>
<context>
    <name>ShowItem</name>
    <message>
        <location filename="showmanager/showitem.cpp" line="53"/>
        <source>Align to cursor</source>
        <translation>Aligner sur le curseur</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="56"/>
        <location filename="showmanager/showitem.cpp" line="85"/>
        <location filename="showmanager/showitem.cpp" line="261"/>
        <source>Lock item</source>
        <translation>Verrouiller l&apos;élément</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="66"/>
        <source>Name: %1
Start time: %2
Duration: %3
%4</source>
        <translation>Nom : %1
Début : %2
Durée : %3
%4</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="70"/>
        <source>Click to move this item along the timeline</source>
        <oldsource>Click to move this item across the timeline</oldsource>
        <translation>Cliquer pour déplacer cet élément à travers la timeline</translation>
    </message>
    <message>
        <location filename="showmanager/showitem.cpp" line="80"/>
        <location filename="showmanager/showitem.cpp" line="256"/>
        <source>Unlock item</source>
        <translation>Déverrouiller l&apos;élément</translation>
    </message>
</context>
<context>
    <name>ShowManager</name>
    <message>
        <location filename="showmanager/showmanager.cpp" line="200"/>
        <source>New s&amp;how</source>
        <translation>Nouveau s&amp;how</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="212"/>
        <source>New s&amp;equence</source>
        <translation>Nouvelle séqu&amp;ence</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="218"/>
        <source>New &amp;audio</source>
        <translation>Nouveau son (&amp;A)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="225"/>
        <source>New vi&amp;deo</source>
        <translation>Nouvelle vi&amp;déo</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="232"/>
        <source>&amp;Copy</source>
        <translation>&amp;Copier</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="239"/>
        <source>&amp;Paste</source>
        <translation>Coller (&amp;P)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="246"/>
        <source>&amp;Delete</source>
        <translation>Supprimer (&amp;D)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="253"/>
        <source>Change Co&amp;lor</source>
        <translation>Cou&amp;leur</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="274"/>
        <source>Snap to &amp;Grid</source>
        <translation>&amp;Grille magnétique</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="281"/>
        <source>St&amp;op</source>
        <translation>St&amp;op</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="287"/>
        <source>&amp;Play</source>
        <translation>Lecture (&amp;P)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="348"/>
        <source>Time division:</source>
        <translation>Division temporelle :</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="353"/>
        <source>Time</source>
        <translation>Temps</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="569"/>
        <source>New Show</source>
        <translation>Nouveau show</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="570"/>
        <source>Show name setup</source>
        <translation>Définir le nom du show</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="571"/>
        <source>Show name:</source>
        <translation>Nom du show :</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="206"/>
        <source>Add a &amp;track or an existing function</source>
        <translation>Ajouter une pis&amp;te ou une fonction existante</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="260"/>
        <source>Lock item</source>
        <translation>Verrouiller l&apos;élément</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="267"/>
        <source>Item start time and duration</source>
        <translation>Début et durée de l&apos;élément</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="695"/>
        <location filename="showmanager/showmanager.cpp" line="813"/>
        <source> (Copy)</source>
        <translation> (copie)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="766"/>
        <source>Track %1</source>
        <translation>Piste %1</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="795"/>
        <location filename="showmanager/showmanager.cpp" line="885"/>
        <source>New Sequence</source>
        <translation>Nouvelle séquence</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="863"/>
        <location filename="showmanager/showmanager.cpp" line="940"/>
        <location filename="showmanager/showmanager.cpp" line="999"/>
        <source>Overlapping error</source>
        <translation>Erreur de recouvrement</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="863"/>
        <location filename="showmanager/showmanager.cpp" line="940"/>
        <location filename="showmanager/showmanager.cpp" line="999"/>
        <source>Overlapping not allowed. Operation canceled.</source>
        <translation>Le recouvrement n&apos;est pas permis. Opération annulée.</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="873"/>
        <source>Scene for %1 - Track %2</source>
        <translation>Scène pour %1 - Piste %2</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="898"/>
        <source>Open Audio File</source>
        <translation>Ouvrir un fichier audio</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="907"/>
        <source>Audio Files (%1)</source>
        <translation>Fichiers audio (%1)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="909"/>
        <location filename="showmanager/showmanager.cpp" line="968"/>
        <source>All Files (*.*)</source>
        <translation>Tous les fichiers (*.*)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="911"/>
        <location filename="showmanager/showmanager.cpp" line="970"/>
        <source>All Files (*)</source>
        <translation>Tous les fichiers (*)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="933"/>
        <source>Unsupported audio file</source>
        <translation>Fichier audio non pris en charge</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="933"/>
        <source>This audio file cannot be played with QLC+. Sorry.</source>
        <translation>Ce fichier audio ne peut pas être lu par QLC+, désolé.</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="957"/>
        <source>Open Video File</source>
        <translation>Ouvrir un fichier vidéo</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="966"/>
        <source>Video Files (%1)</source>
        <translation>Fichiers vidéo (%1)</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="992"/>
        <source>Unsupported video file</source>
        <translation>Fichier vidéo non pris en charge</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="992"/>
        <source>This video file cannot be played with QLC+. Sorry.</source>
        <translation>Ce fichier vidéo ne peut pas être lu par QLC+, désolé.</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1035"/>
        <location filename="showmanager/showmanager.cpp" line="1089"/>
        <source>Paste error</source>
        <translation>Erreur de collage</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1035"/>
        <source>Overlapping paste not allowed. Operation canceled.</source>
        <translation>Le recouvrement par collage n&apos;est pas permis. Opération annulée.</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1089"/>
        <source>Trying to paste on an incompatible Scene. Operation canceled.</source>
        <translation>La scène vers laquelle coller est incompatible. Opération annulée.</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1414"/>
        <source>Track name setup</source>
        <translation>Définir le nom de la piste</translation>
    </message>
    <message>
        <location filename="showmanager/showmanager.cpp" line="1415"/>
        <source>Track name:</source>
        <translation>Nom de la piste :</translation>
    </message>
</context>
<context>
    <name>SimpleDesk</name>
    <message>
        <location filename="simpledesk.cpp" line="247"/>
        <source>Universe</source>
        <translation>Univers</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="234"/>
        <source>Next page</source>
        <translation>Page suivante</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="226"/>
        <source>Current page</source>
        <translation>Page actuelle</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="218"/>
        <source>Previous page</source>
        <translation>Page précédente</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="209"/>
        <source>View mode</source>
        <translation>Type de vue</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="242"/>
        <source>Reset universe</source>
        <translation>Réinitialiser l&apos;univers</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="287"/>
        <source>Playback</source>
        <translation>Faders de lancement</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="284"/>
        <location filename="simpledesk.cpp" line="294"/>
        <source>Cue Stack</source>
        <translation>Pile de mémoires</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="304"/>
        <source>Previous cue</source>
        <translation>Mémoire précédente</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="310"/>
        <source>Stop cue stack</source>
        <translation>Arrêter la pile de mémoires</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="316"/>
        <source>Next cue</source>
        <translation>Mémoire suivante</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="324"/>
        <source>Clone cue stack</source>
        <translation>Cloner la pile de mémoires</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="330"/>
        <source>Edit cue stack</source>
        <translation>Éditer la pile de mémoires</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="337"/>
        <source>Record cue</source>
        <translation>Enregistrer une mémoire</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="558"/>
        <source>Channel groups</source>
        <translation>Groupes de canaux</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1049"/>
        <source>Cue Stack - Playback %1</source>
        <translation>Pile de mémoires - Fader %1</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1237"/>
        <source>No selection</source>
        <translation>Aucune sélection</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1260"/>
        <source>Cue name</source>
        <translation>Nom de la mémoire</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1267"/>
        <source>Multiple Cues</source>
        <translation>Mémoires multiples</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1364"/>
        <source>Delete cue</source>
        <translation>Supprimer la mémoire</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1418"/>
        <source>Clone Cue Stack</source>
        <translation>Cloner la pile de mémoires</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1418"/>
        <source>Clone To Playback#</source>
        <translation>Cloner vers le fader</translation>
    </message>
    <message>
        <location filename="simpledesk.cpp" line="1484"/>
        <source>Cue %1</source>
        <translation>Mémoire %1</translation>
    </message>
</context>
<context>
    <name>SpeedDial</name>
    <message>
        <location filename="speeddial.cpp" line="144"/>
        <source>Hours</source>
        <translation>Heures</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="153"/>
        <source>Minutes</source>
        <translation>Minutes</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="162"/>
        <source>Seconds</source>
        <translation>Secondes</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="171"/>
        <source>Milliseconds</source>
        <translation>Millisecondes</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="177"/>
        <source>Infinite</source>
        <translation>Infini</translation>
    </message>
    <message>
        <location filename="speeddial.cpp" line="129"/>
        <source>Tap</source>
        <translation>Tap</translation>
    </message>
</context>
<context>
    <name>SpeedDialWidget</name>
    <message>
        <location filename="speeddialwidget.cpp" line="61"/>
        <source>Fade In</source>
        <translation>Montée</translation>
    </message>
    <message>
        <location filename="speeddialwidget.cpp" line="67"/>
        <source>Fade Out</source>
        <translation>Descente</translation>
    </message>
    <message>
        <location filename="speeddialwidget.cpp" line="73"/>
        <source>Hold</source>
        <translation>Maintien</translation>
    </message>
</context>
<context>
    <name>TimingsTool</name>
    <message>
        <location filename="showmanager/timingstool.cpp" line="50"/>
        <source>Start Time</source>
        <translation>Début</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="61"/>
        <source>Duration</source>
        <translation>Durée</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="68"/>
        <source>Duration options</source>
        <translation>Options de durée</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="70"/>
        <source>Stretch the original function duration</source>
        <translation>Étendre la durée de la fonction d&apos;origine</translation>
    </message>
    <message>
        <location filename="showmanager/timingstool.cpp" line="71"/>
        <source>Loop function until duration is reached</source>
        <translation>Répéter la fonction jusqu&apos;à ce que sa durée soit écoulée</translation>
    </message>
</context>
<context>
    <name>TrackItem</name>
    <message>
        <location filename="showmanager/trackitem.cpp" line="55"/>
        <source>Move up</source>
        <translation>Monter</translation>
    </message>
    <message>
        <location filename="showmanager/trackitem.cpp" line="58"/>
        <source>Move down</source>
        <translation>Descendre</translation>
    </message>
    <message>
        <location filename="showmanager/trackitem.cpp" line="62"/>
        <source>Change name</source>
        <translation>Renommer</translation>
    </message>
    <message>
        <location filename="showmanager/trackitem.cpp" line="66"/>
        <source>Delete</source>
        <translation>Supprimer</translation>
    </message>
</context>
<context>
    <name>UniverseItemWidget</name>
    <message>
        <location filename="universeitemwidget.cpp" line="86"/>
        <source>Input:</source>
        <translation>Entrée :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="87"/>
        <source>Profile:</source>
        <translation>Profil :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="88"/>
        <source>Output:</source>
        <translation>Sortie :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="89"/>
        <source>Feedback:</source>
        <translation>Retour d&apos;info :</translation>
    </message>
    <message>
        <location filename="universeitemwidget.cpp" line="112"/>
        <location filename="universeitemwidget.cpp" line="114"/>
        <location filename="universeitemwidget.cpp" line="116"/>
        <location filename="universeitemwidget.cpp" line="118"/>
        <source>None</source>
        <translation>Aucun</translation>
    </message>
</context>
<context>
    <name>VCButton</name>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="93"/>
        <source>Choose...</source>
        <translation>Choisir...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="97"/>
        <source>None</source>
        <translation>Aucun</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="135"/>
        <source>Button %1</source>
        <translation>Bouton %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="313"/>
        <source>Select button icon</source>
        <translation>Sélectionner l&apos;icône du bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="314"/>
        <source>Images (%1)</source>
        <translation>Images (%1)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="579"/>
        <source>Toggle Blackout</source>
        <translation>Blackout</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="581"/>
        <source>Stop ALL functions!</source>
        <translation>Arrêter TOUTES les fonctions&#xa0;!</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbutton.cpp" line="820"/>
        <source>Icon</source>
        <translation>Icône</translation>
    </message>
</context>
<context>
    <name>VCButtonProperties</name>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="33"/>
        <source>Button properties</source>
        <translation>Propriétés du bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="219"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="225"/>
        <source>Button label</source>
        <translation>Nom du bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="232"/>
        <source>Text to display on the button</source>
        <translation>Le texte à afficher sur le bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="239"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="246"/>
        <source>The function that this button controls</source>
        <translation>La fonction que ce bouton contrôle</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="256"/>
        <source>Attach a function to this button</source>
        <translation>Attacher une fonction au bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="276"/>
        <source>Detach the button&apos;s function attachment</source>
        <translation>Détacher la fonction liée au bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="68"/>
        <source>Toggle Blackout</source>
        <translation>Blackout</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="83"/>
        <source>Stop All Functions</source>
        <translation>Arrêter toutes les fonctions</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="90"/>
        <source>Fade time:</source>
        <translation>Temps de fondu :</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="145"/>
        <source>Adjust function intensity when it is running</source>
        <translation>Ajuster l&apos;intensité de la fonction durant son exécution</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="148"/>
        <source>Adjust Function Intensity</source>
        <translation>Ajuster l&apos;intensité de la fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="163"/>
        <source>Function&apos;s adjusted intensity percentage when run</source>
        <translation>Ajustement de l&apos;intensité en pourcentage de la fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="39"/>
        <source>On button press...</source>
        <translation>Lorsque le bouton est pressé...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="45"/>
        <source>Toggle the assigned function on/off with this button</source>
        <translation>Bascule on/off de la fonction sélectionée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="48"/>
        <source>Toggle function on/off</source>
        <translation>Interrupteur (Toggle)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="58"/>
        <source>Flash the assigned function with this button</source>
        <translation>Lire la fonction attachée au bouton lorsqu&apos;il est pressé</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.ui" line="61"/>
        <source>Flash function (only for scenes)</source>
        <translation>Flash (uniquement pour les scènes)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcbuttonproperties.cpp" line="133"/>
        <source>No function</source>
        <translation>Aucune fonction</translation>
    </message>
</context>
<context>
    <name>VCClockProperties</name>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="33"/>
        <source>Clock properties</source>
        <translation>Propriétés de l&apos;horloge</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="47"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="53"/>
        <source>Clock type</source>
        <translation>Type de l&apos;horloge</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="59"/>
        <source>Stopwatch</source>
        <translation>Chronomètre</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="66"/>
        <source>h</source>
        <translation> h</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="76"/>
        <source>Countdown</source>
        <translation>Minuteur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="83"/>
        <source>m</source>
        <translation> min</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="93"/>
        <source>s</source>
        <translation> s</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="109"/>
        <source>Clock</source>
        <translation>Horloge</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="122"/>
        <source>Schedule</source>
        <translation>Planificateur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="188"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="193"/>
        <source>Time</source>
        <translation>Temps</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="205"/>
        <source>Input</source>
        <translation>Entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.ui" line="214"/>
        <source>No input control available for Clock type</source>
        <translation>Aucun contrôle d&apos;entrée pour le type d&apos;horloge</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.cpp" line="39"/>
        <source>Play/Pause control</source>
        <translation>Contrôle de lecture/pause</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcclockproperties.cpp" line="48"/>
        <source>Reset control</source>
        <translation>Réinitialiser le contrôle</translation>
    </message>
</context>
<context>
    <name>VCCueList</name>
    <message>
        <source>Blend</source>
        <translation type="vanished">Mélanger</translation>
    </message>
    <message>
        <source>Link</source>
        <translation type="vanished">Lien</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="210"/>
        <source>Show/Hide crossfade sliders</source>
        <translation>Afficher/Masquer les crossfaders</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="221"/>
        <location filename="virtualconsole/vccuelist.cpp" line="1036"/>
        <source>Play/Pause Cue list</source>
        <translation>Lecture/pause de la liste de cue</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="230"/>
        <location filename="virtualconsole/vccuelist.cpp" line="1037"/>
        <source>Stop Cue list</source>
        <translation>Arrêt de la liste de cue</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="239"/>
        <source>Go to previous step in the list</source>
        <translation>Aller au pas précédent</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="248"/>
        <source>Go to next step in the list</source>
        <translation>Aller au pas suivant</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="257"/>
        <source>Cue list</source>
        <translation>Séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1030"/>
        <source>Play/Stop Cue list</source>
        <translation>Lecture/arrêt de la liste de cue</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1031"/>
        <source>Pause Cue list</source>
        <translation>Mettre la liste de cue en pause</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Fade In</source>
        <translation>Montée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Fade Out</source>
        <translation>Descente</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Duration</source>
        <translation>Durée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelist.cpp" line="1402"/>
        <source>Notes</source>
        <translation>Notes</translation>
    </message>
</context>
<context>
    <name>VCCueListProperties</name>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="33"/>
        <source>Cue list properties</source>
        <translation>Propriétés du séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="39"/>
        <source>Cue list name</source>
        <translation>Nom du séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="56"/>
        <source>The name of the cue list widget</source>
        <translation>Le nom du séquenceur en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="70"/>
        <source>Cue List</source>
        <oldsource>Cues</oldsource>
        <translation>Séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="120"/>
        <source>Chaser</source>
        <translation>Chaser</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="76"/>
        <source>The chaser function to use as cue list</source>
        <translation>Le chaser à utiliser en tant que séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="86"/>
        <source>Choose the chaser function to use as the steps for the cue list</source>
        <translation>Définir le chaser à utiliser comme pas pour le séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="103"/>
        <source>Detach current chaser from the cue list</source>
        <translation>Détacher le chaser actuel du séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="127"/>
        <location filename="virtualconsole/vccuelistproperties.ui" line="134"/>
        <source>Behavior of the Next/Previous buttons when the chaser is not active</source>
        <translation>Comportement des boutons Suivant et Précédent lorsque le chaser est inactif</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="141"/>
        <source>Run chaser from first/last step (default)</source>
        <translation>Démarrer le chaser depuis le premier/dernier pas</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="146"/>
        <source>Run chaser from next/previous step</source>
        <translation>Démarrer le chaser depuis le pas suivant/précédent</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="151"/>
        <source>Select next/previous step</source>
        <translation>Sélectionner le pas suivant/précédent</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="156"/>
        <source>Do nothing</source>
        <translation>Ne rien faire</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="177"/>
        <source>Playback buttons layout</source>
        <translation>Disposition des boutons de lecture</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="183"/>
        <source>Play/Pause + Stop</source>
        <translation>Lecture/pause + arrêt</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="193"/>
        <source>Play/Stop + Pause</source>
        <translation>Lecture/arrêt + pause</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="204"/>
        <source>Playback</source>
        <translation>Lancement</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="230"/>
        <source>Next Cue</source>
        <translation>Pas suivant</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="276"/>
        <source>Side Fader</source>
        <oldsource>Side Faders</oldsource>
        <translation>Faders sur le flanc</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="253"/>
        <source>Previous Cue</source>
        <translation>Pas précédent</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="282"/>
        <source>Behaviour</source>
        <translation>Comportement</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="294"/>
        <source>None</source>
        <translation>Aucun</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="304"/>
        <source>Crossfade</source>
        <translation>Crossfaders</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.ui" line="314"/>
        <source>Steps</source>
        <translation>Pas</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="67"/>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="215"/>
        <source>Play/Pause control</source>
        <translation>Contrôle de lecture/pause</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="76"/>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="216"/>
        <source>Stop control</source>
        <translation>Contrôle d&apos;arrêt</translation>
    </message>
    <message>
        <source>Left Fader</source>
        <translation type="vanished">Fader de gauche</translation>
    </message>
    <message>
        <source>Right Fader</source>
        <translation type="vanished">Fader de droite</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="118"/>
        <source>External Input</source>
        <translation>Entrée externe</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="220"/>
        <source>Play/Stop control</source>
        <translation>Contrôle de lecture/arrêt</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="221"/>
        <source>Pause control</source>
        <translation>Contrôle de pause</translation>
    </message>
    <message>
        <location filename="virtualconsole/vccuelistproperties.cpp" line="229"/>
        <source>No function</source>
        <translation>Aucune fonction</translation>
    </message>
</context>
<context>
    <name>VCFrame</name>
    <message>
        <location filename="virtualconsole/vcframe.cpp" line="1449"/>
        <source>Add</source>
        <translation>Ajouter</translation>
    </message>
</context>
<context>
    <name>VCFrameProperties</name>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="33"/>
        <source>Frame Properties</source>
        <translation>Propriétés du cadre</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="41"/>
        <source>Frame name</source>
        <translation>Nom du cadre</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="57"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="63"/>
        <source>Appearance</source>
        <translation>Apparence</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="75"/>
        <source>Accept child widgets</source>
        <translation>Accepter les widgets enfants</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="88"/>
        <source>Allow resizing</source>
        <translation>Autoriser le redimensionnement</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="101"/>
        <source>Show header</source>
        <translation>Afficher l&apos;en-tête</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="114"/>
        <source>Show enable button</source>
        <translation>Afficher le bouton d&apos;activation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="210"/>
        <source>Page Name</source>
        <translation>Nom de la page</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.cpp" line="63"/>
        <source>External Input - Enable</source>
        <translation>Entrée externe - Activation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.cpp" line="76"/>
        <source>External Input - Previous Page</source>
        <translation>Entrée Externe - Page Précédente</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.cpp" line="89"/>
        <source>External Input - Next Page</source>
        <translation>Entrée Externe - Page Suivante</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="141"/>
        <source>Pages</source>
        <translation>Pages</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="147"/>
        <source>Enable</source>
        <translation>Activé</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="180"/>
        <source>Clone first page widgets</source>
        <translation>Cloner les widgets de la première page</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="156"/>
        <source>Pages circular scrolling</source>
        <translation>Navigation circulaire des pages</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcframeproperties.ui" line="173"/>
        <source>Number of pages:</source>
        <translation>Nombre de pages :</translation>
    </message>
</context>
<context>
    <name>VCLabel</name>
    <message>
        <location filename="virtualconsole/vclabel.cpp" line="46"/>
        <source>Label</source>
        <translation>Étiquette</translation>
    </message>
    <message>
        <location filename="virtualconsole/vclabel.cpp" line="79"/>
        <source>Rename Label</source>
        <translation>Renommer l&apos;étiquette</translation>
    </message>
    <message>
        <location filename="virtualconsole/vclabel.cpp" line="79"/>
        <source>Caption:</source>
        <translation>Légende :</translation>
    </message>
</context>
<context>
    <name>VCMatrix</name>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="173"/>
        <source>Animation %1</source>
        <translation>Animation %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="634"/>
        <source>End Color Reset</source>
        <translation>Réinitialiser la couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="673"/>
        <source>Start color Red component</source>
        <translation>Couleur de départ : composante rouge</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="675"/>
        <source>Start color Green component</source>
        <translation>Couleur de départ : composante verte</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="677"/>
        <source>Start color Blue component</source>
        <translation>Couleur de départ : composante bleue</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="689"/>
        <source>End color Red component</source>
        <translation>Couleur de fin : composante rouge</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="691"/>
        <source>End color Green component</source>
        <translation>Couleur de fin : composante verte</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrix.cpp" line="693"/>
        <source>End color Blue component</source>
        <translation>Couleur de fin : composante bleue</translation>
    </message>
</context>
<context>
    <name>VCMatrixPresetSelection</name>
    <message>
        <location filename="virtualconsole/vcmatrixpresetselection.ui" line="33"/>
        <source>Select an animation preset</source>
        <translation>Sélectionner un préréglage d&apos;animation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixpresetselection.ui" line="51"/>
        <source>Pattern</source>
        <translation>Motif</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixpresetselection.ui" line="69"/>
        <source>Properties</source>
        <translation>Propriétés</translation>
    </message>
</context>
<context>
    <name>VCMatrixProperties</name>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="33"/>
        <source>Animation widget properties</source>
        <oldsource>RGB Matrix properties</oldsource>
        <translation>Propriétés du widget d&apos;animation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="57"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="65"/>
        <source>The function that this widget controls</source>
        <oldsource>The function that this matrix controls</oldsource>
        <translation>La fonction que ce widget contrôle</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="75"/>
        <source>Widget name</source>
        <translation>Nom du widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="82"/>
        <source>RGB Matrix Function</source>
        <oldsource>Matrix Function</oldsource>
        <translation>Fonction de matrice RVB</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="89"/>
        <source>Detach the matrix function attachment</source>
        <oldsource>Detach the button&apos;s function attachment</oldsource>
        <translation>Détacher la fonction contrôlée par ce widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="109"/>
        <source>Attach a function to this widget</source>
        <oldsource>Attach a function to this button</oldsource>
        <translation>Attacher une fonction à ce widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="129"/>
        <source>Text to display on the widget</source>
        <oldsource>Text to display on the button</oldsource>
        <translation>Le texte à afficher sur le widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="136"/>
        <source>Apply color and preset changes immediately</source>
        <translation>Appliquer immédiatement les changements de couleur et préréglage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="143"/>
        <source>Show Label</source>
        <translation>Afficher l&apos;étiquette</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="150"/>
        <source>Show Slider</source>
        <translation>Afficher le fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="157"/>
        <source>Show Start Color Button</source>
        <translation>Afficher le bouton de couleur de départ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="164"/>
        <source>Show End Color Button</source>
        <translation>Afficher le bouton de couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="171"/>
        <source>Show Preset Combo</source>
        <translation>Afficher la liste déroulante de préréglages</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="180"/>
        <source>Slider External Input</source>
        <translation>Entrée externe du fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="186"/>
        <source>Input universe</source>
        <translation>Univers d&apos;entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="193"/>
        <source>The input universe that sends data to this widget</source>
        <translation>L&apos;univers d&apos;entrée qui contrôle ce widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="203"/>
        <source>Input channel</source>
        <translation>Canal d&apos;entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="210"/>
        <source>The particular input channel within the input universe that sends data to this widget</source>
        <translation>Le canal de l&apos;univers d&apos;entrée qui contrôle ce widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="233"/>
        <source>Choose an external input universe &amp; channel that this widget should listen to</source>
        <translation>Choisir l&apos;univers d&apos;entrée et son canal qui contrôlera ce widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="236"/>
        <source>Choose...</source>
        <translation>Choisir...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="243"/>
        <source>When toggled, you can move an external slider/knob to assign it to the animation widget slider.</source>
        <oldsource>When toggled, you can move an external slider/knob to assign it to this virtual console slider.</oldsource>
        <translation>Lorsqu&apos;elle est activée, bougez un fader ou un bouton rotatif externe afin de l&apos;assigner à ce fader de widget d&apos;animation.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="246"/>
        <source>Auto Detect</source>
        <translation>Auto-détection</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="273"/>
        <source>Custom Controls</source>
        <translation>Réglages personnalisés</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="370"/>
        <source> Add start color knobs</source>
        <translation> Ajouter des potards de couleur de départ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="281"/>
        <source> Add end color knobs</source>
        <translation> Ajouter des potards de couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="337"/>
        <source> Add end color reset</source>
        <translation> Ajouter un bouton de réinitialisation de couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="348"/>
        <source> Remove</source>
        <translation> Enlever</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="312"/>
        <source> Add preset</source>
        <translation> Ajouter un bouton de préréglage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="299"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="304"/>
        <source>Value</source>
        <translation>Valeur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="359"/>
        <source> Add start color</source>
        <translation> Ajouter un bouton de couleur de départ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="381"/>
        <source> Add end color</source>
        <translation> Ajouter un bouton de couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.ui" line="323"/>
        <source> Add text</source>
        <translation>Ajouter du texte</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="152"/>
        <source>No function</source>
        <translation>Aucune fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="228"/>
        <source>Start Color</source>
        <translation>Couleur de départ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="234"/>
        <source>Start Color Knob</source>
        <translation>Potard de couleur de début</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="240"/>
        <source>End Color</source>
        <translation>Couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="246"/>
        <source>End Color Knob</source>
        <translation>Potard de couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="252"/>
        <source>End Color Reset</source>
        <translation>Réinitialisation de la couleur de fin</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="257"/>
        <source>Animation</source>
        <translation>Animation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="279"/>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="411"/>
        <source>Text</source>
        <translation>Texte</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcmatrixproperties.cpp" line="410"/>
        <source>Enter a text</source>
        <translation>Saisir le texte</translation>
    </message>
</context>
<context>
    <name>VCPropertiesEditor</name>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="33"/>
        <source>Virtual Console Settings</source>
        <oldsource>Virtual Console properties</oldsource>
        <translation>Propriétés de la console virtuelle</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="62"/>
        <location filename="virtualconsole/vcproperties.ui" line="88"/>
        <source>Widget grid layout X resolution</source>
        <translation>Définition en pixels de la grille</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="761"/>
        <source>Choose...</source>
        <translation>Choisir...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="43"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="49"/>
        <source>Virtual Console Size</source>
        <translation>Taille de la grille</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="55"/>
        <source>Width</source>
        <translation>Largeur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="81"/>
        <source>Height</source>
        <translation>Hauteur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="124"/>
        <source>Widgets</source>
        <translation>Widgets</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="136"/>
        <source>Widgets default properties</source>
        <translation>Propriétés par défaut</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="512"/>
        <source>Button size</source>
        <oldsource>Button size:</oldsource>
        <translation>Taille des boutons</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="243"/>
        <source>Solo frame size</source>
        <oldsource>Solo frame size:</oldsource>
        <translation>Taille des cadres de solos</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="222"/>
        <source>Slider size</source>
        <oldsource>Slider size:</oldsource>
        <translation>Taille des faders</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="545"/>
        <source>Speed dial size</source>
        <oldsource>Speed dial size:</oldsource>
        <translation>Taille des contrôleurs de vitesse</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="158"/>
        <source>XY Pad size</source>
        <oldsource>XY Pad size:</oldsource>
        <translation>Taille des pads XY</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="519"/>
        <source>Cue List size</source>
        <oldsource>Cue List size:</oldsource>
        <translation>Taille des séquenceurs</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="328"/>
        <source>Frame size</source>
        <oldsource>Frame size:</oldsource>
        <translation>Taille des cadres</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="165"/>
        <source>Speed dial value</source>
        <oldsource>Speed dial value:</oldsource>
        <translation>Valeur des contrôleurs de vitesse</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="568"/>
        <source>Button status style</source>
        <oldsource>Button status style:</oldsource>
        <translation>Style des boutons</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="502"/>
        <source>LED</source>
        <translation>LED</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="250"/>
        <source>Border</source>
        <translation>Bordure</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="229"/>
        <source>Audio triggers size</source>
        <oldsource>Audio triggers size:</oldsource>
        <translation>Taille des déclencheurs audio</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="361"/>
        <location filename="virtualconsole/vcproperties.ui" line="447"/>
        <location filename="virtualconsole/vcproperties.ui" line="585"/>
        <location filename="virtualconsole/vcproperties.ui" line="608"/>
        <source>px</source>
        <translation> px</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="575"/>
        <source>Animation size</source>
        <oldsource>RGB Matrix size</oldsource>
        <translation>Taille des animations</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="625"/>
        <source>Grand Master</source>
        <translation>Grand Master</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="631"/>
        <source>Channels</source>
        <translation>Canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="637"/>
        <source>Apply Grand Master only to Intensity channels.</source>
        <translation>Appliquer le Grand Master uniquement sur les canaux d&apos;intensité.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="640"/>
        <source>Intensity</source>
        <translation>Intensité</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="647"/>
        <source>Apply Grand Master to all channels.</source>
        <translation>Appliquer le Grand Master sur tous les canaux.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="650"/>
        <source>All channels</source>
        <translation>Tous les canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="660"/>
        <source>Values</source>
        <translation>Valeurs</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="666"/>
        <source>Make Grand Master reduce levels by a percentage.</source>
        <translation>Réduire proportionnellement les valeurs des canaux.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="669"/>
        <source>Reduce</source>
        <translation>Réduire</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="676"/>
        <source>Make Grand Master limit the maximum channel values.</source>
        <translation>Limiter les valeurs maximums des canaux.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="679"/>
        <source>Limit</source>
        <translation>Limiter</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="689"/>
        <source>External Input</source>
        <translation>Entrée externe</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="695"/>
        <source>Input Universe</source>
        <translation>Univers d&apos;entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="702"/>
        <source>Input universe for Grand Master slider.</source>
        <translation>L&apos;univers d&apos;entrée qui contrôle le Grand Master.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="712"/>
        <source>Input Channel</source>
        <translation>Canal d&apos;entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="719"/>
        <source>Input channel for Grand Master slider.</source>
        <translation>Le canal de l&apos;univers d&apos;entrée qui contrôle le Grand Master.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="742"/>
        <source>When toggled, you can move an external slider/knob to assign it to the Grand Master slider.</source>
        <translation>Lorsqu&apos;elle est activée, bougez un fader ou un bouton rotatif externe afin de l&apos;assigner au Grand Master.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="745"/>
        <source>Auto Detect</source>
        <translation>Auto-détection</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="758"/>
        <source>Choose an external input universe &amp; channel that the Grand Master slider should listen to.</source>
        <translation>Choisir l&apos;univers d&apos;entrée et son canal qui contrôlera le Grand Master.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="784"/>
        <source>Slider movement</source>
        <translation>Mouvement du fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="790"/>
        <source>Normal</source>
        <translation>Normal</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcproperties.ui" line="797"/>
        <source>Inverted</source>
        <translation>Inversé</translation>
    </message>
</context>
<context>
    <name>VCSlider</name>
    <message>
        <location filename="virtualconsole/vcslider.cpp" line="236"/>
        <source>Slider %1</source>
        <translation>Fader %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcslider.cpp" line="617"/>
        <source>Reset channels override</source>
        <translation>Réinitialiser l&apos;écrasement des canaux</translation>
    </message>
</context>
<context>
    <name>VCSliderProperties</name>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="33"/>
        <source>Slider properties</source>
        <translation>Propriétés du fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="46"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="61"/>
        <source>Name of the slider</source>
        <translation>Le nom du fader en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="179"/>
        <source>Value display style</source>
        <translation>Style d&apos;affichage de la valeur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="185"/>
        <source>Show exact DMX values</source>
        <translation>Afficher la valeur DMX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="198"/>
        <source>Show value as percentage</source>
        <translation>Afficher la valeur en pourcentage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="201"/>
        <source>Percentage</source>
        <translation>Pourcentage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="70"/>
        <source>Slider movement</source>
        <translation>Mouvement du fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="76"/>
        <source>Normal</source>
        <translation>Normal</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="86"/>
        <source>Inverted</source>
        <translation>Inversé</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="225"/>
        <source>Value range</source>
        <translation>Intervalle de valeurs</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="231"/>
        <source>Low limit</source>
        <translation>Borne inférieure</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="248"/>
        <source>High limit</source>
        <translation>Borne supérieure</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="219"/>
        <source>Level</source>
        <translation>Niveau</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="238"/>
        <source>Lowest DMX value that can be set with this slider</source>
        <translation>Valeur DMX minimale que peut définir ce fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="410"/>
        <source>Intensity</source>
        <translation>Intensité</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="431"/>
        <source>Gobo/Effect/Macro</source>
        <oldsource>Gobo/Effect</oldsource>
        <translation>Gobo/Effet/Macro</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="462"/>
        <source>Playback</source>
        <translation>Lancement</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="468"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="474"/>
        <source>Function that is attached to the slider</source>
        <translation>La fonction attachée à ce fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="484"/>
        <source>Attach a function to the slider</source>
        <translation>Attacher une fonction au fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="504"/>
        <source>Detach the current function from the slider</source>
        <translation>Détacher la fonction liée au fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="540"/>
        <source>Make the slider control a function</source>
        <translation>Utiliser le fader pour contrôler une fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="543"/>
        <source>Switch to Playback Mode</source>
        <translation>Basculer vers le mode Lancement</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="188"/>
        <source>Actual</source>
        <translation>DMX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="54"/>
        <source>Widget name</source>
        <translation>Nom du widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="109"/>
        <source>Widget appearance</source>
        <translation>Apparence du widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="115"/>
        <source>Slider</source>
        <translation>Fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="132"/>
        <source>Knob</source>
        <translation>Bouton rotatif</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="211"/>
        <source>Catch up with the external controller input value</source>
        <translation>Se mettre à jour avec la valeur d&apos;entrée du contrôleur externe</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="255"/>
        <source>Highest DMX value that can be set with this slider</source>
        <translation>Valeur DMX maximale que peut définir ce fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="284"/>
        <source>Set value range from the selected capability</source>
        <translation>Définir les bornes depuis la fonctionnalité sélectionnée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="287"/>
        <source>From capability</source>
        <translation>Depuis la sélection</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="313"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="318"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="323"/>
        <source>Range</source>
        <translation>Intervalle</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="333"/>
        <source>Select all channels</source>
        <translation>Sélectionner tous les canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="336"/>
        <source>All</source>
        <translation>Tous</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="343"/>
        <source>Unselect everything</source>
        <translation>Désélectionner tous les canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="346"/>
        <location filename="virtualconsole/vcsliderproperties.ui" line="400"/>
        <source>None</source>
        <translation>Aucun</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="353"/>
        <source>Invert selection</source>
        <translation>Inverser la sélection</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="356"/>
        <source>Invert</source>
        <translation>Inverser</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="376"/>
        <source>Choose channels by channel group</source>
        <translation>Choisir les canaux par groupe de canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="379"/>
        <source>By group...</source>
        <translation>Par groupe...</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="441"/>
        <source>Monitor the selected channels and update the slider level</source>
        <translation>Surveiller les canaux sélectionnés et mettre à jour le niveau du fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="394"/>
        <source>Click &amp;&amp; Go</source>
        <translation>Accès rapide</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="417"/>
        <source>RGB</source>
        <translation>RVB</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="424"/>
        <source>CMY</source>
        <translation>CMJ</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="451"/>
        <source>Make the slider control the level of a set of channels</source>
        <translation>Utiliser le fader pour contrôler une sélection de canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="454"/>
        <source>Switch to Level Mode</source>
        <translation>Basculer vers le mode Niveau</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="551"/>
        <source>Submaster</source>
        <translation>Submaster</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="557"/>
        <source>Slider submaster mode is active</source>
        <translation>Le mode Submaster est actif</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="580"/>
        <source>Make the slider act as a submaster</source>
        <translation>Utiliser le fader pour agir comme submaster</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.ui" line="583"/>
        <source>Switch to Submaster Mode</source>
        <translation>Basculer vers le mode Submaster</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="178"/>
        <source>Override reset control</source>
        <translation>Contrôle de réinitialisation de l&apos;écrasement</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="641"/>
        <source>Select channels by group</source>
        <translation>Sélectionner les canaux par groupe</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="642"/>
        <source>Select a channel group</source>
        <translation>Sélectionner un groupe de canaux</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsliderproperties.cpp" line="707"/>
        <source>No function</source>
        <translation>Aucune fonction</translation>
    </message>
</context>
<context>
    <name>VCSoloFrameProperties</name>
    <message>
        <location filename="virtualconsole/vcsoloframeproperties.cpp" line="40"/>
        <source>Solo Frame properties</source>
        <translation>Propriétés de cadre solo</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcsoloframeproperties.cpp" line="43"/>
        <source>Mix sliders in playback mode</source>
        <translation>Mixer les faders en mode lancement</translation>
    </message>
</context>
<context>
    <name>VCSpeedDial</name>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="81"/>
        <source>Duration</source>
        <translation>Durée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="100"/>
        <source>Divide the current time by 2</source>
        <translation>Diviser le temps actuel par 2</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="123"/>
        <source>Multiply the current time by 2</source>
        <translation>Multiplier le temps actuel par 2</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="131"/>
        <source>Reset the current factor to 1x</source>
        <translation>Réinitialiser à x1 le facteur actuel</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="144"/>
        <source>Apply</source>
        <translation>Appliquer</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddial.cpp" line="145"/>
        <source>Send the current value to the function now</source>
        <translation>Envoyer maintenant la valeur actuelle</translation>
    </message>
</context>
<context>
    <name>VCSpeedDialProperties</name>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="33"/>
        <source>Speed Dial Properties</source>
        <translation>Propriétés du contrôleur de vitesse</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="43"/>
        <source>Speed Dial Name</source>
        <translation>Nom du contrôleur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="50"/>
        <source>Title of the dial</source>
        <translation>Le nom du contrôleur de vitesse en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="61"/>
        <source>Functions</source>
        <translation>Fonctions</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="101"/>
        <source>Copy the factors of the selected function</source>
        <translation>Copier les facteurs de la fonction sélectionnée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="121"/>
        <source>Paste copied factors to all selected functions</source>
        <translation>Coller les facteurs copiés de toutes les fonctions sélectionnées</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="182"/>
        <source>Function</source>
        <translation>Fonction</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="187"/>
        <source>Fade In factor</source>
        <oldsource>Fade In *</oldsource>
        <translation>Facteur de Fondu en entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="190"/>
        <source>Multiplier applied before time is sent as Fade In Time to the function.</source>
        <translation>Multiplicateur appliqué avant que la durée soit envoyée comme fondu en ouverture à la fonction.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="195"/>
        <source>Fade Out factor</source>
        <oldsource>Fade Out *</oldsource>
        <translation>Facteur de fondu en sortie</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="198"/>
        <source>Multiplier applied before time is sent as Fade Out Time to the function.</source>
        <translation>Multiplicateur appliqué avant que la durée soit envoyée comme fondu en fermeture à la fonction.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="203"/>
        <source>Duration factor (+tap)</source>
        <oldsource>Duration * (+tap)</oldsource>
        <translation>Facteur de durée (+ tap)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="206"/>
        <source>Multiplier applied before time is sent as Duration to the function.</source>
        <translation>Multiplicateur appliqué avant que la durée soit envoyée comme durée à la fonction.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="67"/>
        <source>Add functions to be controlled</source>
        <translation>Ajouter des fonctions à contrôler</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="84"/>
        <source>Remove selected functions</source>
        <translation>Enlever les fonctions sélectionnées</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="215"/>
        <source>Input</source>
        <translation>Entrée</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="221"/>
        <source>Absolute Value</source>
        <translation>Valeur absolue</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="429"/>
        <source>Preset Name</source>
        <translation>Nom du préréglage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="233"/>
        <source>Range</source>
        <translation>Intervalle</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="246"/>
        <source>ms precision</source>
        <translation>Précision en ms</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="259"/>
        <source>Tap</source>
        <translation>Tap</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="267"/>
        <source>Apply</source>
        <translation>Appliquer</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="372"/>
        <source>Multiplier</source>
        <translation>Multiplicateur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="378"/>
        <source>Reset multiplier factor when the dial value changes</source>
        <translation>Réinitialiser le facteur quand la valeur du contrôleur change</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="408"/>
        <source>Presets</source>
        <translation>Préréglages</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="454"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="459"/>
        <source>Value</source>
        <translation>Valeur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="439"/>
        <source> Add preset</source>
        <translation> Ajouter un préréglage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="467"/>
        <source> Remove</source>
        <translation> Enlever</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="416"/>
        <source>Preset name</source>
        <translation>Nom du préréglage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="289"/>
        <source>Appearance</source>
        <translation>Apparence</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="295"/>
        <source>Show plus and minus buttons</source>
        <translation>Afficher les boutons plus et moins</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="344"/>
        <source>Show multiplier and divisor buttons</source>
        <translation>Afficher les boutons *2 et /2</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="302"/>
        <source>Show the central dial</source>
        <translation>Afficher le cadran central</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="309"/>
        <source>Show the tap button</source>
        <translation>Afficher le bouton de tap</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="351"/>
        <source>Show the apply button</source>
        <translation>Afficher le bouton appliquer</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="316"/>
        <source>Show the hours field</source>
        <translation>Afficher le champ des heures</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="323"/>
        <source>Show the minutes field</source>
        <translation>Afficher le champ des minutes</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="330"/>
        <source>Show the seconds field</source>
        <translation>Afficher le champ des secondes</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.ui" line="337"/>
        <source>Show the milliseconds field</source>
        <translation>Afficher le champ des millisecondes</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.cpp" line="115"/>
        <source>Multiply by 2 Input</source>
        <translation>Entrée du bouton de multiplication par 2</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.cpp" line="124"/>
        <source>Divide by 2 Input</source>
        <translation>Entrée du bouton de division par 2</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcspeeddialproperties.cpp" line="133"/>
        <source>Factor Reset Input</source>
        <translation>Entrée du bouton de réinitialisation du facteur</translation>
    </message>
</context>
<context>
    <name>VCWidget</name>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="144"/>
        <source>Button</source>
        <translation>Bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="145"/>
        <source>Slider</source>
        <translation>Fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="146"/>
        <source>XYPad</source>
        <translation>Pad XY</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="147"/>
        <source>Frame</source>
        <translation>Cadre</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="148"/>
        <source>Solo frame</source>
        <translation>Cadre de solos</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="149"/>
        <source>Speed dial</source>
        <translation>Contrôleur de vitesse</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="150"/>
        <source>Cue list</source>
        <translation>Séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="151"/>
        <source>Label</source>
        <translation>Étiquette</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="152"/>
        <source>Audio Triggers</source>
        <translation>Déclencheur audio</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="153"/>
        <source>Animation</source>
        <translation>Animation</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="154"/>
        <source>Clock</source>
        <translation>Horloge</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="157"/>
        <location filename="virtualconsole/vcwidget.cpp" line="159"/>
        <source>Unknown</source>
        <translation>Inconnu</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidget.cpp" line="513"/>
        <source>This widget has no properties</source>
        <translation>Ce widget n&apos;a pas de propriétés</translation>
    </message>
</context>
<context>
    <name>VCWidgetSelection</name>
    <message>
        <location filename="virtualconsole/vcwidgetselection.ui" line="33"/>
        <source>Virtual Console widget selection</source>
        <translation>Sélection d&apos;un widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidgetselection.ui" line="42"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcwidgetselection.ui" line="47"/>
        <source>Type</source>
        <translation>Type</translation>
    </message>
</context>
<context>
    <name>VCXYPadArea</name>
    <message>
        <location filename="virtualconsole/vcxypadarea.cpp" line="302"/>
        <source>Shift: fine, Ctrl:10x</source>
        <translation>Shift : précis, Ctrl : x10</translation>
    </message>
</context>
<context>
    <name>VCXYPadFixtureEditor</name>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="33"/>
        <source>XY Pad Fixture</source>
        <translation>Propriétés des axes de l&apos;appareil</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="39"/>
        <source>Horizontal X-Axis</source>
        <translation>Axe horizontal (X)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="45"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="101"/>
        <source>Minimum</source>
        <translation>Minimum</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="52"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="69"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="108"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="125"/>
        <source>%</source>
        <translation> %</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="62"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="118"/>
        <source>Maximum</source>
        <translation>Maximum</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="85"/>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="141"/>
        <source>Reverse</source>
        <translation>Inverser</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadfixtureeditor.ui" line="95"/>
        <source>Vertical Y-Axis</source>
        <translation>Axe vertical (Y)</translation>
    </message>
</context>
<context>
    <name>VCXYPadProperties</name>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="33"/>
        <source>XY Pad Properties</source>
        <translation>Propriétés du pad XY</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="53"/>
        <source>General</source>
        <translation>Général</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="102"/>
        <source>XY Pad Name</source>
        <translation>Nom du pad XY</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="109"/>
        <source>The name of this XY Pad</source>
        <translation>Le nom du pad XY en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="143"/>
        <source>Fixtures</source>
        <translation>Appareils</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="149"/>
        <source>List of fixtures that are controlled by this pad</source>
        <translation>La liste des appareils contrôlés par ce pad</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="174"/>
        <source>Fixture</source>
        <translation>Appareil</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="179"/>
        <source>X-Axis</source>
        <translation>Axe X</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="184"/>
        <source>Y-Axis</source>
        <translation>Axe Y</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="192"/>
        <source>Add fixture(s) to the pad</source>
        <translation>Ajouter un ou plusieurs appareils au pad</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="252"/>
        <source>Range Display Mode</source>
        <translation>Mode d&apos;affichage de la plage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="258"/>
        <source>Degrees</source>
        <translation>Degrés</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="268"/>
        <source>Percentage</source>
        <translation>Pourcentage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="278"/>
        <source>DMX</source>
        <translation>DMX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="302"/>
        <source>Presets</source>
        <translation>Préréglages</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="323"/>
        <source>Name</source>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="333"/>
        <source>Add position</source>
        <translation>Ajouter une position</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="344"/>
        <source>Add EFX</source>
        <translation>Ajouter un EFX</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="355"/>
        <source>Add Scene</source>
        <translation>Ajouter une scène</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="366"/>
        <source>Add Fixture Group</source>
        <translation>Ajouter un groupe d&apos;appareils</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="377"/>
        <source>Remove</source>
        <translation>Enlever</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="388"/>
        <source>Move Up</source>
        <translation>Déplacer vers le haut</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="399"/>
        <source>Move Down</source>
        <translation>Déplacer vers le bas</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="418"/>
        <source>Preset name</source>
        <translation>Nom du préréglage</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="85"/>
        <source>Pan / Horizontal Axis</source>
        <translation>Pan / Axe horizontal (X)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="98"/>
        <source>Tilt / Vertical Axis</source>
        <translation>Tilt / Axe vertical (Y)</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="116"/>
        <source>Y-Axis slider movement</source>
        <translation>Mouvement du fader suivant l&apos;axe Y</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="122"/>
        <source>Normal</source>
        <translation>Normal</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="132"/>
        <source>Inverted</source>
        <translation>Inversé</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="212"/>
        <source>Remove selected fixture(s) from the pad</source>
        <translation>Enlever le(s) appareil(s) sélectionné(s) du pad</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.ui" line="232"/>
        <source>Edit the selected fixture&apos;s axis</source>
        <translation>Éditer les axes de l&apos;appareil sélectionné</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="111"/>
        <source>Width</source>
        <translation>Largeur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="119"/>
        <source>Height</source>
        <translation>Hauteur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="370"/>
        <source>Remove fixtures</source>
        <translation>Enlever les appareils</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="371"/>
        <source>Do you want to remove the selected fixtures?</source>
        <translation>Voulez-vous enlever le(s) appareils(s) sélectionné(s)&#xa0;?</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="734"/>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="783"/>
        <source>Error</source>
        <translation>Erreur</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="735"/>
        <source>The selected Scene does not include any Pan or Tilt channel.
Please select one with such channels.</source>
        <translation>La scène sélectionnée n&apos;affecte pas de canal Pan ou Tilt.
Veuillez en choisir une qui affecte un des deux.</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="784"/>
        <source>Please select at least one fixture or head to create this type of preset!</source>
        <translation>Veuillez sélectionner au moins un appareil ou une tête pour créer ce type de préréglage&#xa0;!</translation>
    </message>
    <message>
        <location filename="virtualconsole/vcxypadproperties.cpp" line="791"/>
        <source>Fixture Group</source>
        <translation>Groupe d&apos;appareils</translation>
    </message>
</context>
<context>
    <name>VideoEditor</name>
    <message>
        <location filename="videoeditor.ui" line="33"/>
        <source>Video Editor</source>
        <translation>Éditeur vidéo</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="54"/>
        <source>Video name</source>
        <oldsource>Video name:</oldsource>
        <translation>Nom</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="162"/>
        <source>Play the video and see how it looks like</source>
        <translation>Jouer la vidéo pour avoir un aperçu</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="189"/>
        <source>File name</source>
        <oldsource>File name:</oldsource>
        <translation>Nom du fichier</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="196"/>
        <source>Duration</source>
        <oldsource>Duration:</oldsource>
        <translation>Durée</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="203"/>
        <source>Resolution</source>
        <oldsource>Resolution:</oldsource>
        <translation>Résolution</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="217"/>
        <source>Audio codec</source>
        <oldsource>Audio codec:</oldsource>
        <translation>Codec audio</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="210"/>
        <source>Video codec</source>
        <oldsource>Video codec:</oldsource>
        <translation>Codec vidéo</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="103"/>
        <source>Set an arbitrary URL for this Video</source>
        <translation>Définir une URL arbitraire pour cette vidéo</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="133"/>
        <source>Name of the function being edited</source>
        <translation>Le nom de la fonction en cours d&apos;édition</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="231"/>
        <source>Output Screen</source>
        <oldsource>Output Screen:</oldsource>
        <translation>Écran de sortie</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="238"/>
        <source>Video output</source>
        <translation>Sortie vidéo</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="244"/>
        <source>Windowed</source>
        <translation>Fenêtré</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="254"/>
        <source>Fullscreen</source>
        <translation>Plein écran</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="264"/>
        <source>Playback mode</source>
        <translation>Mode de lecture</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="270"/>
        <source>Single shot</source>
        <translation>Unique</translation>
    </message>
    <message>
        <location filename="videoeditor.ui" line="280"/>
        <source>Loop</source>
        <translation>Boucle</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="118"/>
        <source>Open Video File</source>
        <translation>Ouvrir un fichier vidéo</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="126"/>
        <source>Video Files (%1)</source>
        <translation>Fichiers vidéo (%1)</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="128"/>
        <source>All Files (*.*)</source>
        <translation>Tous les fichiers (*.*)</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="130"/>
        <source>All Files (*)</source>
        <translation>Tous les fichiers (*)</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="158"/>
        <source>Video source URL</source>
        <translation>URL de la source vidéo</translation>
    </message>
    <message>
        <location filename="videoeditor.cpp" line="159"/>
        <source>Enter a URL:</source>
        <translation>Entrer une URL :</translation>
    </message>
</context>
<context>
    <name>VideoItem</name>
    <message>
        <location filename="showmanager/videoitem.cpp" line="50"/>
        <source>Fullscreen</source>
        <translation>Plein écran</translation>
    </message>
    <message>
        <location filename="showmanager/videoitem.cpp" line="184"/>
        <source>Screen %1</source>
        <translation>Écran %1</translation>
    </message>
</context>
<context>
    <name>VirtualConsole</name>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="372"/>
        <source>Cut</source>
        <translation>Couper</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="375"/>
        <source>Copy</source>
        <translation>Copier</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="378"/>
        <source>Paste</source>
        <translation>Coller</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="382"/>
        <source>Delete</source>
        <translation>Supprimer</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="408"/>
        <location filename="virtualconsole/virtualconsole.cpp" line="422"/>
        <location filename="virtualconsole/virtualconsole.cpp" line="435"/>
        <source>Default</source>
        <translation>Défaut</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="432"/>
        <source>Font</source>
        <translation>Police</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="445"/>
        <source>Sunken</source>
        <translation>Enfoncé</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="448"/>
        <source>Raised</source>
        <translation>Relevé</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="451"/>
        <source>None</source>
        <translation>Aucune bordure</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="479"/>
        <source>&amp;Add</source>
        <translation>&amp;Ajouter</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="500"/>
        <source>&amp;Edit</source>
        <translation>&amp;Éditer</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="304"/>
        <source>New Button</source>
        <translation>Nouveau bouton</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="307"/>
        <source>New Button Matrix</source>
        <translation>Nouvelle matrice de boutons</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="310"/>
        <source>New Slider</source>
        <translation>Nouveau fader</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="316"/>
        <source>New Knob</source>
        <translation>Nouveau bouton rotatif</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="319"/>
        <source>New Speed Dial</source>
        <translation>Nouveau contrôleur de vitesse</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="322"/>
        <source>New XY pad</source>
        <translation>Nouveau pad XY</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="325"/>
        <source>New Cue list</source>
        <translation>Nouveau séquenceur</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="328"/>
        <source>New Frame</source>
        <translation>Nouveau cadre</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="331"/>
        <source>New Solo frame</source>
        <translation>Nouveau cadre de solos</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="334"/>
        <source>New Label</source>
        <translation>Nouvelle étiquette</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="337"/>
        <source>New Audio Triggers</source>
        <translation>Nouveau déclencheur audio</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="340"/>
        <source>New Clock</source>
        <translation>Nouvelle horloge</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="365"/>
        <source>Virtual Console Settings</source>
        <translation>Propriétés de la console virtuelle</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="385"/>
        <source>Widget Properties</source>
        <translation>Propriétés du widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="388"/>
        <source>Rename Widget</source>
        <translation>Renommer le widget</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="402"/>
        <source>Background Color</source>
        <translation>Couleur d&apos;arrière-plan</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="405"/>
        <source>Background Image</source>
        <translation>Image d&apos;arrière-plan</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="419"/>
        <source>Font Colour</source>
        <translation>Couleur du texte</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="462"/>
        <source>Bring to front</source>
        <translation>Ramener à l&apos;avant</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="465"/>
        <source>Send to back</source>
        <translation>Renvoyer à l&apos;arrière</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="512"/>
        <source>&amp;Background</source>
        <translation>Arrière-plan (&amp;B)</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="520"/>
        <source>&amp;Foreground</source>
        <translation>Premier plan (&amp;F)</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="527"/>
        <source>F&amp;ont</source>
        <translation>P&amp;olice</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="534"/>
        <source>F&amp;rame</source>
        <translation>Cad&amp;re</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="542"/>
        <source>Stacking &amp;order</source>
        <translation>&amp;Organiser</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1271"/>
        <source>Images</source>
        <translation>Images</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="313"/>
        <source>New Slider Matrix</source>
        <translation>Nouvelle matrice de faders</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="343"/>
        <source>New Animation</source>
        <translation>Nouvelle animation</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="893"/>
        <source>Knob %1</source>
        <translation>Bouton rotatif %1</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1162"/>
        <source>Do you wish to delete the selected widgets?</source>
        <translation>Voulez-vous supprimer les widgets sélectionnés&#xa0;?</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1163"/>
        <source>Delete widgets</source>
        <translation>Supprimer les widgets</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1216"/>
        <source>Rename widgets</source>
        <translation>Renommer les widgets</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1216"/>
        <source>Caption:</source>
        <translation>Légende :</translation>
    </message>
    <message>
        <location filename="virtualconsole/virtualconsole.cpp" line="1269"/>
        <source>Select background image</source>
        <translation>Choisir une image d&apos;arrière-plan</translation>
    </message>
</context>
</TS>
