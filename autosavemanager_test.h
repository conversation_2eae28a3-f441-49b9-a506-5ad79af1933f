#ifndef AUTOSAVEMANAGER_TEST_H
#define AUTOSAVEMANAGER_TEST_H

#include <QObject>
#include <QTest>
#include <QSignalSpy>
#include <QTemporaryDir>

class Doc;
class AutoSaveManager;

class AutoSaveManager_Test : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    void initial();
    void settings();
    void enableDisable();
    void intervalChange();
    void backupSettings();
    void autosaveRequest();
    void documentModification();
    void cleanup_old_backups();

private:
    Doc* m_doc;
    AutoSaveManager* m_autoSaveManager;
    QTemporaryDir* m_tempDir;
    QString m_testWorkspaceFile;
};

#endif // AUTOSAVEMANAGER_TEST_H
