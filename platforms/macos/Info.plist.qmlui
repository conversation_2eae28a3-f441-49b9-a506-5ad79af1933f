<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleIconFile</key>
	<string>qlcplus.icns</string>

	<key>CFBundlePackageType</key>
	<string>APPL</string>

	<key>CFBundleShortVersionString</key>
	<string>__QLC_VERSION__</string>

	<key>CFBundleVersion</key>
	<string>1</string>

	<key>CFBundleSignature</key>
	<string>????</string>

	<key>CFBundleExecutable</key>
	<string>qlcplus-qml</string>

	<key>CFBundleName</key>
	<string>Q Light Controller Plus</string>

	<key>NSPrincipalClass</key>
	<string>NSApplication</string>

	<key>NSHighResolutionCapable</key>
	<string>True</string>

	<key>NSMicrophoneUsageDescription</key>
	<string>Process incoming audio</string>

	<key>NSLocalNetworkUsageDescription</key>
	<string>Access the local network for DMX-related protocols</string>

	<key>com.apple.developer.networking.multicast</key>
	<true/>

	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeIconFile</key>
			<string>qlcplus.icns</string>
			<key>CFBundleTypeName</key>
			<string>QLC+ Workspace</string>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>qxw</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
	</array>
</dict>
</plist>
