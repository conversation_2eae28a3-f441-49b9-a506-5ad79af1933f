cmake_minimum_required(VERSION 3.16)
project(icons VERSION 1.0 LANGUAGES C CXX)

#set(CMAKE_INCLUDE_CURRENT_DIR ON)
include(../../variables.cmake)

# install macOS files for a correct bundle
install(FILES qt.conf DESTINATION ${INSTALLROOT}/${DATADIR})
install(FILES ../../resources/icons/qlcplus.icns DESTINATION ${INSTALLROOT}/${DATADIR})

if(qmlui)
    install(FILES Info.plist.qmlui DESTINATION ${INSTALLROOT}/Info.plist)
else()
    install(FILES Info.plist DESTINATION ${INSTALLROOT})
endif()
install(CODE "execute_process(COMMAND sed -i -e \"s/__QLC_VERSION__/${APPVERSION}/g\" ${INSTALLROOT}/Info.plist)")

set(APPLE_CODESIGN_ENTITLEMENTS "qlcplus.entitlements")

# Starting with arm64 macOS Apple will require ad-hoc code signatures,
# which can be generated by setting the identity to a single dash (-).
# These only include a checksum for verifying integrity, not an actual
# signature.
#if(NOT APPLE_CODESIGN_IDENTITY)
#    set(APPLE_CODESIGN_IDENTITY -)
#endif()

# install Qt library frameworks
#set(QT_FRAMEWORKS_DIR ${_qt5_root_dir}/../../Frameworks) # homebrew
set(QT_FRAMEWORKS_DIR $ENV{QTDIR}/lib)
set(QT_FRAMEWORK_NAMES
    "QtCore"
    "QtDBus"
    "QtGui"
    "QtMultimedia"
    "QtMultimediaWidgets"
    "QtNetwork"
    "QtOpenGL"
    "QtPrintSupport"
    "QtSerialPort"
    "QtSvg"
    "QtWidgets"
    "QtWebSockets"
)
if(QT_MAJOR_VERSION GREATER 5)
    list(APPEND QT_FRAMEWORK_NAMES "QtQml")
else()
    list(APPEND QT_FRAMEWORK_NAMES "QtScript")
endif()

# Loop through each framework and copy it to installation
foreach(FW_NAME IN LISTS QT_FRAMEWORK_NAMES)
    # install each framework Version file
    install(DIRECTORY ${QT_FRAMEWORKS_DIR}/${FW_NAME}.framework
            DESTINATION ${INSTALLROOT}/${LIBSDIR}
            PATTERN "Headers" EXCLUDE)
endforeach()

# install Qt plugins
set(QT_PLUGINS_DIR $ENV{QTDIR}/plugins)
# imageformats
install(FILES ${QT_PLUGINS_DIR}/imageformats/libqgif.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/imageformats)
install(FILES ${QT_PLUGINS_DIR}/imageformats/libqjpeg.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/imageformats)
install(FILES ${QT_PLUGINS_DIR}/imageformats/libqsvg.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/imageformats)
# multimedia
if(QT_MAJOR_VERSION GREATER 5)
    install(FILES ${QT_PLUGINS_DIR}/multimedia/libffmpegmediaplugin.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/multimedia)
    install(FILES ${QT_PLUGINS_DIR}/multimedia/libffmpegmediaplugin.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/multimedia)
else()
    install(FILES ${QT_PLUGINS_DIR}/mediaservice/libqavfmediaplayer.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/mediaservice)
    install(FILES ${QT_PLUGINS_DIR}/mediaservice/libqtmedia_audioengine.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/mediaservice)
endif()
# platforms
install(FILES ${QT_PLUGINS_DIR}/platforms/libqcocoa.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/platforms)
# styles
install(FILES ${QT_PLUGINS_DIR}/styles/libqmacstyle.dylib DESTINATION ${INSTALLROOT}/${PLUGINDIR}/styles)

# install support libraries
set(FFTW_LIBNAME "libfftw3.3.dylib")
set(OGG_LIBNAME "libogg.0.dylib")
set(FLAC_LIBNAME "libFLAC.12.dylib")
set(OPUS_LIBNAME "libopus.0.dylib")
set(MPG123_LIBNAME "libmpg123.0.dylib")
set(LAME_LIBNAME "libmp3lame.0.dylib")
set(VORBIS_LIBNAME "libvorbis.0.dylib")
set(VORBISENC_LIBNAME "libvorbisenc.2.dylib")

install(FILES ${FFTW3_LIBDIR}/${FFTW_LIBNAME} DESTINATION ${INSTALLROOT}/${LIBSDIR})
install(FILES ${LIBFTDI1_libftdi1_LIBDIR}/libftdi1.2.dylib DESTINATION ${INSTALLROOT}/${LIBSDIR})
install(FILES ${LIBFTDI1_libftdi1_LIBDIR}/libftdi1.2.5.0.dylib DESTINATION ${INSTALLROOT}/${LIBSDIR})
install(FILES ${LIBUSB1_LIBDIR}/libusb-1.0.0.dylib DESTINATION ${INSTALLROOT}/${LIBSDIR})
#install(FILES ${MAD_LIBDIR}/libmad.0.dylib DESTINATION ${INSTALLROOT}/${LIBSDIR})
install(FILES ${SNDFILE_LIBDIR}/libsndfile.1.dylib DESTINATION ${INSTALLROOT}/${LIBSDIR})
install(FILES ${SNDFILE_LIBDIR}/libsndfile.1.0.37.dylib DESTINATION ${INSTALLROOT}/${LIBSDIR})

