.TH qlcplus-fixtureeditor 1 "Jan 2016" "QLC+ 4.10.3 GIT"
.\" --------------------------------------------------------------------
.SH NAME
.\" --------------------------------------------------------------------
qlcplus-fixtureeditor \- Fixture definition editor for QLC+
.\" --------------------------------------------------------------------
.\" Legal Terms
.\" --------------------------------------------------------------------
.
.\" License
.de co
.B Copyright
(c) <PERSON><PERSON><PERSON>, <PERSON><PERSON>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0.txt

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
..
.
.\" --------------------------------------------------------------------
.SH SYNOPSIS
.\" --------------------------------------------------------------------
.B qlcplus-fixtureeditor
.\" --------------------------------------------------------------------
.SH DESCRIPTION
.\" --------------------------------------------------------------------
.B Fixture Definition Editor 
is a separate application for creating and modifying fixture definitions used by QLC+
The definitions tell QLC+ (and users) important details about fixtures, such as which
channel is used for pan movement, what value in which channel changes the beam color green,
how the fixture is reset etc... 
.
.
.SH OPTIONS
None.
.
.
.\" --------------------------------------------------------------------
.SH HOMEPAGE
.\" --------------------------------------------------------------------
.UR http://qlcplus.org/
.UE
.
.
.\" --------------------------------------------------------------------
.SH SUPPORT
.
.
.\" --------------------------------------------------------------------
Please report bugs and ask at 
.UR http://www.qlcplus.org/forum/
QLC+ Forum
.UE
.
.
.\" --------------------------------------------------------------------
.SH AUTHORS
.\" --------------------------------------------------------------------
.co
.\" --------------------------------------------------------------------
.SH SEE ALSO
.\" --------------------------------------------------------------------
.BR qlcplus(1)
.
